package com.letu.solutions.dubbo.earnify.util;

import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.core.model.ExtendData;

/**
 * 身份信息转换工具
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public class ExtendCovert {
    public static com.letu.solutions.share.model.model.ExtendData covert(ExtendData extendData) {
        return BeanUtil.copyProperties(extendData, com.letu.solutions.share.model.model.ExtendData.class);
    }
}
