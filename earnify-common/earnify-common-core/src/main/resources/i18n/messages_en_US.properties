\u9A8C\u8BC1\u7801\u6709\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u9A8C\u8BC1 = Verification code is incorrect, please re-verify
\u8D26\u6237\u5DF2\u9501\u5B9A\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458 = Account locked, please contact the administrator
\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF = Username or password error
\u9A8C\u8BC1\u7801\u9519\u8BEF = Verification code error

# \u4EFB\u52A1\u76F8\u5173\u9519\u8BEF\u4FE1\u606F
\u4EFB\u52A1\u4E0D\u5B58\u5728 = Task does not exist
\u4EFB\u52A1\u5DF2\u88AB\u9886\u53D6 = Task has already been claimed
\u4EFB\u52A1\u9886\u53D6\u6570\u91CF\u5DF2\u8FBE\u4E0A\u9650 = Task claim limit has been reached
\u7528\u6237\u4EFB\u52A1\u4E0D\u5B58\u5728 = User task does not exist
\u4EFB\u52A1\u6B65\u9AA4\u4E0D\u5B58\u5728 = Task step does not exist
\u4EFB\u52A1\u5DF2\u5B8C\u6210 = Task has already been completed
\u4EFB\u52A1\u5DF2\u63D0\u4EA4 = Task has already been submitted
\u4EFB\u52A1\u5DF2\u7533\u8BC9 = Task has already been appealed
\u4EFB\u52A1\u672A\u5B8C\u6210\uFF0C\u65E0\u6CD5\u63D0\u4EA4 = Task is not completed and cannot be submitted
\u4EFB\u52A1\u6B65\u9AA4\u672A\u5168\u90E8\u4FDD\u5B58\uFF0C\u65E0\u6CD5\u63D0\u4EA4 = Not all task steps are saved and cannot be submitted
\u4EFB\u52A1\u5DF2\u8FC7\u671F = Task has expired
\u4EFB\u52A1\u72B6\u6001\u4E0D\u6B63\u786E\uFF0C\u65E0\u6CD5\u64CD\u4F5C = Task status is incorrect and cannot be operated
\u4EFB\u52A1ID\u4E0D\u80FD\u4E3A\u7A7A = Task ID cannot be empty
\u7528\u6237\u4EFB\u52A1ID\u4E0D\u80FD\u4E3A\u7A7A = User task ID cannot be empty
\u4EFB\u52A1\u6B65\u9AA4ID\u4E0D\u80FD\u4E3A\u7A7A = Task step ID cannot be empty
\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A = User ID cannot be empty
\u4EFB\u52A1\u64CD\u4F5C\u5931\u8D25 = Task operation failed
\u4EFB\u52A1\u9886\u53D6\u5931\u8D25 = Task claim failed
\u4EFB\u52A1\u63D0\u4EA4\u5931\u8D25 = Task submission failed
\u4EFB\u52A1\u7533\u8BC9\u5931\u8D25 = Task appeal failed
\u4EFB\u52A1\u6B65\u9AA4\u4FDD\u5B58\u5931\u8D25 = Task step save failed
\u4EFB\u52A1\u8BE6\u60C5\u4E0D\u5B58\u5728 = Task details do not exist
\u4EFB\u52A1\u53D1\u5E03\u8005\u4FE1\u606F\u4E0D\u5B58\u5728 = Task publisher information does not exist

# \u65B0\u589E\u7684\u56FD\u9645\u5316\u6D88\u606F
\u4EFB\u52A1\u9886\u53D6\u6210\u529F = Task claimed successfully
\u7533\u8BC9\u5931\u8D25\uFF0C\u4EFB\u52A1\u4E0D\u5B58\u5728\u6216\u65E0\u6743\u9650 = Appeal failed, task does not exist or no permission
\u53EA\u6709\u5DF2\u5B8C\u6210\u7684\u4EFB\u52A1\u624D\u80FD\u7533\u8BC9 =Only completed tasks can be appealed
\u7533\u8BC9\u6210\u529F = Appeal successful
\u7533\u8BC9\u5931\u8D25 = Appeal failed
\u4EFB\u52A1\u4E0D\u5B58\u5728\u6216\u65E0\u6743\u9650 = Task does not exist or no permission
\u8BF7\u5148\u4FDD\u5B58\u6240\u6709\u6B65\u9AA4\u540E\u518D\u63D0\u4EA4 = Please save all steps before submitting
\u63D0\u4EA4\u6210\u529F = Submit successful
\u53C2\u6570\u4E0D\u5B8C\u6574 = Parameters incomplete
\u4EFB\u52A1\u4E0D\u5728\u8FDB\u884C\u4E2D\uFF0C\u65E0\u9700\u518D\u64CD\u4F5C\u6B65\u9AA4 = Task is not in progress, no need to operate steps
\u6B65\u9AA4\u4E0D\u5B58\u5728\u6216\u65E0\u6743\u9650 = Step does not exist or no permission
\u6B65\u9AA4\u5DF2\u5B8C\u6210 = Step completed
\u6B65\u9AA4\u5DF2\u5B8C\u6210\uFF0C\u65E0\u9700\u91CD\u590D\u64CD\u4F5C = Step already completed, no need to repeat
\u6B65\u9AA4\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u63D0\u4F9B\u6B65\u9AA4\u6392\u5E8F\u6216\u6B65\u9AA4ID = Step identifier cannot be empty, please provide step order or step ID

# \u7528\u6237\u76F8\u5173\u9519\u8BEF\u4FE1\u606F
\u7528\u6237\u4E0D\u5B58\u5728 = User does not exist
\u7528\u6237\u5DF2\u88AB\u7981\u7528 = User has been disabled
\u7528\u6237\u4FE1\u606F\u66F4\u65B0\u5931\u8D25 = User information update failed
\u7528\u6237\u5934\u50CF\u66F4\u65B0\u5931\u8D25 = User avatar update failed
\u7528\u6237\u6635\u79F0\u66F4\u65B0\u5931\u8D25 = User nickname update failed
\u7528\u6237\u57FA\u7840\u4FE1\u606F\u66F4\u65B0\u5931\u8D25 = User basic information update failed
\u7528\u6237\u5934\u50CF\u66F4\u65B0\u6210\u529F = User avatar updated successfully
\u7528\u6237\u6635\u79F0\u66F4\u65B0\u6210\u529F = User nickname updated successfully
\u7528\u6237\u57FA\u7840\u4FE1\u606F\u66F4\u65B0\u6210\u529F = User basic information updated successfully
\u6635\u79F0\u5305\u542B\u8FDD\u89C4\u8BCD\u6C47\uFF0C\u8BF7\u91CD\u65B0\u547D\u540D = Nickname contains violation words, please rename
\u5934\u50CF\u5730\u5740\u4E0D\u5408\u6CD5 = Avatar URL is invalid
\u5934\u50CF\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A = Avatar URL cannot be empty
\u7528\u6237\u6635\u79F0\u4E0D\u80FD\u4E3A\u7A7A = User nickname cannot be empty
\u7528\u6237\u6635\u79F0\u4E0D\u5408\u6CD5 = User nickname is invalid

# \u90AE\u4EF6\u76F8\u5173\u9519\u8BEF\u4FE1\u606F
\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A = Email cannot be empty
\u90AE\u7BB1\u683C\u5F0F\u4E0D\u5408\u6CD5 = Email format is invalid
\u90AE\u7BB1\u9A8C\u8BC1\u7801\u4E0D\u80FD\u4E3A\u7A7A = Email verification code cannot be empty
\u90AE\u7BB1\u9A8C\u8BC1\u7801\u683C\u5F0F\u4E0D\u5408\u6CD5 = Email verification code format is invalid
\u90AE\u7BB1\u9A8C\u8BC1\u7801\u5DF2\u8FC7\u671F = Email verification code has expired
\u90AE\u7BB1\u9A8C\u8BC1\u7801\u9519\u8BEF = Email verification code is incorrect
\u90AE\u4EF6\u53D1\u9001\u5931\u8D25 = Email sending failed
\u90AE\u4EF6\u53D1\u9001\u6210\u529F = Email sent successfully
\u90AE\u4EF6\u914D\u7F6E\u4E0D\u5B8C\u6574 = Email configuration is incomplete
\u90AE\u4EF6\u53D1\u9001\u9891\u7387\u8FC7\u9AD8\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5 = Email sending frequency is too high, please try again later
\u90AE\u7BB1\u8D26\u6237\u5DF2\u88AB\u9501\u5B9A\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5 = Email account has been locked, please try again later

# \u9080\u8BF7\u76F8\u5173\u9519\u8BEF\u4FE1\u606F
\u88AB\u9080\u8BF7\u4EBA\u5DF2\u6CE8\u518C = The invitee has already registered
\u5DF2\u9080\u8BF7\u8FC7\u8BE5\u624B\u673A\u53F7 = Already invited this phone number
\u9080\u8BF7\u521B\u5EFA\u5931\u8D25 = Failed to create invitation
\u9080\u8BF7\u521B\u5EFA\u6210\u529F = Invitation created successfully
\u9080\u8BF7\u7801\u4E0D\u5B58\u5728\u6216\u65E0\u6548 = Invite code does not exist or is invalid
\u9080\u8BF7\u7801\u5BF9\u5E94\u7684\u7528\u6237\u5DF2\u88AB\u7981\u7528 = The user corresponding to the invite code has been disabled
\u4E0D\u80FD\u9080\u8BF7\u81EA\u5DF2 = Cannot invite yourself
\u60A8\u5DF2\u7ECF\u88AB\u9080\u8BF7\u8FC7\uFF0C\u4E0D\u80FD\u518D\u6B21\u4F7F\u7528\u9080\u8BF7\u7801 = You have already been invited and cannot use the invite code again
\u83B7\u53D6\u9080\u8BF7\u7801\u5931\u8D25 = Failed to get invite code

# \u793E\u4EA4\u5A92\u4F53\u7ED1\u5B9A\u76F8\u5173\u9519\u8BEF\u4FE1\u606F
\u8BE5\u793E\u4EA4\u5A92\u4F53\u5DF2\u7ED1\u5B9A = This social media has already been bound
\u793E\u4EA4\u5A92\u4F53\u7ED1\u5B9A\u6210\u529F = Social media bound successfully
\u793E\u4EA4\u5A92\u4F53\u7ED1\u5B9A\u5931\u8D25 = Social media binding failed

# \u94B1\u5305\u7ED1\u5B9A\u76F8\u5173\u9519\u8BEF\u4FE1\u606F
\u8BE5\u94B1\u5305\u5730\u5740\u5DF2\u7ED1\u5B9A = This wallet address has already been bound
\u94B1\u5305\u7ED1\u5B9A\u6210\u529F = Wallet bound successfully
\u94B1\u5305\u7ED1\u5B9A\u5931\u8D25 = Wallet binding failed
\u94B1\u5305\u4E0D\u5B58\u5728 = Wallet does not exist
\u8BBE\u7F6E\u9ED8\u8BA4\u94B1\u5305\u5931\u8D25 = Failed to set default wallet
\u5220\u9664\u94B1\u5305\u5931\u8D25 = Failed to delete wallet

# Phone number binding related error messages
手机号已被绑定 = Phone number has already been bound
手机号绑定成功 = Phone number bound successfully
手机号绑定失败 = Phone number binding failed
手机号格式不正确 = Phone number format is incorrect
验证码发送成功 = Verification code sent successfully
验证码发送失败 = Verification code sending failed
验证码错误 = Verification code error
验证码已过期 = Verification code has expired
验证码不能为空 = Verification code cannot be empty
手机号不能为空 = Phone number cannot be empty
已绑定手机号，不可切换 = Phone number already bound, cannot switch
用户已注册或验证码有误 = User already registered or verification code is incorrect
