package com.letu.solutions.core.enums.mq;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * mq枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MqEnum {
//    MESSAGE_NOTICE(1, "message", "通知发送", "message_notice", "GID_MESSAGE_NOTICE", 5),
//    MESSAGE_NOTICE_DELAY(2, "message-delay", "延时通知发送", "message_notice_delay", "GID_MESSAGE_NOTICE_DELAY", 5),
//    MBOX_BATCH_TASK(4, "business", "批量任务执行消息", "mbox_batch_task", "GID_MBOX_BATCH_TASK", 3),
    ORDER_CANCEL_DELAY(1, "business-delay", "延迟队列", "order_cancel_delay", "GID_NOVEL_ORDER_CANCEL_DELAY", 3),
    ;
    private Integer id;
    private String topic;
    private String message;
    private String tag;
    private String groupId;
    private Integer threadNum;
}
