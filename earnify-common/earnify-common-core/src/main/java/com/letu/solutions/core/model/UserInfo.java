package com.letu.solutions.core.model;

import com.letu.solutions.share.model.enums.SexEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 请求头实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 2057290528059080932L;

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 头像链接
     */
    private String imgUrl;

    /**
     * 性别
     */
    private SexEnum sex;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * openId
     */
    private String openId;
}
