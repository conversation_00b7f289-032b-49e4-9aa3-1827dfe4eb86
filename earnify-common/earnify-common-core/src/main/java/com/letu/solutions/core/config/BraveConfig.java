package com.letu.solutions.core.config;

import brave.Tracing;
import brave.context.slf4j.MDCScopeDecorator;
import brave.propagation.CurrentTraceContext;
import brave.propagation.ThreadLocalCurrentTraceContext;
import brave.rpc.RpcTracing;
import brave.sampler.Sampler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@Configuration
@RequiredArgsConstructor
public class BraveConfig {
    @Bean
    public CurrentTraceContext currentTraceContext() {
        return ThreadLocalCurrentTraceContext.newBuilder()
                .addScopeDecorator(MDCScopeDecorator.newBuilder().build()) // optional
                .build();
    }

    @Bean
    public Tracing tracing(CurrentTraceContext ctx, Environment env) {
        return Tracing.newBuilder()
                .localServiceName(env.getProperty("spring.application.name"))
                .currentTraceContext(ctx)
                .sampler(Sampler.ALWAYS_SAMPLE)
                .build();
    }

    @Bean(name = "rpcTracing")
    public RpcTracing rpcTracing(Tracing tracing) {
        return RpcTracing.newBuilder(tracing).build();
    }
}
