package com.letu.solutions.core.model;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ModelAttribute;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/24
 **/
@Slf4j
public class BasicController {

    private static ThreadLocal<HttpServletRequest> currentRequest = new ThreadLocal<>();
    private static ThreadLocal<HttpServletResponse> currentResponse = new ThreadLocal<>();

    /**
     * 封装参数 (每次执行方法前都会先调用该方法封装)
     *
     * @param request  request
     * @param response response
     */
    @ModelAttribute
    protected void getReqAndRes(HttpServletRequest request, HttpServletResponse response) {
        currentRequest.set(request);
        currentResponse.set(response);
    }

    protected HttpServletRequest getRequest() {
        return currentRequest.get();
    }

    protected HttpServletResponse getResponse() {
        return currentResponse.get();
    }

    /**
     * 获取分页对象
     *
     * @return 分页
     */
    protected <T> Page<T> getPage() {
        HttpServletRequest request = getRequest();
        String currentPage = request.getParameter("current");
        String pageSize = request.getParameter("size");
        Integer page = 1;
        Integer size = 10;

        if (!StringUtils.isEmpty(currentPage)) {
            page = Integer.parseInt(currentPage);
        }

        if (!StringUtils.isEmpty(pageSize)) {
            size = Integer.parseInt(pageSize);
        }

        return new Page<T>(page, size);
    }

    /**
     * 获取分页对象
     *
     * @return 分页
     */
    protected <T> Page<T> getPage(Object request) {
        Integer page = 1;
        Integer size = 10;
        if (null != request) {
            Object pageNumberObj = ReflectUtil.getFieldValue(request, "current");
            Object pageSizeObj = ReflectUtil.getFieldValue(request, "size");
            if (null != pageNumberObj) {
                page = Integer.parseInt(pageNumberObj.toString());
            }
            if (null != pageSizeObj) {
                size = Integer.parseInt(pageSizeObj.toString());
            }
        }
        return new Page<T>(page, size);
    }

    /**
     * 获取分页对象
     *
     * @return 分页
     */
    protected <T> Page<T> getPage(String body) {
        Integer page = 1;
        Integer size = 10;
        if (StrUtil.isNotEmpty(body)) {
            try {
                Map<String, Object> parse = (Map<String, Object>) JSON.parse(body);
                page = (Integer) parse.get("current");
                size = (Integer) parse.get("size");
            } catch (Exception e) {

            }
        }
        return new Page<T>(page, size);
    }
}
