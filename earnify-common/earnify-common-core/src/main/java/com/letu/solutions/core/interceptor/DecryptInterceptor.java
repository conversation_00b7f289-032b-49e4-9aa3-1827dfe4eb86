//package com.letu.solutions.core.interceptor;
//
//
//import cn.hutool.core.annotation.AnnotationUtil;
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.ReflectUtil;
//import com.letu.solutions.core.annotation.SensitiveData;
//import com.letu.solutions.core.annotation.SensitiveFiled;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.apache.ibatis.executor.resultset.ResultSetHandler;
//import org.apache.ibatis.plugin.*;
//import org.springframework.stereotype.Component;
//
//import java.lang.reflect.Field;
//import java.sql.Statement;
//import java.util.Collection;
//import java.util.Properties;
//
/// **
// * <AUTHOR>
// * @create 2023-04-13 11:56
// */
//
//@Slf4j
//@Component
///**
// *这里是对找出来的字符串结果集进行解密所以是ResultSetHandler
// *args是指定预编译语句
// */
//@Intercepts({
//        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
//})
//public class DecryptInterceptor implements Interceptor {
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        //取出查询的结果
//        Object resultObject = invocation.proceed();
//        if (ObjectUtil.isNull(resultObject)) {
//            return null;
//        }
//        //基于selectList
//        if (resultObject instanceof Collection<?>) {
//            Collection<?> resultList = (Collection<?>) resultObject;
//            if (!CollectionUtil.isEmpty(resultList) && needToDecrypt(CollectionUtil.getFirst(resultList))) {
//                for (Object result : resultList) {
//                    //逐一解密
//                    decrypt(result);
//                }
//            }
//            //基于selectOne
//        } else {
//            decrypt(resultObject);
//        }
//        return resultObject;
//    }
//
//    private <T> T decrypt(T o) {
//        if (needToDecrypt(o)) {
//            //取出当前类的所有字段，传入加密方法
//            Field[] declaredFields = o.getClass().getDeclaredFields();
//            for (Field aesField : declaredFields) {
//                try {
//                    //取出所有被EncryptDecryptFiled注解的字段
//                    SensitiveFiled filed = aesField.getAnnotation(SensitiveFiled.class);
//                    if (!ObjectUtil.isNull(filed) && aesField.getType().equals(String.class)) {
//                        String fieldValue = (String) ReflectUtil.getFieldValue(o, aesField);
//                        if (StringUtils.isNotEmpty(fieldValue)) {
//                            ReflectUtil.setFieldValue(o, aesField,
//                                    EncryptUtil.decrypt(fieldValue));
//                        }
//                    }
//                } catch (Exception e) {
//                    log.error("反射获取加密字段异常", e);
//                }
//            }
//        }
//        return o;
//    }
//
//    /**
//     * 对单个结果集判空的一个方法
//     *
//     * @param object
//     * @return
//     */
//
//    private boolean needToDecrypt(Object object) {
//        Class<?> objectClass = object.getClass();
//        SensitiveData sensitiveData = AnnotationUtil.getAnnotation(objectClass, SensitiveData.class);
//        return ObjectUtil.isNotNull(sensitiveData);
//    }
//
//    /**
//     * 将此过滤器加入到过滤器链当中
//     *
//     * @param target
//     * @return
//     */
//
//    @Override
//    public Object plugin(Object target) {
//        return Plugin.wrap(target, this);
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//
//    }
//}
