package com.letu.solutions.core.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.springframework.cloud.client.ServiceInstance;

import java.util.List;
import java.util.Properties;

public class NacosServiceDiscoveryServer extends NacosServiceDiscovery {
    private NamingService serverNamingService;

    public NacosServiceDiscoveryServer(NacosDiscoveryProperties discoveryProperties, NacosServiceManager nacosServiceManager) {
        super(discoveryProperties, nacosServiceManager);
        this.discoveryProperties = discoveryProperties;
        this.nacosServiceManager = nacosServiceManager;
    }

    private NacosDiscoveryProperties discoveryProperties;
    private NacosServiceManager nacosServiceManager;

    public List<ServiceInstance> getInstances(String serviceId) throws NacosException {
        String group = this.discoveryProperties.getGroup();
        List<Instance> instances = this.namingService().selectInstances(serviceId, group, true);
        if (CollUtil.isEmpty(instances)) {
            // 如果同分组下找不到服务,那么就从默认分组下找服务
            instances = buildNamingService(discoveryProperties.getNacosProperties()).selectInstances(serviceId, group, true);
        }
        return hostToServiceInstanceList(instances, serviceId);
    }

    private NamingService namingService() {
        return this.nacosServiceManager.getNamingService();
    }

    private NamingService buildNamingService(Properties properties) throws NacosException {
        if (null == serverNamingService) {
            Properties clone = ObjectUtil.clone(properties);
            clone.put("namespace", "server");
            this.serverNamingService = NacosFactory.createNamingService(clone);
        }
        return this.serverNamingService;
    }
}