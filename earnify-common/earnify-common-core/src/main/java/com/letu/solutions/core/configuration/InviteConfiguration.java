package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 邀请相关配置
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@RefreshScope
@Configuration
@Data
public class InviteConfiguration {

    /**
     * 邀请链接URL
     */
    @Value(value = "${invite.url}")
    private String url;
} 