package com.letu.solutions.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台枚举
 *
 * <AUTHOR>
 * @date ：Created in 2020/9/28 13:51
 * @modified By：
 */
@AllArgsConstructor
@Getter
public enum OsTypeEnum {
    h5("h5", 0),
    app("app", 1),
    dy_mini("dy_mini", 2),
    ks_mini("ks_mini", 3),
    wx_mini("wx_mini", 4),
    web("web", 5),
    ;

    private String desc;
    private Integer code;
}
