package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "login")
@RefreshScope
@Configuration
@Data
public class WhiteConfiguration {
    private List<String> whitePhone;
    private List<String> blackPhone;
    private List<String> whiteIps;
    private List<Long> whiteIds;
    private List<String> bestIds;
    private List<String> rankExclusionUserIds;
    private List<String> noticeUrl;
    private List<Long> robotIds;
    private boolean lightOpen = true;
    private boolean loginCheck;
    private List<String> whiteMail;
}
