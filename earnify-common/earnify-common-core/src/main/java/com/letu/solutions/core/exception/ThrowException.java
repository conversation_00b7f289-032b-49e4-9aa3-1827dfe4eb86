package com.letu.solutions.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主动抛出异常
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ThrowException extends RuntimeException {

    private Integer code = -1;

    public ThrowException(String message) {
        super(message);
    }

    public ThrowException(Integer code, String message) {
        super(message);
        this.code = code;
    }
}
