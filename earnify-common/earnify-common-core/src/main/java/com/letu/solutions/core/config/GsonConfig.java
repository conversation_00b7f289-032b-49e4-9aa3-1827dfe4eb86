package com.letu.solutions.core.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.TimeZone;

/**
 * @description
 * <AUTHOR>
 * @createTime 2025/6/5 17:24
 */
@Configuration
public class GsonConfig {

    @Bean
    public Gson gson() {
        return new GsonBuilder()
                .registerTypeAdapter(TimeZone.class, new TimeZoneAdapter())
                .create();
    }
}
