package com.letu.solutions.core.enums.mq;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/8/26
 */
@AllArgsConstructor
@Getter
public enum RecordMqEnum {
    COUNT_WEB_PAGE(1, "count", "渠道页面统计", "count_web_page", "GID_COUNT_WEB_PAGE"),

    ;
    private Integer id;
    private String topic;
    private String message;
    private String tag;
    private String groupId;
}
