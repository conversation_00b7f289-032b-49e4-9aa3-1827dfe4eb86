package com.letu.solutions.core.utils;



import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: TagUtils.java, v 0.1 2025/3/21 14:34 lai_kd Exp $$
 */
public class TagUtils {

    // TODO: 待实现
    public static volatile  String maxBlockCode="000001";
    public static String nextTagCode() {
        // 查询数据库目前最大的code
        long longMaxBlockCode = Long.parseLong(maxBlockCode.substring(maxBlockCode.indexOf("_") + 1));
        maxBlockCode = String.format("%06d", longMaxBlockCode + 1);
        return maxBlockCode;
    }

    public static void main(String[] args) {
        Map<String, String> getenv = System.getenv();
        System.out.println("getenv = " + getenv);
    }

}
