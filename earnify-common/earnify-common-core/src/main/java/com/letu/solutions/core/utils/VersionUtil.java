package com.letu.solutions.core.utils;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;

/**
 * 版本工具
 *
 * <AUTHOR>
 */
public class VersionUtil {
    /**
     * 获取版本号数字
     *
     * @param version 只支持3段 1.0.0
     * @return
     */
    public static Integer versionNum(String version) {
        if (StrUtil.isEmpty(version)) {
            version = "1.0.0";
        }
        String[] split = version.split("\\.");
        StringBuilder versionNumStr = new StringBuilder();
        for (int i = 0; i < split.length; i++) {
            versionNumStr.append(StrUtil.padAfter(split[i], 2, "0"));
        }
        return Integer.parseInt(versionNumStr.toString());
    }

    /**
     * 转换成标准的版本号
     *
     * @param version 版本号 1140
     * @return 转换后的版本号 1.4.0
     */
    public static String transVersion(Integer version) {
        if (version == null) {
            return null;
        }
        String versionStr = Integer.toString(version);
        char[] chars = versionStr.toCharArray();
        StrBuilder builder = new StrBuilder();
        for (int i = 0; i < chars.length; i++) {
            builder.append(chars[i]);
            if (i != chars.length - 1) {
                builder.append(".");
            }
        }
        return builder.toString();
    }

    /**
     * 转换成标准的版本号
     *
     * @param version 版本号 1140
     * @return 转换后的版本号 1.4.0
     */
    public static String transVersion(String version) {
        if (version == null) {
            return null;
        }
        char[] chars = version.toCharArray();
        StrBuilder builder = new StrBuilder();
        for (int i = 0; i < chars.length; i++) {
            builder.append(chars[i]);
            if (i != chars.length - 1) {
                builder.append(".");
            }
        }
        return builder.toString();
    }
}
