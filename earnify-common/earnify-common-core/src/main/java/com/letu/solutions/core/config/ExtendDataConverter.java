package com.letu.solutions.core.config;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson2.JSONObject;
import com.letu.solutions.core.model.ExtendData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.GenericConversionService;
import org.springframework.web.bind.support.ConfigurableWebBindingInitializer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * controller请求头自动转换（用户信息extendData）
 */
@Configuration
@Slf4j
public class ExtendDataConverter implements Converter<String, ExtendData> {
    @Autowired
    private RequestMappingHandlerAdapter requestMappingHandlerAdapter;

    @Override
    public ExtendData convert(String s) {
        return JSONObject.parseObject(URLUtil.decode(s), ExtendData.class);
    }

    /**
     * 增加字符串转换为List集合
     */
    @PostConstruct
    public void addConversionConfig() {
        ConfigurableWebBindingInitializer initializer = (ConfigurableWebBindingInitializer) requestMappingHandlerAdapter.getWebBindingInitializer();
        if (initializer.getConversionService() != null) {
            GenericConversionService genericConversionService = (GenericConversionService) initializer.getConversionService();
            //添加字符串转换为list集合的转换机器
            genericConversionService.addConverter(new ExtendDataConverter());

        }
    }


}
