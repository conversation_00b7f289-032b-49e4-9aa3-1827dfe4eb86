package com.letu.solutions.core.annotation;

import com.letu.solutions.core.enums.BusinessTypeEnum;

import java.lang.annotation.*;

/**
 * 防止重复请求注解
 * <p>
 * 在指定时间内发起的多次请求的uri、请求参数相同,即视为重复提交;
 * 处理过程中如果出现异常,不会影响主流程的执行
 *
 * <AUTHOR>
 * @date 2020-11-15
 */
@Inherited
@Target(ElementType.METHOD)
@Retention(value = RetentionPolicy.RUNTIME)
public @interface Server {

    /**
     * 缓存前缀
     */
    BusinessTypeEnum[] prefix() default {BusinessTypeEnum.all};

    boolean freeCheck() default false;
}