package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 消息服务消费者
 *
 * <AUTHOR>
 * @date 2020/6/3
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rocketmq")
public class RocketMqConfig {
    private String accessKey;
    private String secretKey;
    private String nameSrvAddr;
    private String key;
    private String topicKey;
}
