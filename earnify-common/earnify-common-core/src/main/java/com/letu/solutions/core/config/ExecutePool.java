package com.letu.solutions.core.config;
import brave.Tracing;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.threads.VirtualThreadExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.Resource;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
@EnableAsync
@Slf4j
public class ExecutePool {

    @Resource
    private Tracing tracing;

    @Bean(name = "asyncPool")
    @Primary
    public Executor setAsyncPool() {
        VirtualThreadExecutor executor = new VirtualThreadExecutor("async-service-asyncPool");
        return new TraceableExecutor(tracing.currentTraceContext(), executor);
    }

    @Bean(name = "selfAsyncService")
    @Primary
    public ExecutorService setSelfAsyncService() {
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

        return new TraceableExecutorService(tracing.currentTraceContext(), executor);
    }
}