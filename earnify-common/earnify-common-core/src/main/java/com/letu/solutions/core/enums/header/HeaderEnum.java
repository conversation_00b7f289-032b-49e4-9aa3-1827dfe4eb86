package com.letu.solutions.core.enums.header;

/**
 * <AUTHOR>
 * 请求头枚举
 */
public interface HeaderEnum {
    /**
     * 设备 仅支持[android,ios,web] 3种值
     */
    String os = "os";
    /**
     * 平台[app;h5]
     */
    String osType = "osType";
    /**
     * 业务类型 [admin,customer]
     */
    String businessType = "businessType";
    /**
     * 登录信息标识
     */
    String token = "token";
    /**
     * 登录信息标识
     */
    String sign = "sign";
    /**
     * 渠道id
     */
    String codeChannel = "codeChannel";
    /**
     * 落地页id app没有不传
     */
    String pageId = "pageId";
    /**
     * 设备唯一id
     */
    String deviceId = "deviceId";
    /**
     * 渠道包类型 web不传 “huawei”、”xiaomi” ……
     */
    String platform = "platform";
    /**
     * app版本号（web没有不传）
     */
    String version = "version";
    /**
     * 请求时间戳
     */
    String timestamps = "timestamps";
    /**
     * 浏览器自带请求头 origin
     */
    String origin = "origin";
    /**
     * 浏览器自带请求头 cookie
     */
    String cookie = "cookie";
    /**
     * 浏览器自带请求头 User-Agent
     */
    String userAgent = "User-Agent";
    /**
     * mac
     */
    String mac = "mac";
    /**
     * 浏览器信息
     */
    String browser = "browser";
    /**
     * 手机厂商
     */
    String phoneBrand = "phoneBrand";
    /**
     * 手机型号
     */
    String phoneModel = "phoneModel";
    /**
     * 系统版本
     */
    String androidOsVersion = "androidOsVersion";
}
