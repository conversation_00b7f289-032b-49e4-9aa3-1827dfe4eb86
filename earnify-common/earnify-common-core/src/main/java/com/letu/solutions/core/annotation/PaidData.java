package com.letu.solutions.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记需要后置数据处理的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PaidData {
    /**
     * 指定后置处理器的 Bean 名称
     * （支持 Spring EL 表达式，如 "#result" 表示方法返回值）
     */
    String processor() default "defaultDataPostProcessor";
    /**
     * 用户 ID
     * @return
     */
    String userId() default "0";


}
