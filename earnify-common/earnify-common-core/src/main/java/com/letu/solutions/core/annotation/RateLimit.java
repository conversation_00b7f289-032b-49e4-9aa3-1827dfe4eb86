package com.letu.solutions.core.annotation;

import org.redisson.api.RateIntervalUnit;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {


    // 允许的访问次数
    int count() default 1;

    // 时间窗口（秒）
    int time() default 60;

    // 自定义Redis键
    String key() default "";

    //业务key
    String businessKey() default "";

    /**
     * 时间单位
     * @return
     */
    RateIntervalUnit unit() default RateIntervalUnit.SECONDS;

    /**
     * 自定义提示信息
     * @return
     */
    String message() default "请求过于频繁，请稍后再试";
}