package com.letu.solutions.core.annotation;

import cn.hutool.core.util.ArrayUtil;
import com.letu.solutions.core.config.SpringUtil;
import com.letu.solutions.core.exception.ThrowException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Aspect
@Component
@RequiredArgsConstructor
public class PaidDataProcessAspect {
    /**
     * 定义spel表达式解析器
     */
    private ExpressionParser parser = new SpelExpressionParser();
    /**
     * 定义spel解析模版
     */
    private ParserContext parserContext = new TemplateParserContext();
    /**
     * 定义spel上下文对象进行解析
     */
    private EvaluationContext context = new StandardEvaluationContext();
    /**
     * 方法参数解析器
     */
    private ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();


    /**
     * 环绕通知（若需同时处理同步/异步逻辑）
     */
    @Around("@annotation(paidData)")
    public Object handlePostProcessing(ProceedingJoinPoint joinPoint, PaidData paidData) throws Throwable {
        Object result = joinPoint.proceed(); // 执行原方法

        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();

        DataPostProcessor<Object> processor = SpringUtil.getBean(paidData.processor(), DataPostProcessor.class);
        String userId = getCombineKey(paidData, joinPoint);
        //处理拦截逻辑
        return processor.process(result, method, args,Long.parseLong(userId));
    }
    /**
     * 动态解析参数
     *
     * @param point       切入点
     * @return key
     */
    public String getCombineKey(PaidData paidData, JoinPoint point) {
        String userId = paidData.userId();
        // 获取方法(通过方法签名来获取)
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        // 判断是否是spel格式
        if (StringUtils.containsAny(userId, "#")) {
            // 获取参数值
            Object[] args = point.getArgs();
            // 获取方法上参数的名称
            String[] parameterNames = pnd.getParameterNames(method);
            if (ArrayUtil.isEmpty(parameterNames)) {
                throw new ThrowException("获取参数异常,请联系管理员!");
            }
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
            // 解析返回给key
            try {
                Expression expression;
                if (StringUtils.startsWith(userId, parserContext.getExpressionPrefix())
                        && StringUtils.endsWith(userId, parserContext.getExpressionSuffix())) {
                    expression = parser.parseExpression(userId, parserContext);
                } else {
                    expression = parser.parseExpression(userId);
                }
                userId = expression.getValue(context, String.class);
            } catch (Exception e) {
                throw new ThrowException("解析参数异常,请联系管理员!");
            }
        }
        return Optional.ofNullable(userId).orElse("0");
    }
}
