package com.letu.solutions.core.annotation;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.letu.solutions.core.model.ExtendData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 木鱼
 * @date 2019/11/15.
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnBean(RedissonClient.class)
public class CacheRedisConfig {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedissonClient redissonClient;
    @Value("${redis.cache.ratio:1000}")
    private Integer ratio;

    @Pointcut(value = "@annotation(com.letu.solutions.core.annotation.CacheRedis)")
    public void pointCut() {
    }

    @Around(value = "pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取method对象
        Method method = signature.getMethod();
        CacheRedis cacheRedis = method.getAnnotation(CacheRedis.class);
        int expireTime = cacheRedis.expireTime() * ratio;
        Object[] args = joinPoint.getArgs();
        //创建key
        String key = createKey(signature, args);
        String KeyPrivate = StrUtil.isBlank(cacheRedis.value()) ? "cache:" + signature.getName() + ":" + signature.getMethod().getName() + ":" : cacheRedis.value();
        Object redisValue = redisTemplate.opsForValue().get(KeyPrivate + ":" + key);
        if (null != redisValue) {
            return redisValue;
        }
        // 初始化分布式锁
        RLock lock = redissonClient.getLock("lock:" + KeyPrivate + ":" + key);
        try {
            // 防止缓存击穿 加锁
            lock.lock(cacheRedis.lockTime(), TimeUnit.MILLISECONDS);
            // 再次检查内存是否有，因为高并发下，可能在加锁这段时间内，已有其他线程放入缓存
            redisValue = redisTemplate.opsForValue().get(KeyPrivate + ":" + key);
        } finally {
            if (redisValue != null) {
                unlock(lock, KeyPrivate);
                return redisValue;
            }
        }
        try {
            // 并把结果放入缓存
            // 2. 执行查询的业务逻辑从数据库查询
            long l = System.currentTimeMillis();
            redisValue = joinPoint.proceed(joinPoint.getArgs());
            redisTemplate.opsForValue().set(KeyPrivate + ":" + key, redisValue, expireTime, TimeUnit.MILLISECONDS);
            log.info("缓存处理，执行结束，执行时间:{}", System.currentTimeMillis() - l);
            return redisValue;
        } catch (Exception e) {
            log.error("缓存执行发生异常", e);
            return redisValue;
        } finally {
            unlock(lock, KeyPrivate);
        }
    }

    private void unlock(RLock lock, String KeyPrivate) {
        try {
            if (lock.isLocked()) {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("缓存工具解锁失败，可能出现了慢查询,KeyPrivate：{}", KeyPrivate, e);
        }
    }

    /**
     * 拼接md5参数key
     */
    private String createKey(MethodSignature signature, Object[] args) {
        String[] parameterNames = signature.getParameterNames();
        if (null == parameterNames) {
            return "";
        }
        int length = parameterNames.length;
       JSONObject jsonObject = new JSONObject(16, 0.75f, true);
        for (int i = 0; i < length; i++) {
            //如果存在用户个人信息，跳过
            Object arg = args[i];
            if (arg == null ||  arg instanceof ExtendData){
                continue;
            }
            jsonObject.put(parameterNames[i], args[i]);
        }
        return SecureUtil.md5(jsonObject.toJSONString());
    }

}
