package com.letu.solutions.core.annotation;

import cn.hutool.core.util.ArrayUtil;
import com.letu.solutions.core.exception.ThrowException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnBean(RedissonClient.class)
public class RateLimitConfig {
    private final RedissonClient redissonClient;
    /**
     * 定义spel表达式解析器
     */
    private ExpressionParser parser = new SpelExpressionParser();
   /**
     * 方法参数解析器
     */
    private ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();

    /**
     * 全局 redis key (业务无关的key)
     */
    private final String GLOBAL_REDIS_KEY = "global:";

    /**
     * 限流 redis key
     */
    private final String RATE_LIMIT_KEY = GLOBAL_REDIS_KEY + "rate_limit:";


    @Before("@annotation(rateLimiter)")
    public void rateLimit(JoinPoint joinPoint,RateLimit rateLimiter) {
        int time = rateLimiter.time();
        int count = rateLimiter.count();
        RateIntervalUnit unit = rateLimiter.unit();
        // 处理 key
        String combineKey = getCombineKey(rateLimiter, joinPoint);
        try {
            long number = rateLimiter(combineKey, unit, count, time);
            if (number == -1) {
                throw new ThrowException(rateLimiter.message());
            }
            log.info("限制令牌 => {}, 剩余令牌 => {}, 缓存key => '{}'", count, number, combineKey);
        } catch (Exception e) {
            if (e instanceof ThrowException) {
                throw e;
            } else {
                throw new RuntimeException("服务器限流异常，请稍候再试");
            }
        }
    }

    /**
     * 返回带有特定前缀的 key
     *
     * @param rateLimiter 限流注解
     * @param point       切入点
     * @return key
     */
    public String getCombineKey(RateLimit rateLimiter, JoinPoint point) {
        String key = rateLimiter.key();
        // 获取方法(通过方法签名来获取)
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        // 判断是否是spel格式
        if (StringUtils.containsAny(key, "#")) {
            // 获取参数值
            Object[] args = point.getArgs();
            // 获取方法上参数的名称
            String[] parameterNames = pnd.getParameterNames(method);
            if (ArrayUtil.isEmpty(parameterNames)) {
                throw new ThrowException("限流key解析异常!请联系管理员!");
            }
            //每个参数都设置到evalContext中
            EvaluationContext evalContext = new StandardEvaluationContext();
            for (int i = 0; i < parameterNames.length; i++) {
                evalContext.setVariable(parameterNames[i], args[i]);
            }
            // 解析返回给key
            try {
                key= parser.parseExpression(key).getValue(evalContext,String.class)+"";
            } catch (Exception e) {
                log.error("限流[{}]解析异常!请联系管理员!",key,e);
                throw new ThrowException("限流key解析异常!请联系管理员!");
            }
        }
        // 限流前缀key
        StringBuilder stringBuffer = new StringBuilder(RATE_LIMIT_KEY);
        //key = global:rate_limit:xxx:xxx
        return stringBuffer.append(rateLimiter.businessKey()).append(key).toString();
    }

    /**
     * 限流
     *
     * @param key          限流key
     * @param timeUnit     时间类型
     * @param rate         速率
     * @param rateInterval 速率间隔
     * @return -1 表示失败
     */
    public long rateLimiter(String key, RateIntervalUnit timeUnit, int rate, int rateInterval) {
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
        rateLimiter.trySetRate(RateType.OVERALL, rate, rateInterval, timeUnit);
        if (rateLimiter.tryAcquire()) {
            return rateLimiter.availablePermits();
        } else {
            return -1L;
        }
    }
}

