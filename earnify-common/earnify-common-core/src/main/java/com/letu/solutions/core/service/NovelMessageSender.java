package com.letu.solutions.core.service;

import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: NovelMessageSender.java, v 0.1 2025/4/30 15:55 lai_kd Exp $$
 */
@Component
@RequiredArgsConstructor
public class NovelMessageSender {
    private static final String MESSAGE_SPLIT_SYMBOL = ":";
    private final RedisTemplate<String, String> redisTemplate;
    private final Gson gson;

    public <T> void sendMsg(String sendTopic, String objId, T t) {
        String[] split = objId.split(MESSAGE_SPLIT_SYMBOL, 2);
        this.redisTemplate.opsForValue().set(sendTopic + MESSAGE_SPLIT_SYMBOL + split[0], gson.toJson(t), 15L, TimeUnit.MINUTES);
        this.redisTemplate.convertAndSend(sendTopic, objId);
    }
}
