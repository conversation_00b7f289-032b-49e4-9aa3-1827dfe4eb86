package com.letu.solutions.core.constant;

public class CommonConstant {

    /**
     * 通用公共常量
     */
    public interface PUBLIC_CONSTANT {
        /**
         * 删除
         */
        public static final Long DELETE = 1L;

        /**
         * 不删除
         */
        public static final Long NOT_DELETE = 0L;

        /**
         * 父节点默认主键
         */
        public static final Long PARENT_ID = 0L;
        /**
         * 是叶子节点
         */
        public static final Long LEAF_ID = 1L;

        /**
         * 状态 0-正常
         */
        public static final Long STATUS_OPEN = 0L;

        /**
         * 状态 1-关闭
         */
        public static final Long STATUS_CLOSE = 1L;

        /**
         * 默认角色Id
         */
        public static final Long DEFAULT_ROLE = 1L;

        /**
         * 默认选中
         */
        public static final Long DEFAULT_SELECT = 1L;

        /**
         * 不选中
         */
        public static final Long DEFAULT_NOT_SELECT = 0L;

        /**
         * 已拒绝
         */
        public static final Long Rejected = 0L;

        /**
         * 未提交
         */
        public static final Long AUTHENTICATION_NOT_STATE = 1L;

        /**
         * 待审核
         */
        public static final Long TO_BE_REVIEWED = 2L;

        /**
         * 已经通过
         */
        public static final Long Passed = 3L;

        /**
         * 否
         */
        public static final Integer NO = 0;

        /**
         * 是
         */
        public static final Integer YES = 1;
        /**
         * 系统用户
         */
        public static final String SYSTEM = "SYSTEM";


    }

    /**
     * 通用公共状态
     */
    public interface CURRENCY_STATE {
        /**
         * 通用状态0
         */
        public static final Long CURRENCY_0_STATE = 0L;
        /**
         * 通用状态1
         */
        public static final Long CURRENCY_1_STATE = 1L;
        /**
         * 通用状态2
         */
        public static final Long CURRENCY_2_STATE = 2L;
        /**
         * 通用状态3
         */
        public static final Long CURRENCY_3_STATE = 3L;
        /**
         * 通用状态4
         */
        public static final Long CURRENCY_4_STATE = 4L;
        /**
         * 通用状态5
         */
        public static final Long CURRENCY_5_STATE = 5L;
        /**
         * 通用状态6
         */
        public static final Long CURRENCY_6_STATE = 6L;
        /**
         * 通用状态7
         */
        public static final Long CURRENCY_7_STATE = 7L;
        /**
         * 通用状态8
         */
        public static final Long CURRENCY_8_STATE = 8L;
        /**
         * 通用状态9
         */
        public static final Long CURRENCY_9_STATE = 9L;

        /**
         * 计算文件大小等 基础值
         */
        public static final Long COMPUTER_BASE_VALUE = 1024L;

    }

    /**
     * 符号
     */
    public interface PUBLIC_SYMBOL {

        /**
         * 百分号
         */
        public static final String PERCENTAGE_SYMBOL = "%";

        /**
         * 空字符
         */
        public static final String BLANK_STR = "";

        /**
         * 标点符号
         */
        public static final String GENERAL_SYMBOL = "[\\pP\\pS\\pM\\s]";
    }

    /**
     * 文件类型
     */
    public interface FILE_CONSTANT {

        /**
         * 视频类别
         */
        public static final String[] VIDEO_SUFFIX = {"avi", "mov", "rmvb", "rm", "flv", "mp4", "3gp", "wmv", "swf", "ipod", "psp", "unity3d"};

        /**
         * 游戏文件上传
         */
        public static final String[] GAME_SUFFIX = {"avi", "mov", "rmvb", "rm", "flv", "mp4", "3gp", "wmv", "swf", "ipod", "psp", "fbx", "unity3d", "ab"};

        /**
         * 游戏文件上传
         */
        public static final String[] MEDIA_SUFFIX = {"jpg", "png", "gif", "jpeg", "webp", "ico", "avi", "mov", "rmvb", "rm", "flv", "mp4", "3gp", "wmv", "swf", "ipod", "psp", "fbx"};

        /**
         * 图片类别
         */
        public static final String[] IMG_SUFFIX = {"jpg", "png", "gif", "jpeg", "webp", "ico", "unity3d"};

        /**
         * 官网上传支持文件类型
         */
        public static final String[] WEB_SUFFIX = {"avi", "mov", "rmvb", "rm", "flv", "mp4", "3gp", "wmv", "swf", "ipod", "psp", "fbx", "unity3d", "ab"};
    }

}
