package com.letu.solutions.core.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version Id: UserInfoDto.java, v 0.1 2025/5/7 17:16 lai_kd Exp $$
 */
@Data
public class UserInfoDto implements Serializable {
    private static final long serialVersionUID = 2057290528059080932L;
    /**
     * 用户主键id
     */
    private Long id;

    /**
     * 手机号码
     */
    private String userPhone;

    /**
     * 用户头像
     */
    private String userImage;

    /**
     *昵称
     */
    private String nickName;

    /**
     * 用户类别 NORMAL正常用户 VIP 用户
     */
    private String userType;

    /**
     * 是否创作者 1是 0否
     */
    private Integer creator;

    /**
     * 用户创建日期
     */
    private Integer day;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;

    /**
     * 是否删除[1:已删除;0:未删除]
     */
    private Integer del;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 渠道来源
     */
    private String sourceCode;
    /**
     * vip 过期时间
     */
    private Date vipExpiredTime;
}
