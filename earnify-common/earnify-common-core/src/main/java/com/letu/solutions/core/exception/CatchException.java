package com.letu.solutions.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主动抛出异常
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CatchException extends RuntimeException {

    private Integer code = -1;

    public CatchException(String message) {
        super(message);
    }

    public CatchException(Integer code, String message) {
        super(message);
        this.code = code;
    }
}
