package com.letu.solutions.core.exception;

import cn.hutool.core.util.StrUtil;
import com.letu.solutions.core.model.R;
import com.mysql.cj.jdbc.exceptions.MysqlDataTruncation;
import com.letu.solutions.core.enums.ExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.core.env.Environment;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.nio.file.AccessDeniedException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 异常处理基类
 *
 * <AUTHOR>
 * @version 1.5.0
 * @date 2020/5/18
 */
@Slf4j
@RestControllerAdvice
public class BaseExceptionHandler {
    @Resource
    private Environment environment;
    private final static String prod = "prod";

    /**
     * 处理错误的请求
     */
    @ExceptionHandler(value = Exception.class)
    public R exceptionRest(Exception e) {
        if (e instanceof SQLIntegrityConstraintViolationException) {
            log.warn("唯一索引异常", e);
            return R.fail(ExceptionEnum.RUN_EXCEPTION, "重复添加，请确认数据是否重复！");
        }
        if (e instanceof MysqlDataTruncation) {
            log.warn("数据库长度越界", e);
            return R.fail(ExceptionEnum.RUN_EXCEPTION, "字段长度不合法！");
        }
        log.error(e.getMessage(), e);
        // 打印堆栈信息
        return R.fail(e.getMessage());
    }

    /**
     * 处理错误的请求
     */
    @ExceptionHandler(value = ThrowException.class)
    public R exceptionRest(ThrowException e) {
        // 打印堆栈信息
        return R.fail(e.getCode(), e.getMessage());
    }

    /**
     * 参数异常处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = IllegalArgumentException.class)
    public R exceptionIllegal(IllegalArgumentException e) {
        log.warn("参数异常", e);
        return R.fail(ExceptionEnum.RUN_EXCEPTION, e.getClass().equals(IllegalArgumentException.class) ? e.getMessage() : e.toString());
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public R exceptionMethodArgumentNotValid(MethodArgumentNotValidException e) {
        log.warn("参数校验失败", e);
        try {
            List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
            List<String> collect = allErrors.stream().map(ObjectError::getDefaultMessage).collect(Collectors.toList());
            return R.fail(ExceptionEnum.RUN_EXCEPTION, StrUtil.join(",", collect));
        } catch (Exception exception) {
            return R.fail(ExceptionEnum.RUN_EXCEPTION, "参数校验失败");
        }
    }

    @ExceptionHandler(value = BindException.class)
    public R exceptionBindException(BindException e) {
        log.warn("参数校验失败", e);
        try {
            List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
            List<String> collect = allErrors.stream().map(ObjectError::getDefaultMessage).collect(Collectors.toList());
            return R.fail(ExceptionEnum.RUN_EXCEPTION, StrUtil.join(",", collect));
        } catch (Exception exception) {
            return R.fail(ExceptionEnum.RUN_EXCEPTION, "参数校验失败");
        }
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    public R exceptionBindException(ConstraintViolationException e) {
        log.warn("参数校验失败", e);
        try {
            Set<ConstraintViolation<?>> allErrors = e.getConstraintViolations();
            List<String> collect = allErrors.stream().map(constraintViolation ->
                    String.join(":",
                            constraintViolation.getPropertyPath().toString(),
                            constraintViolation.getMessage())).collect(Collectors.toList()
            );
            return R.fail(ExceptionEnum.RUN_EXCEPTION, StrUtil.join(",", collect));
        } catch (Exception exception) {
            return R.fail(ExceptionEnum.RUN_EXCEPTION, "参数校验失败");
        }
    }

    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public R exceptionBindException(MissingServletRequestParameterException e) {
        log.warn("参数缺失异常", e);
        return R.fail(ExceptionEnum.RUN_EXCEPTION, e.getMessage());
    }

    //@ExceptionHandler(value = RpcException.class)
    //public R exceptionBindException(RpcException e) {
    //    if (prod.equals(environment.getProperty("spring.cloud.nacos.discovery.namespace"))) {
    //        log.error("dubbo服务缺失", e);
    //    } else {
    //        log.warn("dubbo服务缺失", e);
    //    }
    //    return R.fail(ExceptionEnum.RUN_EXCEPTION, "服务异常，请稍后重试");
    //}

    /**
     * 处理错误的请求
     */
    @ExceptionHandler(value = DuplicateKeyException.class)
    public R exceptionSQLIntegrity(SQLIntegrityConstraintViolationException e) {
        // 打印堆栈信息
        log.warn("唯一索引异常", e);
        return R.fail(ExceptionEnum.RUN_EXCEPTION, "重复添加，请确认数据是否重复！");
    }

    /**
     * 处理错误的请求
     */
    @ExceptionHandler(value = DataAccessException.class)
    public R exceptionMysqlData(MysqlDataTruncation e) {
        // 打印堆栈信息
        log.warn("数据库长度越界", e);
        return R.fail(ExceptionEnum.RUN_EXCEPTION, "字段长度不合法！");
    }


    /**
     * 处理错误的请求
     */
    @ExceptionHandler(value = HttpMessageConversionException.class)
    public R httpMessageConversionException(HttpMessageConversionException e) {
        // 打印堆栈信息
        log.warn("参数异常", e);
        return R.fail(ExceptionEnum.RUN_EXCEPTION, e.getMessage());
    }
    /**
     * 处理错误的请求
     */
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public R httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        // 打印堆栈信息
        log.warn("处理请求异常[{}]，不支持当前请求方法[{}],请求头[{}]", e.getMessage(), e.getMethod(),e.getHeaders());
        return R.fail(ExceptionEnum.RUN_EXCEPTION, e.getMessage());
    }
    ///**
    // * 处理空指针的异常
    // */
    //@ExceptionHandler(value = NullPointerException.class)
    //public R exceptionHandler(NullPointerException e) {
    //    log.error("null point exception！The reason is: ", e);
    //    return  R.fail(ExceptionEnum.RUN_EXCEPTION, "空指针异常！");
    //}
    ///**
    // * 未知异常
    // */
    //@ExceptionHandler(value = Exception.class)
    //public R systemExceptionHandler(Exception e) {
    //    log.error("system exception！The reason is：{}", e.getMessage(), e);
    //    return  R.fail(ExceptionEnum.RUN_EXCEPTION, "系统异常,请稍后重试！");
    //}
    //
    //
    ///**
    // * 权限异常
    // */
    //@ExceptionHandler(value = AccessDeniedException.class)
    //public R accessException(AccessDeniedException e) {
    //    log.error("AccessDeniedException:{}", e.getMessage());
    //    return  R.fail(ExceptionEnum.RUN_EXCEPTION, "权限异常！");
    //}
//    @ExceptionHandler(HttpMessageNotReadableException.class)
//    public R<?> handleEnumConvertError(HttpMessageNotReadableException ex) {
//        return R.fail("参数格式错误：请确认枚举值是否合法");
//    }
}
