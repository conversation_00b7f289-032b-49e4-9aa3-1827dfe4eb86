package com.letu.solutions.core.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 锁定工具
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LockUtil {
    private final RedisTemplate<String, String> stringRedisTemplate;
    private final String template = "lock:%s:%s";

    /**
     * 根据枚举加锁
     *
     * @return 可用 返回true 否则false
     */
    public boolean lock(LockKey lockKey, String uniqueKey) {
        String key = String.format(template, lockKey.name(), uniqueKey);
        return checkLockIncrement(key, lockKey.getNum(), lockKey.getTime(), lockKey.getUnit());
    }

    /**
     * 根据枚举加锁
     *
     * @return 可用 返回true 否则false
     */
    public boolean lock(String lockKey, Long num, TimeUnit timeUnit, Integer time, String uniqueKey) {
        String key = String.format(template, lockKey, uniqueKey);
        return checkLockIncrement(key, num, time, timeUnit);
    }

    /**
     * 删除锁
     *
     * @return 可用 返回true 否则false
     */
    public void deleteLock(LockKey lockKey, String uniqueKey) {
        String key = String.format(template, lockKey.name(), uniqueKey);
        stringRedisTemplate.delete(key);
    }

    /**
     * 根据枚举判断是否已锁定
     *
     * @return 计数大于限定次数 返回true 否则false
     */
    public boolean hasLock(LockKey lockKey, String uniqueKey) {
        String key = String.format(template, lockKey.name(), uniqueKey);
        String num = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isEmpty(num)) {
            return false;
        }
        return Long.parseLong(num) > lockKey.getNum();
    }

    /**
     * 三种维度锁定认证
     *
     * @return 计数大于限定次数 返回true 否则false
     */
    public void checkLock(String key, String uniqueKey) {
        String checkTemplate = "lock:%s:%s:%s";
        String m1key = String.format(checkTemplate, key, "1m", uniqueKey);
        String m10key = String.format(checkTemplate, key, "10m", uniqueKey);
        String d1key = String.format(checkTemplate, key, "1d", uniqueKey);
        boolean m1Ok = checkLockIncrement(m1key, 3L, 1, TimeUnit.MINUTES);
        boolean m10Ok = checkLockIncrement(m10key, 10L, 10, TimeUnit.MINUTES);
        boolean d1Ok = checkLockIncrement(d1key, 30L, 1, TimeUnit.DAYS);
        Assert.isTrue(m1Ok, "获取次数已达上限，稍后重试");
        Assert.isTrue(m10Ok, "获取次数已达上限，稍后重试");
        Assert.isTrue(d1Ok, "今日获取次数已达上限，请明日再试");
    }

    public boolean checkLockIncrement(String key, Long maxNum, Integer timeout, TimeUnit timeUnit) {
        Long num = stringRedisTemplate.opsForValue().increment(key, 1L);
        Assert.notNull(num, "锁定计数失败");
        if (num == 1) {
            stringRedisTemplate.expire(key, timeout, timeUnit);
        }
        return num <= maxNum;
    }

    @AllArgsConstructor
    @Getter
    public enum LockKey {
        downMarket(1L, 1, TimeUnit.HOURS),
        sms(5L, 5, TimeUnit.MINUTES),
        updatePwd(1L, 1, TimeUnit.DAYS),
        ;
        /**
         * 最大触发锁数量
         */
        private Long num;
        /**
         * 时间
         */
        private Integer time;
        /**
         * 时间单位
         */
        private TimeUnit unit;
    }
}
