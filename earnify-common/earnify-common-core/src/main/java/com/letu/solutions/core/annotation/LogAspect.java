package com.letu.solutions.core.annotation;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Aspect
@Component
@Slf4j
public class LogAspect {
    @Resource
    private HttpServletRequest request;

    /**
     * ..表示包及子包 该方法代表controller层的所有方法
     *
     * @date 2020/9/21
     */
    @Pointcut("execution(* com.letu.solutions.*.controller..*.*(..)) && !execution(* com.letu.solutions.*.controller.ServerDownController.*(..))")
    public void controllerMethod() {
    }


    @Around("controllerMethod()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String uri = null;
        String userId = null;
        String ipAddress = null;
        String os = null;
        try {
            uri = request.getRequestURI();
            log.info("请求----》:{}", uri);
            userId = request.getHeader("userId");
            ipAddress = request.getHeader("ipAddress");
            os = request.getHeader("os");
            //setLanguage(request);
        } catch (Exception e) {
            log.error("获取请求信息失败,{}", e.getMessage());
        }
        boolean needLog = null != uri && !uri.contains("upload") && !uri.contains("index.e") && !uri.contains("callback.e");
        if (needLog) {
            String params = loadParam((MethodSignature) joinPoint.getSignature(), joinPoint.getArgs());
            log.info("http请求,收到,客户端:{},uri:{},userId:{},ipAddress:{},params:{}", os, uri, userId, ipAddress, params);
        }
        Object result;
        try {
            result = joinPoint.proceed();
        } catch (Exception e) {
            log.info("http请求,截断,客户端:{},uri:{},userId:{},ipAddress:{},errMsg:{}", os, uri, userId, ipAddress, e.getMessage());
            throw e;
        }
        if (needLog) {
            log.info("http请求,响应,客户端:{},uri:{},userId:{},ipAddress:{},response:{}", os,
                    uri, userId, ipAddress, ObjectUtil.isNull(result) ? null : JSONObject.toJSONString(result));
        }
        return result;
    }

    /**
     * 拼接md5参数key
     */
    private String loadParam(MethodSignature signature, Object[] args) {
        String[] parameterNames = signature.getParameterNames();
        if (null == parameterNames) {
            return "";
        }
        int length = parameterNames.length;
        JSONObject jsonObject = new JSONObject(16, 0.75f, true);
        for (int i = 0; i < length; i++) {
            if (!parameterNames[i].equals("extendData")
                    //排除无法序列化的参数
                    && !(args[i] instanceof HttpServletRequest)
                    && !(args[i] instanceof HttpServletResponse)) {
                jsonObject.put(parameterNames[i], args[i]);
            }
        }
        return jsonObject.toJSONString();
    }
}