package com.letu.solutions.core.model;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.util.Date;


@Data
public class CKBaseEntity {

    /**
     * 表id
     */
    private Long id;
    /**
     * 日期
     */
    private Integer day;

    /**
     * 创建时间
     */
    private Date createTime;

    public void writeDay() {
        Date date = new Date();
        this.day = Integer.parseInt(DateUtil.format(date, "yyyyMMdd"));
        this.createTime = date;
    }
}
