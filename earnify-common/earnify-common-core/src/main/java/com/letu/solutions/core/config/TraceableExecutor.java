package com.letu.solutions.core.config;

import brave.propagation.CurrentTraceContext;

import java.util.concurrent.Executor;

public class TraceableExecutor implements Executor {

    private final Executor delegate;
    private final CurrentTraceContext traceContext;

    public TraceableExecutor(CurrentTrace<PERSON>ontext traceContext, Executor delegate) {
        this.traceContext = traceContext;
        this.delegate = delegate;
    }

    @Override
    public void execute(Runnable command) {
        // wrap Runnable 保证 TraceContext 传播
        delegate.execute(traceContext.wrap(command));
    }
}