package com.letu.solutions.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 请求头实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmsUserInfo implements Serializable {

    private static final long serialVersionUID = 2057290528059080932L;

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 全路径接口code列表
     */
    private List<String> nameAlls;
}
