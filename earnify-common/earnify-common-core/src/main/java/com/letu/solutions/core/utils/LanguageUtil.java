package com.letu.solutions.core.utils;

import cn.hutool.core.util.StrUtil;
import com.letu.solutions.core.config.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 多语言处理工具
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Slf4j
public class LanguageUtil {
    private static MessageSource messageSource;
    private static final String type1 = "#";
    private static final String type2 = "$";
    private static final String errorCode = "#语言包异常";
    private static final String defaultLanguage = "zh";

    /**
     * 语言包替换
     */
    public static String trans(String code) {
        if (StrUtil.isEmpty(code)) {
            return code;
        }
        if (null == messageSource) {
            messageSource = SpringUtil.getBean(MessageSource.class);
        }
        int type = code.startsWith(type1) ? 1 : code.startsWith(type2) ? 2 : 0;
        if (type == 0) {
            return code;
        }
        Locale locale = LocaleContextHolder.getLocale();
        if (type == 1 && locale.getLanguage().equals(defaultLanguage)) {
            return code.substring(1);
        }

        return loadMessage(code, locale);
    }

    public static String loadMessage(String code, Locale locale) {
        try {
            return messageSource.getMessage(code.substring(1), null, locale);
        } catch (Exception e) {
            return messageSource.getMessage(errorCode, null, locale);
        }
    }
}
