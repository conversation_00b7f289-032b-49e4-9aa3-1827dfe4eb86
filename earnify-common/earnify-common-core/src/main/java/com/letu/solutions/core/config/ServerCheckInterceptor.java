package com.letu.solutions.core.config;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.letu.solutions.core.annotation.Server;
import com.letu.solutions.core.enums.ExceptionEnum;
import com.letu.solutions.core.enums.ServerEnum;
import com.letu.solutions.core.enums.header.HeaderEnum;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.R;
import com.letu.solutions.core.enums.BusinessTypeEnum;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;
import org.springframework.web.servlet.support.RequestContextUtils;

import java.util.Locale;

/**
 * 请求业务是否合法拦截器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ServerCheckInterceptor implements HandlerInterceptor {
    @Value("${spring.application.name:}")
    private String serverName;

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,@NotNull Object handler) throws Exception {
        try {
            setLanguage(request);
            if (handler instanceof ResourceHttpRequestHandler) {
                throw new ThrowException("#非法访问");
            }
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            String businessType = request.getHeader(HeaderEnum.businessType);
            Server server = handlerMethod.getMethodAnnotation(Server.class);
            if (null != server) {
                if (server.freeCheck()) {
                    return true;
                }
            }
            if (serverName.equals(ServerEnum.cms)) {
                if (!businessType.equals(BusinessTypeEnum.earnify_cms.toString())) {
                    Assert.isNull(server);
                    if (server.freeCheck()) {
                        return true;
                    }
                }
            }
            if (null != server) {
                BusinessTypeEnum[] prefix = server.prefix();
                for (BusinessTypeEnum businessTypeEnum : prefix) {
                    if (businessTypeEnum == BusinessTypeEnum.all || businessType.equals(businessTypeEnum.toString())) {
                        return true;
                    }
                }
                return false;
            }
        } catch (ThrowException e) {
            error(response, e.getCode(), e.getMessage());
            return false;
        } catch (Exception e) {
            error(response, ExceptionEnum.AUTH_ERROR, "#访问受限");
            return false;
        }
        return true;
    }


    private void setLanguage(HttpServletRequest request) {
        LocaleResolver localeResolver = RequestContextUtils.getLocaleResolver(request);
        // 从请求头中获取语言信息
        String languageHeader = request.getHeader("language");

        // 解析语言信息并设置 Locale
        Locale locale = parseLocale(languageHeader);
        localeResolver.setLocale(request, null, locale); // 设置请求的语言类型
    }

    private Locale parseLocale(String language) {
        if (StrUtil.isEmpty(language)) {
            return Locale.SIMPLIFIED_CHINESE; // 默认语言
        }
        return Locale.forLanguageTag(language);  // 根据语言标签解析 Locale
    }

    public void error(HttpServletResponse response, Integer code, String message) throws Exception {
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JSON.toJSONString(R.fail(code, message)));
    }
}
