package com.letu.solutions.core.model;

import cn.hutool.core.util.NumberUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 请求扩展数据模型，用于绑定请求扩展属性&数据，如请求来源；客户端类型代码，客户端IP等
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExtendData implements Serializable {

    private static final long serialVersionUID = 2057290528059080932L;

    private String ipAddress;
    private Long userId;
    private HeaderDto headerDto;
    /**
     * netty管道编号专用
     */
    private String channelText;
    /**
     * 后台管理信息
     */
    private Authentication authentication;

    public boolean isLogin() {
        return null != userId && !NumberUtil.equals(Long.valueOf(0), this.userId);
    }

}
