package com.letu.solutions.core.config;


import com.baomidou.mybatisplus.extension.handlers.GsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @since 2019-11-28
 */
@Component
public class MybatisJsonConfig implements CommandLineRunner {


    /**
     * 用于处理mybatis-plus的json类型字段的序列化和反序列化
     */
    @Override
    public void run(String... args) throws Exception {
        JacksonTypeHandler.setObjectMapper(new ObjectMapper());
        GsonTypeHandler.setGson(new Gson());
    }
}