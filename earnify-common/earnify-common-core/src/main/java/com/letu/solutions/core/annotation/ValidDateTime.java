//package com.letu.solutions.core.annotation;
//
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.lang.Assert;
//
//import jakarta.validation.Constraint;
//import jakarta.validation.ConstraintValidatorContext;
//import jakarta.validation.Payload;
//import java.lang.annotation.Documented;
//import java.lang.annotation.Retention;
//import java.lang.annotation.Target;
//import java.util.Date;
//
//import static java.lang.annotation.ElementType.FIELD;
//import static java.lang.annotation.ElementType.PARAMETER;
//import static java.lang.annotation.RetentionPolicy.RUNTIME;
//
//@Documented
//@Constraint(validatedBy = DateTimeValidator.class)
//@Target({FIELD, PARAMETER})
//@Retention(RUNTIME)
//public @interface ValidDateTime {
//    String message() default "The date and time must be either in the past or in the future.";
//
//    Class<?>[] groups() default {};
//
//    Class<? extends Payload>[] payload() default {};
//
//    // 时间格式
//    Format format() default Format.yyyyMMddHHmmss;
//
//    // 添加属性以指定日期时间必须大于还是小于当前时间
//    Comparison comparison() default Comparison.BOTH;
//
//    enum Format {
//        yyyyMMdd,
//        yyyyMMddHHmmss,
//    }
//
//    enum Comparison {
//        BEFORE,
//        AFTER,
//        BOTH
//    }
//}
//
//class DateTimeValidator implements jakarta.validation.ConstraintValidator<ValidDateTime, Long> {
//
//    private ValidDateTime.Format format;
//
//    private ValidDateTime.Comparison comparison;
//
//    @Override
//    public void initialize(ValidDateTime constraintAnnotation) {
//        this.comparison = constraintAnnotation.comparison();
//        this.format = constraintAnnotation.format();
//    }
//
//    @Override
//    public boolean isValid(Long dateTime, ConstraintValidatorContext context) {
//        if (dateTime == null) {
//            return true; // 如果日期时间为null，默认视为有效
//        }
//        long nowTime = 0;
//        if (format == ValidDateTime.Format.yyyyMMddHHmmss) {
//            nowTime = Long.parseLong(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
//            if (dateTime.toString().length() != 14) {
//                Assert.isTrue(false, "时间格式异常[yyyyMMddHHmmss]~");
//            }
//        } else if (format == ValidDateTime.Format.yyyyMMdd) {
//            nowTime = Long.parseLong(DateUtil.format(new Date(), "yyyyMMdd"));
//            if (dateTime.toString().length() != 8) {
//                Assert.isTrue(false, "时间格式异常[yyyyMMdd]~");
//            }
//        }
//        switch (comparison) {
//            case BEFORE:
//                return dateTime < nowTime;
//            case AFTER:
//                return dateTime > nowTime;
//            case BOTH:
//                return dateTime < nowTime || dateTime > nowTime;
//            default:
//                throw new IllegalStateException("Invalid comparison value");
//        }
//    }
//}