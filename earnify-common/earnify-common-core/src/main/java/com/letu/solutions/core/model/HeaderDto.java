package com.letu.solutions.core.model;

import com.letu.solutions.core.enums.BusinessTypeEnum;
import com.letu.solutions.core.enums.OsEnum;
import com.letu.solutions.core.enums.OsTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 请求头实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HeaderDto implements Serializable {

    private static final long serialVersionUID = 2057290528059080932L;

    /**
     * 设备 仅支持[android,ios,web] 3种值
     */
    private OsEnum os;
    /**
     * 平台[app;h5]
     */
    private OsTypeEnum osType;
    /**
     * 业务类型 [admin,customer]
     */
    private BusinessTypeEnum businessType;
    /**
     * 登录信息标识
     */
    private String token;
    /**
     * 签名标识
     */
    private String sign;
    /**
     * 渠道code web端没有传xl-android-01，cms没有不用传
     */
    private String codeChannel;
    /**
     * 落地页id app没有不传
     */
    private Long pageId;

    /**
     * 设备唯一id
     */
    private String deviceId;

    /**
     * 渠道包类型 web不传
     */
    private String platform;

    /**
     * app版本号（web默认传1.0.0）
     */
    private String version;
    /**
     * 请求时间戳
     */
    private String timestamps;
    /**
     * 主体编号
     */
    private String bodyCode;
    /**
     * 浏览器自带请求头 origin
     */
    private String origin;
    /**
     * 浏览器自带请求头 cookie
     */
    private String cookie;
    /**
     * 浏览器自带请求头 User-Agent
     */
    private String userAgent;
    /**
     * mac
     */
    private String mac;
    /**
     * 浏览器信息
     */
    private String browser;
    /**
     * 手机厂商
     */
    private String phoneBrand;
    /**
     * 手机型号
     */
    private String phoneModel;
    /**
     * 系统版本
     */
    private String androidOsVersion;
    /**
     * appID
     */
    private String appId;
    /**
     * 语言 ['zh-cn', 'en-us']
     */
    private String language;
}
