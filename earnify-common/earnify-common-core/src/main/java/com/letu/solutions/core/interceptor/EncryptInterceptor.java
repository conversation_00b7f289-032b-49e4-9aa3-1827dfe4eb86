//package com.letu.solutions.core.interceptor;
//
//import cn.hutool.core.annotation.AnnotationUtil;
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.ReflectUtil;
//import com.letu.solutions.core.annotation.SensitiveData;
//import com.letu.solutions.core.annotation.SensitiveFiled;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.apache.ibatis.executor.parameter.ParameterHandler;
//import org.apache.ibatis.plugin.*;
//import org.springframework.stereotype.Component;
//
//import java.lang.reflect.Field;
//import java.sql.PreparedStatement;
//import java.util.Collection;
//import java.util.Map;
//import java.util.Properties;
//
/// **
// * 加密拦截器
// *
// * <AUTHOR>
// * @create 2023-04-13 11:06
// */
//
//@Slf4j
//@Component
///**
// * @Intercepts注解开启拦截器
// * type 属性指定当前拦截器使用StatementHandler 、ResultSetHandler、ParameterHandler，Executor的一种
// * method 属性指定使用以上四种类型的具体方法（可进入class内部查看其方法）。
// * args 属性指定预编译语句
// */
//
//@Intercepts({
//        //@Signature注解定义拦截器的实际类型
//        @Signature(type = ParameterHandler.class, method = "setParameters", args = PreparedStatement.class),
//})
//public class EncryptInterceptor implements Interceptor {
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        //@Signature 指定了 type= parameterHandler 后，这里的 invocation.getTarget() 便是parameterHandler
//        //若指定ResultSetHandler ，这里则能强转为ResultSetHandler
//        ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();
//        //获取参数对象，即mapper中paramsType的实例
//        Field paramsFiled = parameterHandler.getClass().getDeclaredField("parameterObject");
//        //将此对象的 accessible 标志设置为指示的布尔值。值为 true 则指示反射的对象在使用时应该取消 Java 语言访问检查。
//        paramsFiled.setAccessible(true);
//        //取出实例
//        Object parameterObject = paramsFiled.get(parameterHandler);
//        if (parameterObject != null) {
//            if (parameterObject instanceof Map<?, ?>) {
//                mapSolve(parameterObject);
//            } else if (parameterObject instanceof Collection<?>) {
//                collectionSolve(parameterObject);
//            } else {
//                objSolve(parameterObject);
//            }
//
//        }
//        //获取原方法的返回值
//        return invocation.proceed();
//    }
//
//    private void mapSolve(Object parameterObject) {
//        Map<?, ?> parameterMap = (Map<?, ?>) parameterObject;
//        for (Object key : parameterMap.keySet()) {
//            Object param = parameterMap.get(key);
//            if (ObjectUtil.isNull(param)) {
//                continue;
//            }
//            objSolve(param);
//        }
//    }
//
//    private void collectionSolve(Object parameterObject) {
//        Collection<?> paramCollection = (Collection<?>) parameterObject;
//        if (CollectionUtil.isEmpty(paramCollection)) {
//            return;
//        }
//        Object first = CollectionUtil.getFirst(paramCollection);
//        SensitiveData sensitiveData = AnnotationUtil.getAnnotation(first.getClass(), SensitiveData.class);
//        if (ObjectUtil.isNull(sensitiveData)) {
//            return;
//        }
//        for (Object param : paramCollection) {
//            objSolve(param);
//        }
//    }
//
//    private void objSolve(Object parameterObject) {
//        Class<?> parameterObjectClass = parameterObject.getClass();
//        //校验该实例的类是否被@SensitiveData所注解
//        SensitiveData sensitiveData = AnnotationUtil.getAnnotation(parameterObjectClass, SensitiveData.class);
//        if (ObjectUtil.isNotNull(sensitiveData)) {
//            //取出当前类的所有字段，传入加密方法
//            Field[] declaredFields = parameterObjectClass.getDeclaredFields();
//            for (Field aesField : declaredFields) {
//                try {
//                    //取出所有被EncryptDecryptFiled注解的字段
//                    SensitiveFiled filed = aesField.getAnnotation(SensitiveFiled.class);
//                    if (!ObjectUtil.isNull(filed) && aesField.getType().equals(String.class)) {
//                        String fieldValue = (String) ReflectUtil.getFieldValue(parameterObject, aesField);
//                        if (StringUtils.isEmpty(fieldValue)) {
//                            continue;
//                        }
//                        ReflectUtil.setFieldValue(parameterObject, aesField,
//                                EncryptUtil.encrypt((String) ReflectUtil.getFieldValue(parameterObject, aesField)));
//                    }
//                } catch (Exception e) {
//                    log.error("反射获取加密字段异常", e);
//                }
//            }
//        }
//    }
//
//    /**
//     * 一定要配置，加入此拦截器到拦截器链
//     *
//     * @param target
//     * @return
//     */
//
//    @Override
//    public Object plugin(Object target) {
//        return Plugin.wrap(target, this);
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//
//    }
//}
