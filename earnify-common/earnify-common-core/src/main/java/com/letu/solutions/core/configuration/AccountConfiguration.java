package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 账户配置相关
 *
 * <AUTHOR>
 */
@RefreshScope
@Data
@ConfigurationProperties(prefix = "account")
@Configuration
public class AccountConfiguration {

    /**
     * 极光配置
     */
    private String jgAuth;
    private String jgAppKey;

    private boolean threeCertOpen;

    private String threeCertCode;

    /**
     * 第三方无法正常进行三要素的用户加入白名单字典
     */
    private Map<String, String> whiteMap;

    /**
     * 平台账户id
     */
    private Long platformUserId;
}
