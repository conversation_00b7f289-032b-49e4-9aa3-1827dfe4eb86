package com.letu.solutions.core.model;

import brave.internal.InternalPropagation;
import brave.propagation.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SleuthDto implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final String MQ_KEY = "sleuthDto";
    private Integer flags;
    private Long traceIdHigh;
    private Long traceId;
    private Long localRootId;
    private Long parentId;
    private Long spanId;

    public SleuthDto(TraceContext context) {
        try {
            this.traceIdHigh = context.traceIdHigh();
            this.traceId = context.traceId();
            this.localRootId = context.localRootId();
            this.parentId = context.parentId();
            this.spanId = context.spanId();
            this.flags = InternalPropagation.instance.flags(context);
        } catch (Exception e) {
            log.error("链路追踪，sleuth对象化失败", e);
        }
    }
}