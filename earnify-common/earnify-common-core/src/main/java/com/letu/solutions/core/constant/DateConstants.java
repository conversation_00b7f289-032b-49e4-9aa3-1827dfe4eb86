package com.letu.solutions.core.constant;

import java.time.format.DateTimeFormatter;

/**
 * 日期相关常量
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public final class DateConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private DateConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 标准日期格式：yyyy-MM-dd
     */
    public static final String DATE_PATTERN = "yyyyMMdd";

    /**
     * 日期正则表达式：yyyy-MM-dd
     */
    public static final String DATE_REGEX = "^\\d{4}-\\d{2}-\\d{2}$";

    /**
     * 标准日期格式化器：yyyy-MM-dd
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    /**
     * 日期时间格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期时间格式化器：yyyy-MM-dd HH:mm:ss
     */
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DATETIME_PATTERN);
}
