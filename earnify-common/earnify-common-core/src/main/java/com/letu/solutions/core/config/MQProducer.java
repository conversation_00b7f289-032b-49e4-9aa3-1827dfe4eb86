package com.letu.solutions.core.config;

import com.letu.solutions.core.configuration.RocketMqConfig;
import org.apache.rocketmq.client.apis.*;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * mq配置器
 *
 * <AUTHOR>
 */
@Configuration
public class MQProducer {

    @Resource
    private RocketMqConfig rocketMqConfig;


    @Bean
    public Producer defaultProducer() throws ClientException {
        SessionCredentialsProvider sessionCredentialsProvider =
                new StaticSessionCredentialsProvider(rocketMqConfig.getAccessKey(), rocketMqConfig.getSecretKey());
        ClientConfiguration clientConfiguration = ClientConfiguration.newBuilder()
                .setEndpoints(rocketMqConfig.getNameSrvAddr())
                .setCredentialProvider(sessionCredentialsProvider)
                .build();
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        Producer build = provider.newProducerBuilder().setClientConfiguration(clientConfiguration).build();
        return build;
    }
}
