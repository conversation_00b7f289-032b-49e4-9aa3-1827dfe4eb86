package com.letu.solutions.core.utils;

import cn.hutool.core.util.RandomUtil;

/**
 * <AUTHOR>
 * @version Id: NickNameUtils.java, v 0.1 2025/5/21 11:37 lai_kd Exp $$
 */
public class NickNameUtils {
    private static final String NICK_NAME="薯条_";

    /**
     * 生成昵称
     * @return
     */
    public static String getNickName() {
       return NICK_NAME + RandomUtil.randomString(5);
    }

    public static void main(String[] args) {
        for (int i = 0; i <10 ; i++) {
            System.out.println(getNickName());
        }
    }
}
