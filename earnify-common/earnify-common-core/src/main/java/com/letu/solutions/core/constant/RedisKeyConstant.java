package com.letu.solutions.core.constant;

/**
 * redis key常量
 * <AUTHOR>
 * @version Id: RedisKeyConstant.java, v 0.1 2025/4/15 18:02 lai_kd Exp $$
 */
public class RedisKeyConstant {

    public static final String SITEMAP_KEY = "novel:sitemap";

    public static final String SITEMAP_KEY_ALL_FILE = "novel:sitemap:all";

    public static final String BUSINESS_CHANNEL_KEY="business:channel:%s";
    public static final String BUSINESS_SITE_KEY="business:site:%s";

    public static final String BUSINESS_SAVE_REC_BOOK_KEY="business:save:rec:book:%s:%s";

    public static final String BUSINESS_SYNC_THIRD_BOOK_KEY="business:sync:third:book:%s:%s";


    public static final String BUSINESS_FEED_SESSION_SITE_USERID_KEY="business:feed:sessionId:%s:%s";
    /**
     * 站点强推
     */
    public static final String BUSINESS_OP_ITEM_KEY="business:op:item:%s";





    public static String getUserTopicFeatureKey(String userId) {
        return "usertopicfeature:" + userId + ":hash";
    }

    public static String getUserInfoKey(String userId) {
        return "userinfo:" + userId + ":value";
    }

    public static String getUserHistoryKey(String userId, String siteId) {
        return String.format("userhistory:%s:%s:zset", userId, siteId);
    }

    public static String getUserLaneCacheItemsKey(String userId, String laneType, String siteId) {
        return String.format("userlanecacheitems:%s-%s-%s", userId, laneType, siteId);
    }

    public static String getRecOpConfigKey() {
        return String.format("rec:opconfig:hash");
    }

    public static String getRecOpItemKey(Long opItemId) {
        return String.format("rec:opitem:" + opItemId + ":value");
    }

    public static String getRecOpItemLockKey(Long opItemId) {
        return String.format("rec:opitem:" + opItemId + ":lock:value");
    }

    public static String getRecOpColumnKey() {
        return String.format("rec:opcolun:hash");
    }

    public static String getUserGroupBitMapKey(String groupCode) {
        return "groupCode:" + groupCode + ":value";
    }

    public static String getRecHistoryDetailKey(String recSeq) {
        return String.format("userhistory:%s:hash", recSeq);
    }

    public static String getItemKey(String itemId) {

        return "item:" + itemId + ":hash";
    }

    public static String getRecallItemKey(Long itemId) {
        return String.format("rec:recallItem:" + itemId + ":value");
    }

    public static String getItemCodeKey(String itemId) {
        return "item:" + itemId + ":hashCode";
    }

    public static String getUserHistoryKeyWithSession(long userId, String sessionId, Long siteId) {
        return String.format("userhistorywithsession:%s:%s:%s:zset", userId, sessionId, siteId);
    }

    public static String getUserHistoryKeyWithExpireTime(Long userId, Long siteId) {
        return String.format("userhistorywithexpiretime:%s:%s:zset", userId, siteId);
    }

    public static String getUserOpHistoryKeyWithExpireTime(String userId, Long siteId) {
        return String.format("userophistorywithexpiretime:%s:%s:zset", userId, siteId);
    }

    public static String getUserOpHistoryKey(String userId, Long siteId) {
        return String.format("userophistory:%s:%s:value", userId, siteId);
    }

    /**
     * 获取业务频道的redis key
     * @param appId
     * @return
     */
    public static String getBuinessChannelKey(String appId) {
        return String.format(BUSINESS_CHANNEL_KEY, appId);
    }
    public static String getBusinessSiteKey(String appId) {
        return String.format(BUSINESS_SITE_KEY, appId);
    }

    /**
     * 获取业务频道保存的book的redis key
     * @param siteId
     * @param bookId
     * @return
     */
    public static String getBusinessSaveRecBookKey(Long siteId,Long bookId) {
        return String.format(BUSINESS_SAVE_REC_BOOK_KEY, siteId,bookId);
    }
    public static String getBusinessSyncThirdBookKey(String appId,Long bookId) {
        return String.format(BUSINESS_SYNC_THIRD_BOOK_KEY, appId,bookId);
    }

    public static String getBusinessFeedSessionSiteUserIdKey(Long siteId,Long userId) {
        return String.format(BUSINESS_FEED_SESSION_SITE_USERID_KEY, siteId,userId);
    }
    public static String getBusinessOpItemKey(Long siteId) {
        return String.format(BUSINESS_OP_ITEM_KEY, siteId);
    }

}
