package com.letu.solutions.core.transaction;

import com.letu.solutions.core.exception.ThrowException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 事务处理类
 *
 * <AUTHOR>
 * @date 2020/10/26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransactionalManage {

    private final TransactionTemplate transactionTemplate;

    /**
     * 事务管理
     *
     * @param consumer 需要被事务处理的代码块
     * @return true-业务逻辑执行成功,false-务逻辑执行失败
     */
    @SuppressWarnings("all")
    public boolean execute(Consumer consumer) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                boolean isRollback = consumer.accept();
                if (!isRollback) {
                    transactionStatus.setRollbackOnly();
                    return false;
                }
                return true;
            } catch (Exception e) {
                log.error("事务管理,业务逻辑发生异常", e);
                transactionStatus.setRollbackOnly();
                return false;
            }
        });
    }

    /**
     * 事务管理: 报警告, 并且会抛出异常
     *
     * @param consumer 需要被事务处理的代码块
     * @return true-业务逻辑执行成功,false-务逻辑执行失败
     */
    @SuppressWarnings("all")
    public boolean executeWithException(Consumer consumer) throws Exception {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                boolean isRollback = consumer.accept();
                if (!isRollback) {
                    transactionStatus.setRollbackOnly();
                    return false;
                }
                return true;
            } catch (Exception e) {
                log.warn("事务管理,业务逻辑发生异常", e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });
    }


    /**
     * 事务管理: 报警告, 并且会抛出异常，并可处理异常数据
     *
     * @param consumer 需要被事务处理的代码块
     * @return true-业务逻辑执行成功,false-务逻辑执行失败
     */
    @SuppressWarnings("all")
    public boolean executeWithException(Consumer consumer, ExceptionRunner exceptionRunner) throws Exception {
        try {
            return transactionTemplate.execute(transactionStatus -> {
                try {
                    boolean isRollback = consumer.accept();
                    if (!isRollback) {
                        transactionStatus.setRollbackOnly();
                        return false;
                    }
                    return true;
                } catch (Exception e) {
                    log.warn("事务管理,业务逻辑发生异常", e);
                    transactionStatus.setRollbackOnly();
                    throw e;
                }
            });
        } catch (Exception e) {
            exceptionRunner.run(e);
            throw e;
        }
    }

    public interface ExceptionRunner {
        void run(Exception e);
    }

    /**
     * 延迟处理,事务提交后执行
     *
     * @param consumer 需要被事务提交后的处理的代码块
     */
    public void afterCommit(Function consumer) {
        if (!TransactionSynchronizationManager.isActualTransactionActive()) {
            throw new ThrowException("#事务延迟处理,必须存在于事务中");
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                consumer.apply();
            }
        });
    }
}
