package com.letu.solutions.core.enums;

/**
 * <AUTHOR>
 * @ClassName ExceptionEnum
 * @Description: 异常枚举类
 * @Date 2019/10/18
 * @Version V1.0
 **/
public interface ExceptionEnum {
    /**
     * 成功
     */
    int SUCCESS = 0;
    /**
     * 失败
     */
    int FAIL = 1;
    /**
     * 金币兑换汇率不匹配
     */
    int GOLD = 10;
    /**
     * 登录失效
     */
    int TOKEN_ERROR = 401;
    /**
     * 签名校验失败
     */
    int SIGN_ERROR = 402;
    /**
     * 访问受限
     */
    int AUTH_ERROR = 403;
    int USER_AUTH_ERROR = 405;
    int USER_YIBAO_ERROR = 406;
    /**
     * 主动抛出异常（一般为参数校验失败，或流程校验不通过等）
     */
    int RUN_EXCEPTION = -1;
    /**
     * 黑名单
     */
    int BLACK = -10;
    /**
     * 无权限访问
     */
    int NOT_PERMISSION = -20;
    /**
     * 请求超时
     */
    int TIMEOUT = -30;
    /**
     * 访问次数受限
     */
    int VISIT_OUT = -40;
    /**
     * 资源不存在
     */
    int NOT_FOUND = 404;
    /**
     * 锁单中无法下架
     */
    int LOCK_ORDER_NOT_DOWN = -50;
}
