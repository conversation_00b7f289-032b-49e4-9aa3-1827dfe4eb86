package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@RefreshScope
@Data
public class RefreshConfiguration {
    @Value("${project.name}")
    private String projectName;

    @Value("${project.defaultCode: cdhd}")
    private String defaultCode;
    /**
     * geoDB库阿里云内网下载地址
     */
    @Value("${geo.oss.path:https://tzgcxks.oss-cn-hangzhou.aliyuncs.com/share/GeoLite2-City.mmdb}")
    private String databaseOssPath;

    /**
     * 渠道异地开关
     * 注：线上测试使用
     */
    @Value("${channel.offsite:}")
    private String channelOffsite;

    /**
     * 渠道资料只筛选三项
     * 注：线上测试使用
     */
    @Value("${channel.three:}")
    private String channelThree;
    /**
     * 刷单风控开关
     */
    @Value("${login.orderCheck:false}")
    private Boolean orderCheck;
    /**
     * 回调url
     */
    @Value("${login.orderCheckUrl:}")
    private String orderCheckUrl;

    @Value("${requestLog.open:false}")
    private Boolean logOpen;
}
