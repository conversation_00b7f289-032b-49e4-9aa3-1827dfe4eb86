package com.letu.solutions.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台枚举
 *
 * <AUTHOR>
 * @date ：Created in 2020/9/28 13:51
 * @modified By：
 */
@AllArgsConstructor
@Getter
public enum OsEnum {
    h5("h5", 1, "1"),
    android("安卓", 2, "2"),
    ios("ios", 3, "3"),
    web("web", 4, "4"),
    macos("macos", 5, "5"),
    /**
     * 小程序
     */
    small_program("small_program",6,"6")
    ;

    private String desc;
    /**
     * 应用类型 用于token校验生成
     */
    private Integer serverType;

    /**
     * 默认渠道id
     */
    private String defaultCodeChannel;
}
