package com.letu.solutions.core.annotation;


import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> 木鱼
 * @date 2019/11/15.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CacheRedis {

    /**
     * 加锁方法的基础key，参数会自动拼接
     *
     * @return
     */
    String value() default "";

    /**
     * 缓存时间
     * 可配置不通启动参数配置缓存时间倍数
     * 单位：秒
     * redis.cache.ratio
     *
     * @return
     */
    int expireTime() default 60;

    /**
     * 加锁时间，默认两秒，可自定义
     * 单位：毫秒
     *
     * @return
     */
    int lockTime() default 2000;
}
