package com.letu.solutions.core.controller;

import com.letu.solutions.core.annotation.Server;
import com.letu.solutions.core.model.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.NetUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: 木鱼
 * @Date: 2021/9/23 14:58
 * @remark:
 */
@RestController
@Slf4j
public class IndexController {
    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    /**
     * 测试
     *
     * @return
     */
    @GetMapping("/index.e")
    @Server(freeCheck = true)
    public R<String> test() {
        String ip = "";
        try {
//            ip = NetUtils.getLocalHost();
//            stringRedisTemplate.opsForValue().set("test", ip);
//            log.info("当前主机ip:{}", ip);
        } catch (Exception e) {
            log.error("基础测试发生异常", e);
        }
        return R.success(ip);
    }
}
