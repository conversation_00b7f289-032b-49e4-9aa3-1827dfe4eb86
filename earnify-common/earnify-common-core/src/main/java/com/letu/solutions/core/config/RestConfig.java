package com.letu.solutions.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2020/6/3
 */
@Configuration
public class RestConfig {
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        // 设置超时10s
        requestFactory.setConnectTimeout(10000);
        // 读取超时5s
        requestFactory.setReadTimeout(5000);
        return new RestTemplate(requestFactory);
    }
}
