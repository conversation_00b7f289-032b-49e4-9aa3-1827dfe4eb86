package com.letu.solutions.core.constant;

import lombok.Getter;

/**
 * Created by Administrator on 2019/10/17.
 */
public interface CacheConstant {

    /**
     * 防止重复请求
     */
    String REPEAT_REQ = "repeat:%s";

    /**
     * 业务防止重复请求 1 业务 2主键
     */
    String REPEAT_BUSINESS_REQ = "repeat:%s:%s";

    /**
     * 客户端短信验证码key
     */
    String adminSms = "admin:sms:%s:%s";

    /**
     * 客户端短信验证码key
     */
    String adminSmsLock = "admin:smsLock:%s:%s";

    /**
     * 客户端短信验证码key
     */
    String cusSms = "customer:sms:%s:%s";
    /**
     * 客户端错误校验key
     */
    String cusSmsLimit = "customer:sms:limit:%s:%s";

    /**
     * 客户端错误校验key
     */
    String timeIdRedisKey = "idKey:%s:%s";

    /**
     * id自增器key
     */
    String idRedisKey = "idKey:%s";

    /**
     * 客户端错误校验key 第一段是业务 第二段是手机号 值为uuid
     */
    String smsCheckKey = "sms:check:%s:%s";

    String baseKey = "cache:baseKey";

    String nftHomePage = "cache:nft:homePage";
    String nftSellList = "cache:nft:sellList";
    String accountSellList = "cache:account:sellList";
    String accountMinSellList = "cache:accountMin:sellList";
    String nftResellList = "cache:nft:resellList";

    String certKey = "cache:cert:userCert:%s";

    String eventKey = "lock:event:userEvent:%s";

    /**
     * 三方登录小程序sessionKey
     */
    String THIRD_LOGIN_SESSION_KEY = "third:mini:sessionKey:%s:%s";
    /**
     * 三方小程序accessTokenKey
     */
    String THIRD_APP_GLOBAL_ACCESS_TOKEN_KEY = "third:app:accessToken:%s";
    /**
     * 订单支付状态
     */
    String ORDER_PAY_STATUS_KEY = "cache:orders:payStatus:%s";
    /**
     * 订单支付回调锁
     */
    String ORDER_PAY_CALLBACK_LOCK_KEY = "lock:order:pay:callback:%s";

    /**
     * 拉起支付限流业务KEY
     */
    String ORDER_LOAD_PAY_RATELIMIT_KEY = "order_load_pay:";
    /**
     * 创建限流业务KEY
     */
    String ORDER_CREATE_RATELIMIT_KEY = "order_create:";

    /**
     * 用户详情缓存
     */
    String CUSTOMER_USER_INFO_CACHE_KEY = "customer:info:%s";

    /**
     * 用户书籍解锁记录
     */
    String USER_BOOK_UNLOCK_RECORD_KEY = "user:book:unlock:record:%s:%s:%s";
    /**
     * 用户激励广告观看
     */
    String USER_AD_REWARD_KEY = "user:ad:reward:%s";

    /**
     * 用户渠道资源配置
     */
    String USER_SITE_RESOURCE_CONF_KEY = "user:site:resource:conf:%s:%s:%s";

    /**
     * 邮件验证码
     */
    String emailCode = "customer:emailCode:%s";
    /**
     * 邮件验证码类型
     */
    String EMAIL_CODE_CER = "EMAIL_CODE_CER";
    /**
     * 短信验证码类型
     */
    @Getter
    enum SMSType {
        SMS_COUNT_60_S("%s_%s:SMS_SEND_COUNT_60_S:%s"),
        SMS_COUNT_10_M("%s_%s:SMS_SEND_COUNT_10_M:%s"),
        SMS_COUNT_CURRENT_D("%s_%s:SMS_SEND_COUNT_CURRENT_D:%s"),
        ;
        private String name;

        SMSType(String CUSTOMERLogin) {
            this.name = CUSTOMERLogin;
        }
    }
}
