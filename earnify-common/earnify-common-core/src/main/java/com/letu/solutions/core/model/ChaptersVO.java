package com.letu.solutions.core.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version Id: ChaptersVO.java, v 0.1 2025/3/28 15:56 lai_kd Exp $$
 */
@Data
public class ChaptersVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 卷序号
     */
    private Long volumeSeq;
    /**
     * 卷名称
     */
    private String volumeName;
    /**
     * 章节序号
     */
    private Long chapterNumber;
    /**
     * 章节名称
     */
    private String chapterName;
    /**
     * 章节内容文件路径
     */
    private String contentFile;
}
