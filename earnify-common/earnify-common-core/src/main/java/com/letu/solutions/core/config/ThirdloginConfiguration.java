package com.letu.solutions.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: jack ma
 * @CreateTime: 2025-05-13  18:33
 * @Version: 1.0
 */
@Data
@RefreshScope
@Configuration
public class ThirdloginConfiguration {

//    @Value("${thirdlogin.apple.client_id}")
//    private String appleClientId;
//
//    @Value("${thirdlogin.apple.team_id}")
//    private String appleTeamId;
//
//    @Value("${thirdlogin.apple.key_id}")
//    private String appleKeyId;
//
//    @Value("${thirdlogin.apple.private_key}")
//    private String applePrivateKey;

    @Value("${thirdlogin.google.h5.client_id}")
    private String googleClientH5;

    @Value("${thirdlogin.google.android.client_id}")
    private String googleClientAndroid;
//
//    @Value("${thirdlogin.twitter.clientId}")
//    private String twitterClientId;
//    @Value("${thirdlogin.twitter.clientSecret}")
//    private String twitterClientSecret;
//    @Value("${thirdlogin.twitter.redirectUri}")
//    private String twitterRedirectUri;
}
