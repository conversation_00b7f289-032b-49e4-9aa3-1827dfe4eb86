package com.letu.solutions.core.constant;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 2020-03-24
 * @version 1.0.0
 *
 * 数据操作类型常量类
 */
@Slf4j
public final class OpTypeConst {

    /**
     * 删除
     */
    public static final String DELETE = "DELETE";

    /**
     * 更新
     */
    public static final String UPDATE = "UPDATE";

    /**
     *  更新/新增
     */
    public static final String UPSERT = "UPSERT";

    /**
     * 新增
     */
    public static final String INSERT = "INSERT";


    private OpTypeConst() {
    }

    public static String parse(String opType) {
        String stdOpType = null;
        switch (opType) {
            case INSERT:
                stdOpType = INSERT;
                break;
            case UPDATE:
                stdOpType = UPDATE;
                break;
            case DELETE:
                stdOpType = DELETE;
                break;
            case UPSERT:
                stdOpType = UPSERT;
                break;
            default:
        }
        if (StrUtil.isBlank(stdOpType)) {
            log.error("unknown operation {}", opType);
            stdOpType = opType;
        }
        return stdOpType;
    }
}
