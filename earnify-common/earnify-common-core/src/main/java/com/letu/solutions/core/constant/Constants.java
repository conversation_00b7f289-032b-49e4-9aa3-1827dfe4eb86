package com.letu.solutions.core.constant;

public class Constants {
    public final static String PROD = "prod";

    public static final String UNKNOWN = "未知";

    public static final String UNKNOWN_EN = "unknown";

    public static final String NOTIFICATION = "notification";

    public static final String REAL_A = "a";

    public static final String P = "p";

    public static final String CHANNEL = "channel:%s";

    public static final String Limit_1 = "LIMIT 1";

    public static final int BUSINESS_SUCCESS = 1;
    public static final int BUSINESS_FAIL = 2;

    public static final String UNIVERSAL_CODE = "123456";

    public static final int GREEN_SUCCESS_CODE = 200;
    // 数字0
    public static final int ZERO = 0;
    // 数字1
    public static final int NUMBER_1 = 1;

    //discovery namespace
    public static final String NACOS_DISCOVERY_NAMESPACE = "spring.cloud.nacos.discovery.namespace";

    public static final String IP_CITY_LANG = "en";

    /**
     * 文件类型
     */
    public interface FILE_CONSTANT {
        /**
         * 视频类别
         */
        String[] VIDEO_SUFFIX = {"avi", "mov", "rmvb", "rm", "flv", "mp4", "3gp", "wmv", "swf", "ipod", "psp", "unity3d"};

        /**
         * 图片类别
         */
        String[] IMG_SUFFIX = {"jpg", "png", "gif", "jpeg", "webp", "ico", "unity3d"};

        /**
         * 音频文件
         */
        String[] VOICE_SUFFIX = {"mp3", "wav", "flac", "aac", "ogg", "wma"};
    }

    public static final String LANGUAGE_HEADER = "language";

    // 定义逗号分隔符，用于字符串拼接或其他需要分隔符的场景
    public static final String COMMA = ",";

    public static final String PIPE_SYMBOL = "\\|";

    public enum Header {
        userId,
        ipAddress,
        os,
        businessType,
        version,
        codeChannel
    }

    public static final String AB_SIDE_A = "A";

    public static final String AB_SIDE_B = "B";

    public static final String KINK_CUSTOMER = "kink_customer";

    public static final String IGNORE_UPLOAD = "upload";
    public static final String IGNORE_INDEX = "index.e";
    public static final String IGNORE_IMAGEFILE = "imageFile";
}
