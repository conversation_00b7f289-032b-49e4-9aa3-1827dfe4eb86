package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "register")
@RefreshScope
@Configuration
@Data
public class RegisterConfiguration {
    private List<String> userImgUrls;
}
