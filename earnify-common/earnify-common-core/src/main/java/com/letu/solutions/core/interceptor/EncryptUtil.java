package com.letu.solutions.core.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

/**
 * 对称加密解密工具
 */
public class EncryptUtil {
    private final static AES aes = SecureUtil.aes("JOZ9lRfnUaJRH8G9".getBytes());
    private final static String pre = "BJT-";

    public static boolean isEncrypt(String param) {
        return StrUtil.isNotEmpty(param) && param.startsWith(pre);
    }

    public static String encrypt(String param) {
        if (StrUtil.isEmpty(param) || param.startsWith(pre)) {
            return param;
        }
        return pre + aes.encryptBase64(param);
    }

    public static String decrypt(String param) {
        if (StrUtil.isEmpty(param) || !param.startsWith(pre)) {
            return param;
        }
        return aes.decryptStr(param.replace(pre, ""));
    }
}
