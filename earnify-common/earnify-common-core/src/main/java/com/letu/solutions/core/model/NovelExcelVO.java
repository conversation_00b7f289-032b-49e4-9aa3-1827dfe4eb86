package com.letu.solutions.core.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version Id: NovelExcelVO.java, v 0.1 2025/3/28 11:31 lai_kd Exp $$
 */
@Data
public class NovelExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 书名
     */
    private String title;
    /**
     *  描述
     */
    private String description;
    /**
     * 作者
     */
    private Long author;
    /**
     * 书籍类型
     */

    private String bookType;
    /**
     * 二级分类
     */
    private String categoryTwo;
    /**
     * 三级分类
     */
    private String categoryThree;
    /**
     * 章节内容
     */
    private String contentFile;
    /**
     * 男频、女频
     */
    private String bookAudienceType;
    /**
     * 书籍状态
     */
    private String serialStatus;
    /**
     * 书籍更新频率
     */
    private String updateFrequency;
    /**
     * 特高风险‌（4）
     * ‌高风险‌（3）
     * ‌中等风险‌（2）
     * ‌低风险‌：（1）
     * ‌无风险‌（0）
     */
    private Integer riskLevel;
}
