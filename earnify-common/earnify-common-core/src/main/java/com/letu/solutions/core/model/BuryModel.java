package com.letu.solutions.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BuryModel implements Serializable {

    private static final long serialVersionUID = -6629236424516460212L;
    private String tableName;
    private String insertSql;
    private List<Object> params;
}
