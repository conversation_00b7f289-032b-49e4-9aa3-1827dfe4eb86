package com.letu.solutions.core.config;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.util.TimeZone;

/**
 * @description 解决高版本jdk timezone序列化错误的适配器
 * <AUTHOR>
 * @createTime 2025/6/5 17:19
 */
public class TimeZoneAdapter extends TypeAdapter<TimeZone> {
    @Override
    public void write(JsonWriter out, TimeZone value) throws IOException {
        if (value == null) {
            out.nullValue();
        } else {
            // 使用公共 API 获取时区 ID
            out.value(value.getID());
        }
    }

    @Override
    public TimeZone read(Json<PERSON>eader in) throws IOException {
        String id = in.nextString();
        if (id == null || id.isEmpty()) {
            return null;
        }
        // 使用公共 API 创建 TimeZone
        return TimeZone.getTimeZone(id);
    }
}
