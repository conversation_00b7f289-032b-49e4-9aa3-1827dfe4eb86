package com.letu.solutions.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Slf4j
public class Authentication implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * token uuid
     */
    private String uuid;

    /**
     * 端
     */
    private String platform;

    /**
     * 客户端用户信息
     */
    private UserInfo userInfo;

    /**
     * 管理端用户信息
     */
    private CmsUserInfo cmsUserInfo;
}
