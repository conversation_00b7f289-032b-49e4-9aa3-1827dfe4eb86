package com.letu.solutions.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoticeModel {
    /**
     * 模板code
     */
    private String code;
    private Map<String, String> param;
    /**
     * 是否批量通知
     */
    private boolean batch = false;
    /**
     * 创建时间（mq延迟对照使用）
     */
    private String createTime;
    /**
     * 延时短信发送时间 yyyyMMddHHmm
     */
    private String sendTime;
}
