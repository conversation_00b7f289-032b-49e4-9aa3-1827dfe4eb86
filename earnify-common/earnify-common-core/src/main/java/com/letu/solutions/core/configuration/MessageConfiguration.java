package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@RefreshScope
@Configuration
@Data
public class MessageConfiguration {

    /**
     * 阿里云短信
     */
    @Value(value = "${aliyun.sms.accessKeyId:}")
    private String aliyunAccessKeyId;
    @Value(value = "${aliyun.sms.accessKeySecret:}")
    private String aliyunAccessSecret;
    @Value(value = "${aliyun.sms.signName:}")
    private String aliyunAccessSign;

    /**
     * 邮箱配置相关
     */
    @Value(value = "${email.host:}")
    private String emailHost;
    @Value(value = "${email.port:}")
    private Integer emailPort;
    @Value(value = "${email.from:}")
    private String emailFrom;
    @Value(value = "${email.user:}")
    private String emailUser;
    @Value(value = "${email.pass:}")
    private String emailPass;

    /**
     * 极光
     */
    @Value(value = "${jpush.cl:}")
    private String jpushCl;
    @Value(value = "${jpush.fl:}")
    private String jpushFl;
    @Value(value = "${jpush.prod:true}")
    private boolean jpushProd;
    @Value(value = "${jpush.keep:}")
    private Integer jpushKeepTime;

    /**
     * 创蓝
     */
    @Value(value = "${cl.url:}")
    private String clUrl;
    @Value(value = "${cl.account:}")
    private String clAccount;
    @Value(value = "${cl.password:}")
    private String clPassword;

    /**
     * 百物
     */
    @Value(value = "${bw.corp_id:}")
    private String bwCorpId;
    @Value(value = "${bw.corp_pwd:}")
    private String bwCorpPwd;
    @Value(value = "${bw.corp_service:}")
    private String bwCorpService;

}
