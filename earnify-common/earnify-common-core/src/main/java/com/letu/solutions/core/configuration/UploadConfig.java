package com.letu.solutions.core.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @ClassName UploadConfig
 * @Description: oss 配置
 * <AUTHOR>
 * @Date 2019/10/18
 * @Version V1.0
 **/
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "oss")
public class UploadConfig {
    private String awsS3Url;
    private String awsAccessKey;
    private String awsSecretKey;
    private String baseUrl;
    /**
     * 内网域名
     */
    private String innerBaseUrl;
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String imgPrefix;
    private String videoPrefix;
    private String mediaPrefix;
    private Long imgMaxSize;
    private Long txtMaxSize;
    private Long excelMaxSize;
    private Long docMaxSize;
    private Long pdfMaxSize;
    private Long videoMaxSize;
    private String apkPrefix;
    private String txtPrefix;
    private String excelPrefix;
    private String docPrefix;
    private String jPushPrefix;
    private String pdfPrefix;
    private String messageBackPrefix;
    private String domain;
}
