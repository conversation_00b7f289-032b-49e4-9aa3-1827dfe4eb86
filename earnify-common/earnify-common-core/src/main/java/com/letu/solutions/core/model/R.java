package com.letu.solutions.core.model;

import com.letu.solutions.core.utils.LanguageUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 响应
 *
 * @param <T>
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final String successMsg = "success";
    public static final int CODE_SUCCESS = 0;
    public static final int CODE_FAILURE = 1;

    /**
     * 响应码 0成功 1失败
     */
    private int code = CODE_SUCCESS;
    /**
     * 响应数据
     */
    private T data;
    /**
     * 响应信息
     */
    private String message;

    public static <T> R<T> success() {
        R<T> result = new R<>();
        result.message = successMsg;
        return result;
    }

    @SuppressWarnings("rawtypes")
    public static <T> R<T> success(T data) {
        R<T> result = R.success();
        result.setData(data);
        return result;
    }
    @SuppressWarnings("rawtypes")
    public static <T> R<T> success(T data, String message) {
        R<T> result = R.success();
        result.setData(data);
        result.message = LanguageUtil.trans(message);
        return result;
    }

    public static <T> R<T> fail(String message) {
        return fail(1, message);
    }

    public static <T> R<T> fail(int code, String message) {
        return fail(code, message, null);
    }

    public static <T> R<T> fail(int code, String message, T data) {
        R<T> result = new R<>();
        result.setMessage(LanguageUtil.trans(message));
        result.setCode(code);
        result.setData(data);
        return result;
    }

    public boolean isOk() {
        return this.code == CODE_SUCCESS;
    }

}
