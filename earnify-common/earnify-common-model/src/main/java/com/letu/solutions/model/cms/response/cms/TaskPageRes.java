package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import lombok.Data;
import java.io.Serializable;
import com.letu.solutions.model.response.customer.PartyUserInfoRes;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 任务属性 1人工 2自动
     */
    private Integer attribute;
    /**
     * 发布者id
     */
    private Long userId;
    /**
     * 发布者名称
     */
    private String nickName;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 是否排重 1排重 2不排重
     */
    private Integer weightSorting;
    /**
     * 任务类型 枚举
     */
    private TaskTypeEnum taskType;
    /**
     * 任务有效期 天数
     */
    private Integer time;
    /**
     * 任务状态 枚举
     */
    private TaskStatusEnum state;
    /**
     * 保证金
     */
    private Integer bond;
    /**
     * 任务数量
     */
    private Integer number;
    /**
     * 任务领取个数
     */
    private Integer taskReceiveNum;
    /**
     * 任务完成个数
     */
    private Integer taskFinishNum;
    /**
     * 单价格
     */
    private BigDecimal price;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 冻结中金额
     */
    private BigDecimal frozenAmount;
    /**
     * 已发放
     */
    private BigDecimal grantedPoints;
    /**
     * 已发放金额
     */
    private BigDecimal grantedAmount;
    /**
     * 甲方用户信息
     */
    private PartyUserInfoRes partyUserInfo;
}
