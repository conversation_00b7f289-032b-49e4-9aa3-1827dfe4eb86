package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import com.letu.solutions.model.enums.cms.ProductStatusEnum;
import com.letu.solutions.model.enums.cms.ProductTypeEnum;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 产品表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class ProductSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品名称
     */
    @NotEmpty(message = "产品名称不可为空")
    private String productName;
    /**
     * 产品logo的URL
     */
    private String logo;
    /**
     * 主图
     */
    private String hostUrl;
    /**
     * 甲方用户ID
     */
    @NotNull(message = "甲方用户ID不可为空")
    private Long partyUserId;
    /**
     * 产品类型
     */
    @NotNull(message = "产品类型不可为空")
    private ProductTypeEnum productType;

}
