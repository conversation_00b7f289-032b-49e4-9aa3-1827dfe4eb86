package com.letu.solutions.model.response.userdetail;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 用户详情表
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
public class UserDetailDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户详情唯一标识符
     */
    private Long id;
    /**
     * 关联的用户ID
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String name;
    /**
     * 性别[SexEnum]
     */
    private String gender;
    /**
     *
     */
    private String imgUrl;
    /**
     * 最初登录时间
     */
    private Date firstLoginTime;
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;
    /**
     * 最后离线时间[有Im业务才有]
     */
    private Date lastLeaveTime;
    /**
     * 最初登录平台
     */
    private String lastLoginPlatform;
    /**
     * 注册平台
     */
    private String registerPlatform;
    /**
     * 真实姓名
     */
    private String realName;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 身份证正面
     */
    private String idCardFront;
    /**
     * 身份证反面
     */
    private String idCardBack;
    /**
     * 生日
     */
    private Date birthday;
    /**
     * 身高
     */
    private Long height;
    /**
     * 重量
     */
    private Long weight;
    /**
     * 介绍
     */
    private String intro;
}
