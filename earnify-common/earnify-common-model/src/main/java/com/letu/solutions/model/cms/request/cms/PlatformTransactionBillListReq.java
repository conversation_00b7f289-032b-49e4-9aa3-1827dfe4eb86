package com.letu.solutions.model.cms.request.cms;

import com.alipay.api.domain.Account;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 平台账户流水
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlatformTransactionBillListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 动账ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String userName;
    /**
     * 账号类型（甲方账户，乙方账户）
     */
    private AccountTypeEnum accountType;
    /**
     * 动账类型（充值，提现）
     */
    private FundTypeEnum fundType;

    /**
     * 协议网络（如 ERC20, TRC20, BEP20）
     */
    private String network;

}
