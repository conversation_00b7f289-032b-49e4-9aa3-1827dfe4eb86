package com.letu.solutions.model.cms.request.user;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description 用户启用/禁用修改入参
 * <AUTHOR>
 * @createTime 2025/4/10 10:35
 */
@Data
public class UserEnableReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @NotNull(message = "$ID不可为空")
    private Long id;

    /**
     * 状态[1:启用;0:禁用]
     */
    @NotNull(message = "$请选择启用/禁用")
    @Min(value = 0, message = "错误的用户状态：0")
    @Max(value = 1, message = "错误的用户状态：1")
    private Integer enable;
}
