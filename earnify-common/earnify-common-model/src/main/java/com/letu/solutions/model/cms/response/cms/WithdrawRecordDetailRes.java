package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 提现记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class WithdrawRecordDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID，提现记录ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 提现状态（0未处理，1未通过，2已打款）
     */
    private Integer withdrawStatus;
    /**
     * 账户类型（0甲方账户，1乙方账户）
     */
    private Integer accountType;
    /**
     * 操作类型（0用户操作，1平台操作）
     */
    private Integer actionType;
    /**
     * 协议网络（如 ERC20, TRC20）
     */
    private String network;
    /**
     * 提现数量
     */
    private BigDecimal amount;
    /**
     * 币种（如 USDT, ETH）
     */
    private String tokenSymbol;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 平台付款钱包地址
     */
    private String fromAddress;
    /**
     * 用户收款钱包地址
     */
    private String toAddress;
    /**
     * 链上交易哈希（Txn Hash）
     */
    private String txHash;
    /**
     * 截图凭证，最多支持3张图片URL
     */
    private String evidenceImages;
    /**
     * 处理人员（管理员）
     */
    private String operator;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 处理完成时间
     */
    private Date finishTime;
}
