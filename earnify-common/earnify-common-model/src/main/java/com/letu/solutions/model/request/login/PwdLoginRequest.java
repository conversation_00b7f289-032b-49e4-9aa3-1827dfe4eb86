package com.letu.solutions.model.request.login;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class PwdLoginRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    @NotEmpty(message = "$手机号不可为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;

    /**
     * 密码
     */
    @NotEmpty(message = "$密码")
    @Size(min = 6, max = 16, message = "$请输入6-16位密码")
    private String password;
}
