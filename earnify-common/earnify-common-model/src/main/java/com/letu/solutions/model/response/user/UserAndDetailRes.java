package com.letu.solutions.model.response.user;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户和用户Detail信息
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserAndDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID 用户id
     */
    private Long id;

    /**
     * 手机号码
     */
    private String userPhone;

    /**
     * 用户创建时间
     */
    private Date createTime;
}
