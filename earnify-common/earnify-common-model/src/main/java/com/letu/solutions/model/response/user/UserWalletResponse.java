package com.letu.solutions.model.response.user;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户钱包绑定响应
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class UserWalletResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 钱包绑定列表
     */
    private List<WalletBinding> walletBindings;

    /**
     * 钱包绑定项
     */
    @Data
    public static class WalletBinding implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 钱包ID
         */
        private Long id;

        /**
         * 钱包类型
         */
        private String walletType;

        /**
         * 钱包类型描述
         */
        private String walletTypeDesc;

        /**
         * 钱包地址
         */
        private String walletAddress;

        /**
         * 是否默认钱包
         */
        private Boolean isDefault;

        /**
         * 绑定时间
         */
        private Date bindTime;
    }
} 