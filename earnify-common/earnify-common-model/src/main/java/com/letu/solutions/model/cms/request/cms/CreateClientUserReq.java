package com.letu.solutions.model.cms.request.cms;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;


import java.io.Serializable;

/**
 * 新增甲方用户请求
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class CreateClientUserReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 邮箱（必填）
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 密码（必填）
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String password;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户简介
     */
    private String userDescription;

    /**
     * Twitter账号
     */
    private String twitterUrl;

    /**
     * Telegram账号
     */
    private String telegramUrl;

    /**
     * Discord账号
     */
    private String discordUrl;

    /**
     * Reddit账号
     */
    private String redditUrl;

    /**
     * TikTok账号
     */
    private String tiktokUrl;

    /**
     * Medium账号
     */
    private String mediumUrl;
}
