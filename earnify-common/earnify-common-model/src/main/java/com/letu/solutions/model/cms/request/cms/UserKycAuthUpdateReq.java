package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * KYC身份认证表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserKycAuthUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不可为空")
    private Long id;
    /**
     * 用户ID，关联user表
     */
    @NotNull(message = "用户ID，关联user表不可为空")
    private Long userId;
    /**
     * 证件类型：1=身份证，2=护照，3=驾驶执照
     */
    @NotNull(message = "证件类型：1=身份证，2=护照，3=驾驶执照不可为空")
    private Integer idType;
    /**
     * 证件号码
     */
    @NotEmpty(message = "证件号码不可为空")
    private String idNumber;
    /**
     * 证照图片数组
     */
    @NotEmpty(message = "证照图片数组不可为空")
    private String documentImages;
    /**
     * 提交时间
     */
    @NotNull(message = "提交时间不可为空")
    private Date submitTime;
    /**
     * 审核状态：0待审核，1已通过，2未通过
     */
    @NotNull(message = "审核状态：0待审核，1已通过，2未通过不可为空")
    private Integer verifyStatus;
    /**
     * 审核时间
     */
    private Date verifyTime;
    /**
     * 审核备注或失败原因
     */
    private String remark;
}
