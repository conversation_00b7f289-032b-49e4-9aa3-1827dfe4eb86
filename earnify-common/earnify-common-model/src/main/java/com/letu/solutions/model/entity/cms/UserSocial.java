package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.letu.solutions.model.enums.cms.SocialBindStatusEnum;
import com.letu.solutions.model.enums.cms.SocialTypeEnum;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户社交媒体绑定信息表
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@TableName("user_social")
@Builder
public class UserSocial implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 用户ID，关联user表
    */
    private Long userId;

    /**
    * 社交媒体类型
    */
    private SocialTypeEnum socialType;

    /**
    * 社交媒体链接
    */
    private String socialUrl;

    /**
    * 绑定状态：pending-待验证，verified-已验证，failed-验证失败
    */
    private SocialBindStatusEnum status;

    /**
    * 验证时间
    */
    private Date verifyTime;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;
}
