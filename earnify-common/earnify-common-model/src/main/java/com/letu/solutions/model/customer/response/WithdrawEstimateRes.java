package com.letu.solutions.model.customer.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预计到账响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WithdrawEstimateRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提现金额
     */
    private BigDecimal withdrawAmount;

    /**
     * 手续费率
     */
    private BigDecimal feeRate;

    /**
     * 手续费金额
     */
    private BigDecimal feeAmount;

    /**
     * 每笔提现费用
     */
    private BigDecimal perWithdrawFee;

    /**
     * 预计到账金额
     */
    private BigDecimal estimatedAmount;

    /**
     * 协议网络
     */
    private String network;
}
