package com.letu.solutions.model.response.user;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 已完成用户任务响应
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class CompletedUserTaskResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发布者（甲方账户昵称）
     */
    private String publisher;

    /**
     * 任务标题
     */
    private String taskTitle;

    /**
     * 完成时间
     */
    private Date completedTime;

    /**
     * 奖励积分
     */
    private BigDecimal rewardPoints;
} 