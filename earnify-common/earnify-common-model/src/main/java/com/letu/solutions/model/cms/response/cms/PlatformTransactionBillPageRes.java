package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.ActionTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import lombok.Data;
import java.io.Serializable;

/**
 * 平台账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class PlatformTransactionBillPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 动账ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String userName;
    /**
     * 账号类型（甲方账户，乙方账户）
     */
    private AccountTypeEnum accountType;
    /**
     * 动账类型（充值，提现）
     */
    private FundTypeEnum fundType;

    /**
     * 方向（入账，出账）
     */
    private FundSideTypeEnum side;
    /**
     * 动账数量
     */
    private BigDecimal amount;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 变动前金额
     */
    private BigDecimal balanceBefore;
    /**
     * 变动后金额
     */
    private BigDecimal balanceAfter;
    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 协议网络（如 ERC20, TRC20, BEP20）
     */
    private String network;

    /**
     * 平台钱包地址
     */
    private String platformWalletAddress;

    /**
     * 操作人员
     */
    private String operator;

}
