package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 申述记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppealRecordListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 甲方用户ID
     */
    private Long partyAUserId;
    /**
     * 甲方用户名
     */
    private String partyAUsername;
    /**
     * 乙方用户ID
     */
    private Long partyBUserId;
    /**
     * 乙方用户名
     */
    private String partyBUsername;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 开始时间
     */
    private String staTime;
    /**
     * 结束时间
     */
    private String finishTime;
}
