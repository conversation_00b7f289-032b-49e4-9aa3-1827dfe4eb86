package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import lombok.Data;

/**
 * 用户接收任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("user_task")
public class UserTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 产品自增ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 任务id
    */
    private Long taskId;

    /**
    * 用户id
    */
    private Long userId;

    /**
    * 任务状态 枚举
    */
    private UserTaskStatusEnum state;

    /**
    * 任务描述
    */
    private String taskDescribe;
    /**
    * 单价
    */
    private BigDecimal price;

    /**
    * 任务到期时间
    */
    private Date time;
    /**
     * 申诉到期时间
     */
    private Date appealTime;
    /**
     * 任务审核时间
     */
    private Date examineTime;
    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

}
