package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.letu.solutions.model.enums.cms.WalletTypeEnum;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户钱包绑定信息表
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@TableName("user_wallet")
public class UserWallet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 用户ID，关联user表
    */
    private Long userId;

    /**
    * 钱包类型
    */
    private WalletTypeEnum walletType;

    /**
    * 钱包地址
    */
    private String walletAddress;

    /**
    * 是否默认钱包：0-否，1-是
    */
    private Integer isDefault;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;
} 