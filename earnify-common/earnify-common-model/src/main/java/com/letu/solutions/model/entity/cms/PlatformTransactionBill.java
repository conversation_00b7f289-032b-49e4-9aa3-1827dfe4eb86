package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import lombok.Data;

/**
 * 平台账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("platform_transaction_bill")
public class PlatformTransactionBill implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 动账ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 用户ID
    */
    private Long userId;

    /**
    * 用户昵称
    */
    private String userName;

    /**
    * 账号类型（甲方账户，乙方账户）
    */
    private AccountTypeEnum accountType;

    /**
    * 动账类型（充值，提现）
    */
    private FundTypeEnum fundType;

    /**
    * 方向（入账，出账）
    */
    private FundSideTypeEnum side;

    /**
    * 动账数量
    */
    private BigDecimal amount;

    /**
    * 币种（如 USDT、ETH）
    */
    private String currency;

    /**
    * 手续费
    */
    private BigDecimal fee;

    /**
    * 变动前金额
    */
    private BigDecimal balanceBefore;

    /**
    * 变动后金额
    */
    private BigDecimal balanceAfter;


    /**
     * 充值或提现关联id
     */
    private Long fundId;

    /**
    * 备注说明
    */
    private String remark;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
     * 平台账户id
     */
    private Long platformUserId;

    /**
     * 日期字段（格式：yyyyMMdd，如：********）
     */
    private Integer day;
}
