package com.letu.solutions.model.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统角色-接口关系
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleInterfaceRel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 全路径接口code
     */
    private String nameAll;


}
