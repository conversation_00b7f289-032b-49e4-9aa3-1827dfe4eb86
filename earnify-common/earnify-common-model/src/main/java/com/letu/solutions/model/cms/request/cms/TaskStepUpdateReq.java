package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 步骤
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskStepUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    @NotNull(message = "产品自增ID不可为空")
    private Long id;
    /**
     * 任务id
     */
    @NotNull(message = "任务id不可为空")
    private Long taskId;
    /**
     * 任务描述
     */
    @NotEmpty(message = "任务描述不可为空")
    private String taskDescribe;
    /**
     * 单价
     */
    @NotNull(message = "单价不可为空")
    private BigDecimal price;
    /**
     * 排序
     */
    @NotNull(message = "排序不可为空")
    private Integer srot;
}
