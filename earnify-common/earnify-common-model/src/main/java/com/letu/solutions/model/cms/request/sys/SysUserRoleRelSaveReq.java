package com.letu.solutions.model.cms.request.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户角色关系入参
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SysUserRoleRelSaveReq implements Serializable {
    private static final long serialVersionUID = 5986083612141188747L;
    /**
     * 用户id
     */
    @NotNull(message = "$用户id不可为空")
    private Long userId;

    /**
     * 角色id
     */
    private List<Long> roleIds;

}
