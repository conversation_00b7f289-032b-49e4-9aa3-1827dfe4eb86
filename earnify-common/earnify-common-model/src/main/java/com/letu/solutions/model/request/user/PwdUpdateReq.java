package com.letu.solutions.model.request.user;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class PwdUpdateReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 密码
     */
    @NotEmpty(message = "$密码不可为空")
    @Size(min = 6, max = 16, message = "$请输入6-16位密码")
    private String pwd;

    /**
     * 手机号 （修改登录密码时必须传，作忘记密码用）
     */
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;
    /**
     * 令牌
     */
    @NotEmpty(message = "$令牌不可为空")
    @Length(min = 32, max = 32, message = "$令牌必须为32位")
    private String uuid;
}
