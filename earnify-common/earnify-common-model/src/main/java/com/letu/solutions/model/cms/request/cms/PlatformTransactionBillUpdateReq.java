package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 平台账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class PlatformTransactionBillUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 动账ID
     */
    @NotNull(message = "动账ID不可为空")
    private Long id;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 用户昵称
     */
    private String userName;
    /**
     * 账号类型（0=甲方账户，1=乙方账户）
     */
    @NotNull(message = "账号类型（0=甲方账户，1=乙方账户）不可为空")
    private Integer accountType;
    /**
     * 动账类型（0=充值，1=提现）
     */
    @NotNull(message = "动账类型（0=充值，1=提现）不可为空")
    private Integer actionType;
    /**
     * 方向（1=入账，2=出账）
     */
    @NotNull(message = "方向（1=入账，2=出账）不可为空")
    private Integer side;
    /**
     * 动账数量
     */
    @NotNull(message = "动账数量不可为空")
    private BigDecimal amount;
    /**
     * 币种（如 USDT、ETH）
     */
    @NotEmpty(message = "币种（如 USDT、ETH）不可为空")
    private String coinSymbol;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 变动前金额
     */
    @NotNull(message = "变动前金额不可为空")
    private BigDecimal balanceBefore;
    /**
     * 变动后金额
     */
    @NotNull(message = "变动后金额不可为空")
    private BigDecimal balanceAfter;
    /**
     * 充值或提现关联id
     */
    private Long fundId;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不可为空")
    private Date createdAt;
    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不可为空")
    private Date updatedAt;
}
