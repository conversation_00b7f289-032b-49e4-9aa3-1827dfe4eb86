package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 步骤
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskStepListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 任务描述
     */
    private String taskDescribe;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 排序
     */
    private Integer srot;
}
