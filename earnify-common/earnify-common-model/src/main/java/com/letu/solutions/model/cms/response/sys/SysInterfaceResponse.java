package com.letu.solutions.model.cms.response.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.letu.solutions.model.enums.InterfaceTypeEnum;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 系统接口
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysInterfaceResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * code类型
     */
    private InterfaceTypeEnum type;

    /**
     * 接口code
     */
    private String name;

    /**
     * 全路径接口code
     */
    private String nameAll;

    /**
     * 接口描述
     */
    private String interfaceDesc;

    /**
     * 全路径接口描述
     */
    private String interfaceAllDesc;

    /**
     * 子集
     */
    private List<SysInterfaceResponse> children;


}
