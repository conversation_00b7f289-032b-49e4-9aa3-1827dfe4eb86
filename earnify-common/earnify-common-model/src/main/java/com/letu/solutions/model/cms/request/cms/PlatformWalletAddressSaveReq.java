package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.dubbo.remoting.http12.rest.Schema;

import java.io.Serializable;

/**
 * 平台钱包地址表
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class PlatformWalletAddressSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 链名称（如 TRC20、ERC20、BEP20）
     */
    @NotEmpty(message = "链名称（如 TRC20、ERC20、BEP20）")
    @Schema(hidden = true)
    private String network;
    /**
     * 平台钱包地址
     */
    @NotEmpty(message = "平台钱包地址不可为空")
    private String address;
}
