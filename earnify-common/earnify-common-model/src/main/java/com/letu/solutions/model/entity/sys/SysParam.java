package com.letu.solutions.model.entity.sys;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 系统参数
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Data
@TableName("sys_param")
public class SysParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 参数名
    */
    private String name;
    /**
     * 参数key
     */
    @TableField(value = "`key`")
    private String key;

    /**
     * 参数值
     */
    private String value;

    /**
     * 参数描述
     */
    @TableField(value = "`describe`")
    private String describe;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改时间
    */
    private Date updateTime;
}
