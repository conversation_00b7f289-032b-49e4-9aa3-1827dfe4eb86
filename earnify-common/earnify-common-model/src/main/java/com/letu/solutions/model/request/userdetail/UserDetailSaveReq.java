package com.letu.solutions.model.request.userdetail;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.validated.annotation.Adult;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 用户详情表
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
public class UserDetailSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 关联的用户ID
     */
    private Long userId;
    /**
     * 用户昵称
     */
    @NotEmpty(message = "$用户昵称不可为空")
    @Size(min = 2, max = 10, message = "$昵称长度必须在2到10个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "$昵称只能包含字母、数字和下划线")
    private String name;
    /**
     * 性别[SexEnum]
     */
    @NotEmpty(message = "$性别[SexEnum]不可为空")
    private String gender;
    /**
     * 注册平台
     */
    @NotEmpty(message = "$注册平台不可为空")
    private String registerPlatform;
    /**
     * 真实姓名
     */
    @NotEmpty(message = "$真实姓名不可为空")
    private String realName;
    /**
     * 生日
     */
    @NotEmpty(message = "$生日不可为空")
    @Adult
    private Date birthday;
    /**
     * 身高
     */
    @NotNull(message = "$身高不可为空")
    private BigDecimal height;
    /**
     * 体重
     */
    @NotNull(message = "$体重不可为空")
    private BigDecimal weight;
    /**
     * 介绍
     */
    private String intro;
}
