package com.letu.solutions.model.validated;

import jakarta.validation.groups.Default;

public interface DingDing extends Default {
    interface Text extends Default {
    }

    interface Link extends Default {
    }

    interface Markdown extends Default {
    }

    interface Whole_ActionCard extends Default {
    }

    interface Independence_ActionCard extends Default {
    }

    interface FeedCard extends Default {
    }

}