package com.letu.solutions.model.log;

import cn.hutool.core.date.DateUtil;
import com.letu.solutions.core.enums.OsTypeEnum;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.HeaderDto;
import com.letu.solutions.core.utils.VersionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;

/**
 * <AUTHOR>
 * 埋点基础类
 */
@Data
@Slf4j
public class BaseLog {
    /**
     * 天 yyyyMMdd
     */
    private Integer day;
    /**
     * 小时 HH
     */
    private Integer hour;
    /**
     * 设备 仅支持[android,ios,web] 3种值
     */
    private String os;
    /**
     * 平台[app;h5]
     */
    private OsTypeEnum osType;
    /**
     * 渠道id
     */
    private String codeChannel;
    /**
     * 落地页id
     */
    private Long pageId;
    /**
     * 渠道包类型
     */
    private String platform;
    /**
     * 主体编号
     */
    private String bodyCode;
    /**
     * ip
     */
    private String ipAddress;
    /**
     * 设备号
     */
    private String deviceNo;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户城市
     */
    private String city;
    /**
     * 渠道系数
     */
    private String deductRation;
    /**
     * 返佣系数
     */
    private String callbackRation;
    /**
     * 开启纯净版 0：否  1：是
     */
    private Integer purEdition;
    /**
     * 定位城市
     */
    private String locationCity;
    /**
     * 是否新用户[1:是;0:否]
     */
    private Integer isNewUser;
    private Date createTime;

    /**
     * app版本号
     */
    private Integer version;

    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 埋点公用信息初始化
     *
     * @param extendData
     * @return
     */
    public BaseLog build(ExtendData extendData) {
        Date createTime = new Date();
        this.day = Integer.parseInt(DateUtil.format(createTime, "yyyyMMdd"));
        this.hour = Integer.parseInt(DateUtil.format(createTime, "HH"));
        this.createTime = createTime;
        HeaderDto headerDto = extendData.getHeaderDto();
        this.os = headerDto.getOs().toString();
        this.osType = headerDto.getOsType();
        this.ipAddress = extendData.getIpAddress();
        this.deviceNo = extendData.getHeaderDto().getDeviceId();
        this.userId = extendData.getUserId();
        this.bodyCode = extendData.getHeaderDto().getBodyCode();
        this.platform = extendData.getHeaderDto().getPlatform();
//        try { 
//            UserAgent ua = UserAgentUtil.parse(extendData.getHeaderDto().getUserAgent());
//        } catch (Exception e) {
//            log.error("设备信息初始化报错，未正确获取userAgent,userAgent:{}", extendData.getHeaderDto(), e);
//        }
        this.version = VersionUtil.versionNum(headerDto.getVersion());
        this.isNewUser = isNewUser(extendData);
        return this;
    }

    /**
     * 判断新用户
     *
     * @param extendData
     * @return
     */
    public Integer isNewUser(ExtendData extendData) {
        if (Objects.isNull(extendData) || Objects.isNull(extendData.getAuthentication()) || Objects.isNull(extendData.getAuthentication().getUserInfo()) || Objects.isNull(extendData.getAuthentication().getUserInfo().getCreateTime())) {
            return 0;
        }

        Date created = extendData.getAuthentication().getUserInfo().getCreateTime();
        if (Objects.nonNull(created)
                && DateUtil.format(created, PURE_DATE_PATTERN).equals(this.day.toString())) {
            return 1;
        }
        return 0;
    }

}
