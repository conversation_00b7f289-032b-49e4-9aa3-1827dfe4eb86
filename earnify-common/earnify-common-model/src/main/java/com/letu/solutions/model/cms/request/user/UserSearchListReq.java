package com.letu.solutions.model.cms.request.user;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户阅读记录表
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserSearchListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 用户搜索关键词
     */
    private String keywords;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 修改时间
     */
    private Date updatedAt;
}
