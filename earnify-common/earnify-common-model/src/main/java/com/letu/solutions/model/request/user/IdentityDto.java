package com.letu.solutions.model.request.user;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/4/19
 */
@Data
public class IdentityDto {


    /**
     * 用户手机号
     */
    @NotBlank(message = "$请输入手机号~")
    private String phone;

    /**
     * 身份证号
     */
    @NotBlank(message = "$请输入身份证号~")
    private String idCard;

    /**
     * 实名姓名
     */
    @NotBlank(message = "$请输入姓名~")
    private String realName;
}
