package com.letu.solutions.model.cms.response.cms;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 资金增长趋势响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
public class FundGrowthTrendRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 开始日期
     */
    private Integer startDate;

    /**
     * 结束日期
     */
    private Integer endDate;

    /**
     * 每日资金增长数据列表
     */
    private List<DailyFundGrowthData> dailyGrowthData;

    /**
     * 每日资金增长数据
     */
    @Data
    @Builder
    public static class DailyFundGrowthData implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 日期，格式：yyyyMMdd
         */
        private Integer date;

        /**
         * 充值金额
         */
        private BigDecimal rechargeAmount;

        /**
         * 提现金额
         */
        private BigDecimal withdrawAmount;

        /**
         * 冻结金额
         */
        private BigDecimal frozenAmount;

        /**
         * 发放金额
         */
        private BigDecimal grantedAmount;
    }
}
