package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户充值记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class DepositRecordUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID，充值记录ID
     */
    @NotNull(message = "主键ID，充值记录ID不可为空")
    private Long id;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 协议网络（如 ERC20, TRC20, BEP20）
     */
    @NotEmpty(message = "协议网络（如 ERC20, TRC20, BEP20）不可为空")
    private String network;
    /**
     * 充值数量
     */
    @NotNull(message = "充值数量不可为空")
    private BigDecimal amount;
    /**
     * 币种（如 USDT, ETH）
     */
    @NotEmpty(message = "币种（如 USDT, ETH）不可为空")
    private String tokenSymbol;
    /**
     * 用户付款钱包地址
     */
    @NotEmpty(message = "用户付款钱包地址不可为空")
    private String fromAddress;
    /**
     * 平台收款钱包地址
     */
    @NotEmpty(message = "平台收款钱包地址不可为空")
    private String toAddress;
    /**
     * 链上交易哈希（Txn Hash）
     */
    @NotEmpty(message = "链上交易哈希（Txn Hash）不可为空")
    private String txHash;
    /**
     * 截图凭证，最多支持3张图片URL
     */
    private String evidenceImage;
    /**
     * 处理人员（后台管理员）
     */
    private String operator;
    /**
     * 备注信息
     */
    private String remark;
}
