package com.letu.solutions.model.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import com.letu.solutions.model.enums.IconTypeEnum;
import com.letu.solutions.model.enums.SysMenuTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统权限（菜单）
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父菜单id
     */
    private Long parentId;

    /**
     * 菜单PATH链接（前端要求）
     */
    private String path;

    /**
     * 目录类型[menu:菜单,list:目录]
     */
    private SysMenuTypeEnum menuType;

    /**
     * 路由名称
     */
    private String component;

    /**
     * 路由名称
     */
    private String routeName;

    /**
     * 菜单显示名称
     */
    private String title;

    /**
     * icon来源类型
     */
    private IconTypeEnum iconType;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 隐藏标志（false-0-不隐藏 true-1-隐藏）
     */
    private Integer hidden;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
