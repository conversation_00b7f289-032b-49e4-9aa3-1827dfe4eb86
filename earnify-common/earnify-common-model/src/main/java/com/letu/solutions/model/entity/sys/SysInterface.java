package com.letu.solutions.model.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import com.letu.solutions.model.enums.InterfaceTypeEnum;
import lombok.*;

/**
 * 系统接口
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysInterface implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * code类型
     */
    private InterfaceTypeEnum type;

    /**
     * 接口code
     */
    private String name;

    /**
     * 全路径接口code
     */
    private String nameAll;

    /**
     * 接口描述
     */
    private String interfaceDesc;

    /**
     * 全路径接口描述
     */
    private String interfaceAllDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
