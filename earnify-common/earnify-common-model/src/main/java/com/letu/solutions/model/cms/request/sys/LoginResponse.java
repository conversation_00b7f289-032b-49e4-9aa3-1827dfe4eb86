package com.letu.solutions.model.cms.request.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.letu.solutions.share.model.enums.SexEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 后台系统用户表出参
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LoginResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号(登录账号)
     */
    private String phone;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户性别
     */
    private SexEnum sex;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户登录标识
     */
    private String token;
}
