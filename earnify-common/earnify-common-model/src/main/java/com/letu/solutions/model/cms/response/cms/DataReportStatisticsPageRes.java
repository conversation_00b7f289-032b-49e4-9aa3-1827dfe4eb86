package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 数据简报统计
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class DataReportStatisticsPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 统计日期
     */
    private Date statDate;
    /**
     * 甲方新增用户数
     */
    private Integer newUserCountA;
    /**
     * 乙方新增用户数
     */
    private Integer newUserCountB;
    /**
     * 新增用户数
     */
    private Integer newUserCount;
    /**
     * 新增产品数
     */
    private Integer newProductCount;
    /**
     * 新增任务数
     */
    private Integer newTaskCount;
    /**
     * 领取任务数
     */
    private Integer taskClaimCount;
    /**
     * 完成任务数
     */
    private Integer taskCompleteCount;
    /**
     * 充值成功积分
     */
    private BigDecimal pointsRechargeSuccess;
    /**
     * 提现成功积分
     */
    private BigDecimal pointsWithdrawSuccess;
    /**
     * 冻结积分
     */
    private BigDecimal pointsFrozen;
    /**
     * 发放积分
     */
    private BigDecimal pointsGranted;
    /**
     * 返还积分
     */
    private BigDecimal pointsReturned;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
