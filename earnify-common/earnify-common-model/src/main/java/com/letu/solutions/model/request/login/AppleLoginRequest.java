package com.letu.solutions.model.request.login;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: jack ma
 * @CreateTime: 2025-04-09  15:45
 * @Version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AppleLoginRequest extends BaseLoginRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "$code不能为空")
    private String code;


}
