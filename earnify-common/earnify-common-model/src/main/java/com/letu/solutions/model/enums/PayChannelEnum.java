package com.letu.solutions.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道
 *
 * @description 支付渠道
 * <AUTHOR>
 * @createTime 2025/5/9 18:01
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum {

    /**
     * 微信支付
     */
    wechatPay("wechatPay", "微信支付"),
    /**
     * 支付宝支付
     */
    aliPay("alipay", "支付宝"),
    /**
     * 快手支付
     */
    kuaishouPay("kuaishouPay", "快手支付"),
    /**
     * 抖音支付
     */
    douyinPay("douyinPay", "抖音支付"),
    /**
     * ios支付
     */
    applePay("applePay", "ios支付"),
    ;

    private String code;
    private String desc;
}
