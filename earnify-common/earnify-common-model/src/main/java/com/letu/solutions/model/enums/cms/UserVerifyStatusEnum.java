package com.letu.solutions.model.enums.cms;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户审核状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Getter
@AllArgsConstructor
public enum UserVerifyStatusEnum {

    /**
     * 未提交
     */
    not_submitted("未提交"),

    /**
     * 待审核
     */
    pending("待审核"),

    /**
     * 未通过
     */
    rejected("未通过"),

    /**
     * 已通过
     */
    approved("已通过");



    @EnumValue
    @JsonValue
    private final String desc;
}
