package com.letu.solutions.model.cms.request.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 角色菜单关系入参
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SysRoleMenuRelSaveReq implements Serializable {
    private static final long serialVersionUID = 5986083612141188747L;
    /**
     * 角色ID
     */
    @NotNull(message = "$请选择角色")
    private Long roleId;

    /**
     * 权限ID
     */
    private List<Long> menuIds;

}
