package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 账户资金信息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserAccountBalanceDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 可用资金
     */
    private BigDecimal availableAmount;
    /**
     * 冻结资金
     */
    private BigDecimal frozenAmount;
    /**
     * 币种
     */
    private String currency;
}
