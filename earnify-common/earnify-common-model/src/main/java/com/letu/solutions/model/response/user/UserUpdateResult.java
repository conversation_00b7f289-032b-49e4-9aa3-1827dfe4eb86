package com.letu.solutions.model.response.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户更新结果
 * User update result
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdateResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 成功结果
     */
    public static UserUpdateResult success() {
        return UserUpdateResult.builder()
                .success(true)
                .build();
    }
    
    /**
     * 失败结果
     */
    public static UserUpdateResult fail(String errorMessage) {
        return UserUpdateResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
} 