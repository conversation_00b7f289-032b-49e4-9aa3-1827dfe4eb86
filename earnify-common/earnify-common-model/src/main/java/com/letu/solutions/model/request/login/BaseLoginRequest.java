package com.letu.solutions.model.request.login;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jack ma
 * @CreateTime: 2025-04-09  16:34
 * @Version: 1.0
 */
@Data
public class BaseLoginRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @NotBlank(message = "$设备id不为空")
    private String deviceId;

    /**
     * 落地页id
     */
    @NotNull(message = "$落地页id不可为空")
    private Long pageId;

    /**
     * 渠道code
     */
    @NotBlank(message = "$渠道code不可为空")
    private String codeChannel;
}
