package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTaskUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务id
     */
    @NotNull(message = "任务id不可为空")
    private Long taskId;
    /**
     * 乙方用户id
     */
    @NotNull(message = "乙方用户id不可为空")
    private Long userId;

    /**
     * 审核状态 1 通过 2不通过
     */
    @NotNull(message = "审核状态不可为空")
    private Integer state;
}
