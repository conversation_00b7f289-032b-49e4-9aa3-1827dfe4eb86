package com.letu.solutions.model.request.user;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 修改用户信息
 */
@Data
public class UserUpdateInfoReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 头像地址
     */
//    @NotEmpty(message = "$头像地址不可为空")
//    @Size(min = 10, max = 255, message = "$头像地址不合法")
    private String imgUrl;

    /**
     * 用户昵称
     */
    @NotEmpty(message = "$用户昵称不能为空")
    @Size(min = 2, max = 16, message = "$用户昵称不合法")
    private String nickName;
    /**
     * 用户简介
     */
    private String userDescription;

    /**
     * 邀请码
     */
    private String inviteCode;
    /**
     * 国家
     */
    @NotEmpty(message = "$国家不能为空")
    private String countryName;
}
