package com.letu.solutions.model.cms.request.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 启用禁用实体
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @NotNull(message = "$id不可为空")
    private Long id;

}
