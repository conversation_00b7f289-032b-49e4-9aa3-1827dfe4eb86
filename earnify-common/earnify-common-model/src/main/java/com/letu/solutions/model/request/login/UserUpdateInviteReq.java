package com.letu.solutions.model.request.login;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserUpdateInviteReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 邀请码
     */
    @NotEmpty(message = "$邀请码不可为空")
    @Size(min = 1, max = 10, message = "$邀请码不合法")
    private String inviteCode;
}
