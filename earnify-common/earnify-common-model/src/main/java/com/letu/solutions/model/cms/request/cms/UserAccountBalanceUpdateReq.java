package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 账户资金信息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserAccountBalanceUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不可为空")
    private Long id;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 可用资金
     */
    @NotNull(message = "可用资金不可为空")
    private BigDecimal availableAmount;
    /**
     * 冻结资金
     */
    @NotNull(message = "冻结资金不可为空")
    private BigDecimal frozenAmount;
    /**
     * 币种
     */
    @NotEmpty(message = "币种不可为空")
    private String currency;
}
