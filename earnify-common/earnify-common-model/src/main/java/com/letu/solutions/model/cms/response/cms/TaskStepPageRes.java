package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.letu.solutions.model.enums.cms.StepTypeEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import java.util.List;

/**
 * 步骤
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskStepPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 任务描述
     */
    private String taskDescribe;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 排序
     */
    private Integer srot;
    /**
     * 步骤类型：支持多选，如 ["text", "image"] 表示文字+图片
     * 可选值：text-文本，image-图片
     */
    private List<String> stepType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    
    // ========== 用户步骤相关字段 ==========
    
    /**
     * 用户任务步骤ID
     */
    private Long userTaskStepId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户任务ID
     */
    private Long userTaskId;
    
    /**
     * 任务文字操作（用户填写的文字内容）
     */
    private String taskTextOperate;
    
    /**
     * 任务图片操作（用户上传的图片URL列表）
     */
    private List<String> taskImageOperate;
    
    /**
     * 是否保存，0-未保存，1-已保存
     */
    private Boolean isSaved;
}
