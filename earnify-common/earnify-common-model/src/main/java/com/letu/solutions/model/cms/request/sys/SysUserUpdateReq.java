package com.letu.solutions.model.cms.request.sys;

import com.letu.solutions.share.model.enums.SexEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 系统用户修改入参
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "$用户id不可为空")
    private Long id;

    /**
     * 用户姓名
     */
    @NotEmpty(message = "$用户姓名不可为空")
    private String name;

    /**
     * 邮箱
     */
    @NotEmpty(message = "$邮箱不可为空")
    private String email;

    /**
     * 用户性别
     */
    @NotNull(message = "$用户性别不可为空")
    private SexEnum sex;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态[1:启用;0:禁用]
     */
    @NotNull(message = "$状态不可为空")
    private Integer enable;

}
