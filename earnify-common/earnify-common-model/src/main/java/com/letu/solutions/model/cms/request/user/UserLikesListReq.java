package com.letu.solutions.model.cms.request.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户点赞记录表
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserLikesListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private Long likeId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 
     */
    private Long bookId;
    /**
     * 应用appId
     */
    private String appId;
    /**
     * 0取消点赞 1点赞
     */
    private Long likeType;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 修改时间
     */
    private Date updatedAt;
}
