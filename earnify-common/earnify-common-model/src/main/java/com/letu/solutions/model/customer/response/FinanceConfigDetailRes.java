package com.letu.solutions.model.customer.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 财务配置详情响应（客户端版本）
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class FinanceConfigDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 协议网络
     */
    private String network;

    /**
     * 币种
     */
    private String currency;

    /**
     * 最小充值金额
     */
    private BigDecimal minAmount;

    /**
     * 最大充值金额
     */
    private BigDecimal maxAmount;

    /**
     * 最小提现金额
     */
    private BigDecimal minWithdrawAmount;

    /**
     * 最大提现金额
     */
    private BigDecimal maxWithdrawAmount;

    /**
     * 提现手续费率
     */
    private BigDecimal withdrawFeeRate;

    /**
     * 每笔提现费用
     */
    private BigDecimal perWithdrawFee;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 平台钱包地址列表
     */
    private List<String> addressList;
}
