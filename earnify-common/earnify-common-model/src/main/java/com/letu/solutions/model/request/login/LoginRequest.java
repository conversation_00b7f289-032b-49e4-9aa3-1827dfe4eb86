package com.letu.solutions.model.request.login;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class LoginRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    @NotEmpty(message = "$手机号不可为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;

    /**
     * 验证码
     */
    @NotEmpty(message = "$验证码不可为空")
    @Length(min = 4, max = 4, message = "$验证码必须为4位")
    private String smsCode;
}
