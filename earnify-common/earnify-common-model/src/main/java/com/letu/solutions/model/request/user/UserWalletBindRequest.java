package com.letu.solutions.model.request.user;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import com.letu.solutions.model.enums.cms.WalletTypeEnum;

/**
 * 用户钱包绑定请求
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class UserWalletBindRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 钱包类型
     */
    @NotNull(message = "钱包类型不能为空")
    private WalletTypeEnum walletType;

    /**
     * 钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    private String walletAddress;

    /**
     * 是否设为默认
     */
    private Boolean isDefault = false;
} 