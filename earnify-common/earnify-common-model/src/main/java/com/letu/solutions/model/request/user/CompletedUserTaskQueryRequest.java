package com.letu.solutions.model.request.user;

import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 已完成用户任务查询请求
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompletedUserTaskQueryRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务状态列表（可选）
     */
    private List<String> stateList;

} 