package com.letu.solutions.model.cms.request.sys;

import com.letu.solutions.model.enums.IconTypeEnum;
import com.letu.solutions.model.enums.SysMenuTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 菜单修改入参
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SysMenuUpdateReq implements Serializable {

    private static final long serialVersionUID = -1358736889668986982L;
    /**
     * 主键
     */
    @NotNull(message = "$id不可为空")
    private Long id;

    /**
     * 父菜单id
     */
    private Long parentId;

    /**
     * 菜单链接PATH
     */
    @NotEmpty(message = "$菜单链接path不可为空")
    private String path;

    /**
     * 目录类型[menu:菜单,list:目录]
     */
    @NotNull(message = "$目录类型不可为空")
    private SysMenuTypeEnum menuType;

    /**
     * 路由名称
     */
    @NotEmpty(message = "$路由名称不可为空")
    private String component;

    /**
     * 路由名称
     */
    @NotEmpty(message = "$路由名称不可为空")
    private String routeName;

    /**
     * 菜单显示名称
     */
    @NotEmpty(message = "$菜单显示名称不可为空")
    private String title;

    /**
     * icon来源类型
     */
    private IconTypeEnum iconType;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 隐藏标志（0-不隐藏 1-隐藏）
     */
    private Integer hidden;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;
}
