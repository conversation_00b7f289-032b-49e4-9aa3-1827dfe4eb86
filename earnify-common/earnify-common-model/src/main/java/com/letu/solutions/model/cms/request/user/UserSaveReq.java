package com.letu.solutions.model.cms.request.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class UserSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 手机号码
     */
    @NotEmpty(message = "$手机号码不可为空")
    private String userPhone;
    /**
     * 用户头像
     */
    private String userImage;
    /**
     * 用户类别
     */
    @NotEmpty(message = "$用户类别不可为空")
    private String userType;
    /**
     * 是否创作者 1是 0否
     */
    @NotNull(message = "$是否创作者 1是 0否不可为空")
    private Integer creator;
    /**
     * 密码
     */
    @NotEmpty(message = "$密码不可为空")
    private String pwd;
    /**
     * 盐
     */
    private String salt;
    /**
     * 用户状态[common:普通用户;cert:已实名;]
     */
    @NotEmpty(message = "$用户状态[common:普通用户;cert:已实名;]不可为空")
    private String status;
    /**
     * 用户创建日期
     */
    private Integer day;
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;
    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;
    /**
     * 设备id
     */
    private String deviceId;

    /**
     *昵称
     */
    private String nickName;
    /**
     * 渠道来源
     */
    private String sourceCode;
}
