package com.letu.solutions.model.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.share.model.enums.user.UserStatusEnum;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@TableName("user")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 手机号码
    */
    private String userPhone;

    /**
    * 用户头像
    */
    private String userImage;

    /**
     *昵称
     */
    private String nickName;

    /**
    * 用户类别 NORMAL正常用户 VIP 用户
    */
    private String userType;

    /**
    * 密码
    */
    private String pwd;

    /**
    * 盐
    */
    private String salt;

    /**
    * 用户状态[common:普通用户;cert:已实名;]
    */
    private UserStatusEnum status;

    /**
    * 用户创建日期
    */
    private Integer day;

    /**
    * 最后登录时间
    */
    private Date lastLoginTime;

    /**
    * 状态[1:启用;0:禁用]
    */
    private Integer enable;

    /**
     * 是否删除[1:已删除;0:未删除]
     */
    private Integer del;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 设备id
    */
    private String deviceId;
    /**
     * 渠道来源
     */
    private String sourceCode;
    /**
     * 账户类型
     */
    private AccountTypeEnum accountRole;
    /**
     * 邮箱地址
     */
    private String email;
    /**
     * google sub
     */
    private String googleSub;
    /**
     * 用户简介
     */
    private String userDescription;

    /**
     * 推荐码
     */
    private String recommendCode;
    /**
     * 国家
     */
    private String countryName;
}
