package com.letu.solutions.model.enums.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 社交媒体绑定状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Getter
@AllArgsConstructor
public enum SocialBindStatusEnum {

    /**
     * 待验证
     */
    PENDING("pending", "待验证"),

    /**
     * 已验证
     */
    VERIFIED("verified", "已验证"),

    /**
     * 验证失败
     */
    FAILED("failed", "验证失败");

    private final String code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static SocialBindStatusEnum fromCode(String code) {
        for (SocialBindStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING;
    }
} 