package com.letu.solutions.model.cms.response.cms;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 财务统计响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
public class FinanceStatisticsRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台账户余额（可用+冻结）
     */
    private BigDecimal platformAccountBalance;

    /**
     * 总计用户可用余额
     */
    private BigDecimal totalUserAvailableBalance;

    /**
     * 总计用户冻结中余额
     */
    private BigDecimal totalUserFrozenBalance;

    /**
     * 总计已发放奖励金额
     */
    private BigDecimal totalRewardAmount;

    /**
     * 累计充值金额
     */
    private BigDecimal totalDepositAmount;

    /**
     * 累计提现金额
     */
    private BigDecimal totalWithdrawAmount;

    /**
     * 用户总资金（可用+冻结）
     */
    private BigDecimal totalUserBalance;

}
