package com.letu.solutions.model.cms.response.cms;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;

/**
 * 待办事项统计响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
public class TodoStatisticsRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 乙方用户身份待审核数量
     */
    private Integer providerKycPendingCount;

    /**
     * 提现待处理笔数
     */
    private Integer withdrawPendingCount;
}
