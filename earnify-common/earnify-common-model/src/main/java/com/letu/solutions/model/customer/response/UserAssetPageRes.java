package com.letu.solutions.model.customer.response;

import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户资产分页响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetPageRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 时间
     */
    private Date createTime;

    /**
     * 初始数量（变动前余额）
     */
    private BigDecimal initialAmount;

    /**
     * 变动数量
     */
    private BigDecimal changeAmount;

    /**
     * 方向（入账，出账）
     */
    private FundSideTypeEnum side;

    /**
     * 余额（变动后数量）
     */
    private BigDecimal balance;

    /**
     * 动账类型描述
     */
    private String fundTypeDesc;
}
