package com.letu.solutions.model.enums.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.dubbo.remoting.http12.rest.Schema;

/**
 * 任务状态
 */
@Getter
@AllArgsConstructor
public enum WithdrawStatusEnum {
    /**
     * 未处理
     *
     */
    unprocessed("未处理"),
    /**
     * 未通过
     */
    rejected("未通过"),
    /**
     * 通过
     */
    completed("已完成");

    private String desc;
}
