package com.letu.solutions.model.cms.response.cms;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;
import java.util.List;

/**
 * 用户增长趋势响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
public class UserGrowthTrendRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 开始日期
     */
    private Integer startDate;

    /**
     * 结束日期
     */
    private Integer endDate;

    /**
     * 每日用户增长数据列表
     */
    private List<DailyUserGrowthData> dailyGrowthData;

    /**
     * 每日用户增长数据
     */
    @Data
    @Builder
    public static class DailyUserGrowthData implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 日期，格式：yyyyMMdd
         */
        private Integer date;

        /**
         * 甲方用户增长数
         */
        private Integer clientGrowth;

        /**
         * 乙方用户增长数
         */
        private Integer providerGrowth;

        /**
         * 总用户增长数
         */
        private Integer totalGrowth;
    }
}
