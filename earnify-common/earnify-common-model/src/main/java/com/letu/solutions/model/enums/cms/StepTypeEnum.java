package com.letu.solutions.model.enums.cms;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务步骤类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Getter
@AllArgsConstructor
public enum StepTypeEnum {

    /**
     * 文本类型
     */
    text("text", "文本"),

    /**
     * 图片类型
     */
    image("image", "图片");

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;
} 