package com.letu.solutions.model.cms.request.sys;

import com.letu.solutions.share.model.enums.SexEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 系统用户新增入参
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 手机号
     */
    @NotEmpty(message = "$用户手机号不可为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;

    /**
     * 用户姓名
     */
    @NotEmpty(message = "$用户姓名不可为空")
    private String name;

    /**
     * 邮箱
     */
    @NotEmpty(message = "$邮箱不可为空")
    private String email;

    /**
     * 用户性别
     */
    @NotNull(message = "$用户性别不可为空")
    private SexEnum sex;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态[1:启用;0:禁用]
     */
    @NotNull(message = "$状态不可为空")
    private Integer enable;


}
