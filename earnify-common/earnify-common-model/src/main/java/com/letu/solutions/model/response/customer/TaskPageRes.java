package com.letu.solutions.model.response.customer;

import com.letu.solutions.model.cms.response.cms.TaskStepPageRes;
import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务分页响应VO
 */
@Data
public class TaskPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务ID
     */
    private Long id;
    /**
     * 任务属性 1人工 2自动
     */
    private Integer attribute;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 是否排重 1排重 2不排重
     */
    private Integer weightSorting;
    /**
     * 任务类型
     */
    private TaskTypeEnum taskType;
    /**
     * 可接任务数量
     */
    private Integer number;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 任务有效期（天数）
     */
    private Integer time;
    /**
     * 任务状态
     */
    private TaskStatusEnum state;
    /**
     * 步骤数量
     */
    private Integer stepNumber;
    /**
     * 任务图片URL
     */
    private String url;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 任务已完成次数
     */
    private Integer taskFinishNum;
    /**
     * 任务已被领取数量
     */
    private Integer taskReceiveNum;
    /**
     * 到期时间
     */
    private Date expireTime;
    /**
     * 状态展示内容
     */
    private String statusDisplay;
    /**
     * 审核中倒计时（秒）
     */
    private Long auditCountdown;
    
    /**
     * 任务到期倒计时（秒）
     */
    private Long expireCountdownSeconds;
    
    /**
     * 申诉倒计时（秒）
     */
    private Long appealCountdown;
    /**
     * 保证金
     */
    private Integer bond;
    /**
     * 任务步骤列表
     */
    private List<TaskStepPageRes> stepList;
    /**
     * 甲方用户信息
     */
    private PartyUserInfoRes partyUserInfo;
    
    // ========== 用户任务相关字段 ==========
    
    /**
     * 用户任务ID
     */
    private Long userTaskId;
    
    /**
     * 用户任务状态
     */
    private UserTaskStatusEnum userTaskState;
    
    /**
     * 用户任务创建时间
     */
    private Date userTaskCreateTime;
    
    /**
     * 用户任务更新时间
     */
    private Date userTaskUpdateTime;
    
    /**
     * 用户任务完成时间
     */
    private Date userTaskExamineTime;
    
    /**
     * 用户任务申诉时间
     */
    private Date userTaskAppealTime;
    
    /**
     * 用户是否已领取该任务
     */
    private Boolean hasReceived;
    
    /**
     * 用户任务进度（如：3/5）
     */
    private String userTaskProgress;
} 