package com.letu.solutions.model.response.customer;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 邀请记录响应
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class InviteRecordRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名称（被邀请人昵称）
     */
    private String userName;

    /**
     * 邀请方式
     */
    private String inviteMethod;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 奖励积分
     */
    private BigDecimal rewardPoints;
} 