package com.letu.solutions.model.response.user;

import com.letu.solutions.model.response.login.UserResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * 登录返回信息
 *
 * <AUTHOR>
 */
@Data
public class LoginResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户token
     */
    private String token;

    /**
     * 用户信息
     */
    private UserResponse userDetail;

    /**
     * 是否新用户：0--否，1--是
     */
    private Integer isNewUser;
}
