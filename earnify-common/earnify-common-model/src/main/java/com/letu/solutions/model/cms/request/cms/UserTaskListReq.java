package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserTaskListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private Long taskId;
    /** 任务名称 */
    private String taskName;
    /** 甲方账号昵称 */
    private String partyANickName;
    /** 甲方id */
    private Long partyAId;
    /** 乙方账号昵称 */
    private String partyBNickName;
    /** 乙方id */
    private Long partyBId;
    /** 关联产品id */
    private Long productId;
    /** 产品名称 */
    private String productName;
    /**
     * 开始时间
     */
    private String staTime;
    /**
     * 结束时间
     */
    private String finishTime;


}
