package com.letu.solutions.model.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import com.letu.solutions.share.model.enums.SexEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 后台系统用户表
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号(登录账号)
     */
    private String phone;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户性别
     */
    private SexEnum sex;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 是否已删除[1:是;0:否]
     */
    private Integer del;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


}
