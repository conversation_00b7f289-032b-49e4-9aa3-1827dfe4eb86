package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.validation.constraints.Size;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 用户充值记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class DepositRecordSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 协议网络（如 ERC20, TRC20, BEP20）
     */
    @NotEmpty(message = "协议网络（如 ERC20, TRC20, BEP20）不可为空")
    private String network;
    /**
     * 充值数量
     */
    @NotNull(message = "充值数量不可为空")
    private BigDecimal amount;
    /**
     * 用户付款钱包地址
     */
    @NotEmpty(message = "用户付款钱包地址不可为空")
    private String fromAddress;
    /**
     * 平台收款钱包地址
     */
    @NotEmpty(message = "平台收款钱包地址不可为空")
    private String toAddress;
    /**
     * 链上交易哈希（Txn Hash）
     */
    @NotEmpty(message = "链上交易哈希（Txn Hash）不可为空")
    private String txHash;
    /**
     * 截图凭证，最多支持3张图片URL
     */
    @Size(min = 0, max = 3)
    private List<String> evidenceImages;
}
