package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTransactionBillDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 账号类型（0=甲方，1=乙方）
     */
    private Integer accountType;
    /**
     * 动账类型（0=任务奖励/保证金冻结，1=任务奖励冻结，2=任务奖励到账，3=任务奖励解冻发放，4=任务奖励/保证金解冻返还，5=任务奖励解冻返还，6=充值，7=提现，8=提现审核驳回）
     */
    private Integer actionType;
    /**
     * 方向（1=入账，2=出账）
     */
    private Integer side;
    /**
     * 动账金额
     */
    private BigDecimal amount;
    /**
     * 币种
     */
    private String coinSymbol;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 变动前金额
     */
    private BigDecimal balanceBefore;
    /**
     * 变动后金额
     */
    private BigDecimal balanceAfter;
    /**
     * 冻结金额
     */
    private BigDecimal frozen;
    /**
     * 充值或提现关联id
     */
    private Long fundId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 更新时间
     */
    private Date updatedAt;
}
