package com.letu.solutions.model.request.user;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import com.letu.solutions.model.enums.cms.SocialTypeEnum;

/**
 * 用户社交媒体绑定请求
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class UserSocialBindRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 社交媒体类型
     */
    @NotNull(message = "社交媒体类型不能为空")
    private SocialTypeEnum socialType;

    /**
     * 社交媒体链接
     */
    @NotNull(message = "社交媒体链接不能为空")
    private String socialUrl;

    /**
     * 是否需要验证（可选，默认为true）
     */
    private Boolean needVerify = true;
} 