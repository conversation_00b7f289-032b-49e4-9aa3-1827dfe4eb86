package com.letu.solutions.model.cms.request.cms;

import com.letu.solutions.core.constant.DateConstants;
import lombok.Data;

import jakarta.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 基础趋势查询请求
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public abstract class BaseTrendReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询类型：LAST_7_DAYS(近七天), THIS_MONTH(本月), LAST_MONTH(上月), CUSTOM(自定义)
     */
    @Pattern(regexp = "^(LAST_7_DAYS|THIS_MONTH|LAST_MONTH|CUSTOM)$", 
             message = "查询类型只能是：LAST_7_DAYS、THIS_MONTH、LAST_MONTH、CUSTOM")
    private String queryType;

    /**
     * 自定义开始日期，格式：yyyyMMdd
     * 当queryType为CUSTOM时必填
     */
    private Integer startDate;

    /**
     * 自定义结束日期，格式：yyyyMMdd
     * 当queryType为CUSTOM时必填
     */
    private Integer endDate;
}
