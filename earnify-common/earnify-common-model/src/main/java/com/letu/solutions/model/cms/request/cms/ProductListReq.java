package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import com.letu.solutions.model.enums.cms.ProductStatusEnum;
import com.letu.solutions.model.enums.cms.ProductTypeEnum;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 产品表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 甲方用户ID
     */
    private Long partyUserId;
    /**
     * 产品类型
     */
    private ProductTypeEnum productType;
    /**
     * 产品状态
     */
    private ProductStatusEnum productStatus;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 甲方昵称
     */
    private String partyUserNickname;
}
