package com.letu.solutions.model.cms.response.cms;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.ProductStatusEnum;
import com.letu.solutions.model.enums.cms.ProductTypeEnum;
import lombok.Data;


/**
 * 产品表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class ProductPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品logo的URL
     */
    private String logo;
    /**
     * 主图
     */
    private String hostUrl;
    /**
     * 甲方用户ID
     */
    private Long partyUserId;
    /**
     * 产品类型，
     */
    private ProductTypeEnum productType;
    /**
     * 产品状态
     */
    private ProductStatusEnum productStatus;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 甲方用户昵称
     */
    private String partyUserNickname;
    /**
     * 任务发布次数
     */
    private Integer taskPublishCount;
    /**
     * 任务发布个数
     */
    private Integer taskPublishNum;
    /**
     * 任务领取个数
     */
    private Integer taskReceiveNum;
    /**
     * 任务完成个数
     */
    private Integer taskFinishNum;
    /**
     * 已经发送奖励金额
     */
    private BigDecimal sentRewardAmount;
    /**
     * 剩余冻结金额
     */
    private BigDecimal freezeAmount;
}
