package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.WithdrawStatusEnum;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 提现记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class WithdrawRecordUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID，提现记录ID
     */
    @NotNull(message = "主键ID，提现记录ID不可为空")
    private Long id;
    /**
     * 提现状态（未通过，已打款）
     */
    @NotNull(message = "提现状态不能为空")
    private WithdrawStatusEnum withdrawStatus;
    /**
     * 备注信息
     */
    private String remark;
}
