package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.letu.solutions.model.enums.cms.CertificateTypeEnum;
import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * KYC身份认证表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("user_kyc_auth")
public class UserKycAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 用户ID，关联user表
    */
    private Long userId;

    /**
    * 账户类型: 甲方, 乙方
    */
    private AccountTypeEnum accountRole;

    /**
    * 证件类型：1=身份证，2=护照，3=驾驶执照
    */
    private CertificateTypeEnum idType;

    /**
    * 证件号码
    */
    private String idNumber;

    /**
    * 证照图片数组
    */
    @TableField(typeHandler = com.letu.solutions.model.hanlder.StringListTypeHandler.class)
    private List<String> documentImages;

    /**
    * 提交时间
    */
    private Date submitTime;

    /**
    * 审核状态：未提交、待审核、已通过、未通过
    */
    private UserVerifyStatusEnum verifyStatus;

    /**
    * 审核时间
    */
    private Date verifyTime;

    /**
    * 审核备注或失败原因
    */
    private String remark;
}
