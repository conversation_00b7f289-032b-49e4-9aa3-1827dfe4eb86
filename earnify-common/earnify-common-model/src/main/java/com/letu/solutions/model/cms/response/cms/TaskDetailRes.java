package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.entity.cms.TaskStep;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 任务属性 1人工 2自动
     */
    private Integer attribute;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 发布者id
     */
    private Long userId;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 是否排重 1排重 2不排重
     */
    private Integer weightSorting;
    /**
     * 任务类型 枚举
     */
    private TaskTypeEnum taskType;
    /**
     * 可接任务数量
     */
    private Integer number;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 任务有效期 天数
     */
    private Integer time;
    /**
     * 任务状态 枚举
     */
    private String state;
    /**
     * 步骤数量
     */
    private Integer stepNumber;
    /**
     * 任务图片
     */
    private String url;
    /**
     * 步骤集合
     */
    private List<TaskStep> taskSteps;
}
