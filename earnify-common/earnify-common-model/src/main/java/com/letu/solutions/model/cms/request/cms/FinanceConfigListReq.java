package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 财务配置表
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinanceConfigListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    private Long id;
    /**
     * 链类型（TRC20、ERC20）
     */
    private String network;
    /**
     * 币种（如 USDT）
     */
    private String currency;
    /**
     * 最小充值金额
     */
    private BigDecimal minAmount;
    /**
     * 最大充值金额
     */
    private BigDecimal maxAmount;
    /**
     * 最小提现金额
     */
    private BigDecimal minWithdraw;
    /**
     * 最大提现金额
     */
    private BigDecimal maxWithdraw;
    /**
     * 单用户每日最多可提现次数
     */
    private Integer maxWithdrawTimesPerDay;
    /**
     * 手续费比例（如 0.005 表示0.5%）
     */
    private BigDecimal feeRate;
    /**
     * 每笔提现固定手续费
     */
    private BigDecimal fixedFee;
    /**
     * 
     */
    private String remark;
}
