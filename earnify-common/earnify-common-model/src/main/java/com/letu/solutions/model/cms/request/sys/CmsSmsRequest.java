package com.letu.solutions.model.cms.request.sys;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class CmsSmsRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @NotEmpty(message = "$手机号不可为空")
    private String phone;
}
