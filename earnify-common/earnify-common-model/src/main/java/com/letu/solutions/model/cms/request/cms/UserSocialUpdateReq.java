package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 社交媒体绑定信息表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserSocialUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不可为空")
    private Long id;
    /**
     * 用户ID，关联user表
     */
    @NotNull(message = "用户ID，关联user表不可为空")
    private Long userId;
    /**
     * Twitter链接
     */
    private String twitter;
    /**
     * Telegram链接
     */
    private String telegram;
    /**
     * Discord链接
     */
    private String discord;
    /**
     * Reddit链接
     */
    private String reddit;
    /**
     * TikTok链接
     */
    private String tiktok;
    /**
     * Medium链接
     */
    private String medium;
}
