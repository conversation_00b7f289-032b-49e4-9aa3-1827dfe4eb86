package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTaskSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务id
     */
    @NotNull(message = "任务id不可为空")
    private Long taskId;
    /**
     * 用户id
     */
    @NotNull(message = "用户id不可为空")
    private Long userId;
    /**
     * 任务状态 枚举
     */
    @NotEmpty(message = "任务状态 枚举不可为空")
    private String state;
    /**
     * 步骤任务描述
     */
    @NotEmpty(message = "步骤任务描述不可为空")
    private String taskDescribe;
    /**
     * 步骤单价
     */
    @NotNull(message = "步骤单价不可为空")
    private BigDecimal price;
    /**
     * 步骤排序
     */
    @NotNull(message = "步骤排序不可为空")
    private Integer srot;
    /**
     * 任务到期时间
     */
    @NotNull(message = "任务到期时间不可为空")
    private Date time;
}
