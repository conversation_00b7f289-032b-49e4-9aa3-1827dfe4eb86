package com.letu.solutions.model.enums.cms;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 奖励状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Getter
@AllArgsConstructor
public enum RewardStatusEnum {

    /**
     * 待发放
     */
    PENDING("pending", "待发放"),

    /**
     * 已发放
     */
    GRANTED("granted", "已发放"),

    /**
     * 发放失败
     */
    FAILED("failed", "发放失败");

    private final String code;
    private final String desc;

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static RewardStatusEnum fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (RewardStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 