package com.letu.solutions.model.cms.request.sys;

import com.letu.solutions.model.enums.LogTypeEnum;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysOperateListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 日志类型['common','error']
     */
    private LogTypeEnum logType;
    /**
     * 操作人员id
     */
    private Long userId;
    /**
     * 操作接口名称
     */
    private String methodNameAll;
    /**
     * 操作接口描述
     */
    private String methodDescAll;
    /**
     * 请求URL
     */
    private String requestUrl;
}
