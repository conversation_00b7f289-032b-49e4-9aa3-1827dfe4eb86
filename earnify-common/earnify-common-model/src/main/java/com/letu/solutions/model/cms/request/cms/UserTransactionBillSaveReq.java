package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.dubbo.common.logger.FluentLogger;

import java.io.Serializable;

/**
 * 账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserTransactionBillSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 账号类型（甲方，乙方）
     * @see AccountTypeEnum
     */
    @NotNull(message = "账号类型（甲方，乙方）不可为空")
    private AccountTypeEnum accountType;
    /**
     * 动账类型（任务奖励/保证金冻结，任务奖励冻结，任务奖励到账，任务奖励解冻发放，任务奖励/保证金解冻返还，任务奖励解冻返还，充值，提现，提现审核驳回）
     * @see com.letu.solutions.model.enums.cms.ActionTypeEnum
     */
    @NotNull(message = "动账类型（任务奖励/保证金冻结，任务奖励冻结，任务奖励到账，任务奖励解冻发放，任务奖励/保证金解冻返还，任务奖励解冻返还，充值，提现，提现审核驳回）不可为空")
    private FundTypeEnum fundType;
    /**
     * 方向（入账，出账）
     * @see  FundSideTypeEnum
     */
    @NotNull(message = "方向（1=入账，2=出账）不可为空")
    private FundSideTypeEnum side;
    /**
     * 动账金额
     */
    @NotNull(message = "动账金额不可为空")
    private BigDecimal amount;
    /**
     * 币种
     */
    @NotEmpty(message = "币种不可为空")
    private String currency;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 变动前金额
     */
    @NotNull(message = "变动前金额不可为空")
    private BigDecimal balanceBefore;
    /**
     * 变动后金额
     */
    @NotNull(message = "变动后金额不可为空")
    private BigDecimal balanceAfter;
    /**
     * 冻结金额
     */
    @NotNull(message = "冻结金额不可为空")
    private BigDecimal frozen;
    /**
     * 充值或提现关联id
     */
    private Long fundId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 操作日期
     */
    private Integer day;
}
