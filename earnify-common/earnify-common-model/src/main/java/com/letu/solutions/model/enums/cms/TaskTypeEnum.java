package com.letu.solutions.model.enums.cms;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型
 */
@Getter
@AllArgsConstructor
public enum TaskTypeEnum {


    /**
     * 任务
     */
    task( "任务"),
    /**
     * 链游
     */
    nft( "链游"),
    ;

    private String desc;
    
    @JsonValue
    public String getDesc() {
        return desc;
    }
    
    @JsonCreator
    public static TaskTypeEnum fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        for (TaskTypeEnum type : TaskTypeEnum.values()) {
            if (type.name().equalsIgnoreCase(value) || type.getDesc().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
