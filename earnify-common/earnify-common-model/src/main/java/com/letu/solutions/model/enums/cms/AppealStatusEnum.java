package com.letu.solutions.model.enums.cms;

/**
 * 申诉状态枚举
 * 0-待审核，1-通过，2-不通过
 */
public enum AppealStatusEnum {
    PENDING(0, "待审核"),
    APPROVED(1, "通过"),
    REJECTED(2, "不通过");

    private final int code;
    private final String desc;

    AppealStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AppealStatusEnum fromCode(Integer code) {
        if (code == null) return null;
        for (AppealStatusEnum e : values()) {
            if (e.code == code) return e;
        }
        return null;
    }
} 