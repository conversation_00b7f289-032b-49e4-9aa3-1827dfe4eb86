package com.letu.solutions.model.cms.response.cms;

import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 乙方用户管理响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkerUserManagementRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份审核状态
     */
    private UserVerifyStatusEnum verifyStatus;

    /**
     * 关联上级名称
     */
    private String parentUserName;

    /**
     * 关联上级ID
     */
    private Long parentUserId;

    /**
     * 直推人数
     */
    private Integer directInviteCount;

    /**
     * 总人数（包含间推）
     */
    private Integer totalInviteCount;

    /**
     * 领取任务数
     */
    private Integer receivedTaskCount;

    /**
     * 完成任务数
     */
    private Integer completedTaskCount;

    /**
     * 未结奖励
     */
    private BigDecimal pendingReward;

    /**
     * 已结奖励
     */
    private BigDecimal settledReward;

    /**
     * 提现中
     */
    private BigDecimal withdrawingAmount;

    /**
     * 已提现
     */
    private BigDecimal withdrawnAmount;

    /**
     * 账户余额
     */
    private BigDecimal accountBalance;

    /**
     * 创建时间
     */
    private Date createTime;
}
