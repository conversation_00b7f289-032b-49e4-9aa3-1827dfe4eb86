package com.letu.solutions.model.cms.request.cms;

import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 乙方用户身份审核请求
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class UserKycAuthVerifyReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    private UserVerifyStatusEnum verifyStatus;

    /**
     * 审核不通过原因（审核不通过时必填）
     */
    private String rejectReason;
}
