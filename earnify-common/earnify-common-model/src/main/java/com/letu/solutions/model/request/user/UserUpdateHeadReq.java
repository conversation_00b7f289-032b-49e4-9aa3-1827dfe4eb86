package com.letu.solutions.model.request.user;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class UserUpdateHeadReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 头像地址
     */
    @NotEmpty(message = "$头像地址不能为空")
    @Size(min = 10, max = 255, message = "$头像地址不合法")
    private String imgUrl;
}
