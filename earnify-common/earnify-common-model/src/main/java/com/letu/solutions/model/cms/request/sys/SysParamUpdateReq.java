package com.letu.solutions.model.cms.request.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 系统参数
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Data
public class SysParamUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    @NotNull(message = "不可为空")
    private Long id;
    /**
     * 参数名
     */
    @NotEmpty(message = "参数名不可为空")
    private String name;
    /**
     * 参数key
     */
    @NotEmpty(message = "参数key不可为空")
    private String key;
    /**
     * 参数值
     */
    @NotEmpty(message = "参数值不可为空")
    private String value;
    /**
     * 参数描述
     */
    private String describe;
}
