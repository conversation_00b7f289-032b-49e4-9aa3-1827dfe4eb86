package com.letu.solutions.model.cms.response.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.letu.solutions.model.entity.sys.SysRole;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 系统角色
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父角色id
     */
    private Long parentId;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色描述
     */
    private String roleDesc;

    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;

    /**
     * 子集
     */
    private List<SysRoleResponse> children;


}
