package com.letu.solutions.model.cms.response.cms;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;

/**
 * 基础趋势响应数据
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
public class BaseTrendRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;
}
