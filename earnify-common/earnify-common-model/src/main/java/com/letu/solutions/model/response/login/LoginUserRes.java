package com.letu.solutions.model.response.login;

import lombok.Data;

import java.io.Serializable;

/**
 * 会员
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class LoginUserRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID 用户id
     */
    private Long id;
    /**
     * 是否完善信息  0 否 1 是
     */
    private Integer completeInfo;

    /**
     * 认证状态; 0 否 1 是
     */
    private Integer certification;
    /**
     *  Vip; 0 否 1 是
     */
    private Integer vip;
    /**
     * 是否已经完成测试题;  0 否 1 是
     */
    private Integer test;
    /**
     * 头像地址
     */
    private String imgUrl;
    /**
     * 是否未注册:
     * true表示未注册，需要上报注册事件和登录事件
     * 非 true表示已注册，只需要上报登录事件
     */
    private Boolean reg;
}
