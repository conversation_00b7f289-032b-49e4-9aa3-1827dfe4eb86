package com.letu.solutions.model.cms.request.user;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户阅读记录表
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
public class UserSearchSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 用户搜索关键词
     */
    private String keywords;
    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不可为空")
    private Date createdAt;
    /**
     * 修改时间
     */
    @NotNull(message = "修改时间不可为空")
    private Date updatedAt;
}
