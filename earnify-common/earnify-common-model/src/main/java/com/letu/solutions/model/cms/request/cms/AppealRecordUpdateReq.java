package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 申述记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class AppealRecordUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不可为空")
    private Long id;

    /**
     * 申述状态（0待审核，1通过，2不通过
     */
    @NotNull(message = "申述状态不可为空")
    private Integer appealStatus;

}
