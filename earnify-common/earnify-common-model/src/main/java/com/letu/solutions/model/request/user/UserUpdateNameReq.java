package com.letu.solutions.model.request.user;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class UserUpdateNameReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户昵称
     */
    @NotEmpty(message = "$用户昵称不能为空")
    @Size(min = 2, max = 16, message = "$用户昵称不合法")
    private String nickName;
}
