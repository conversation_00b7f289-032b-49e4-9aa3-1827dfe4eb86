package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 平台账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class PlatformTransactionBillDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 动账ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String userName;
    /**
     * 账号类型（0=甲方账户，1=乙方账户）
     */
    private Integer accountType;
    /**
     * 动账类型（0=充值，1=提现）
     */
    private Integer actionType;
    /**
     * 方向（1=入账，2=出账）
     */
    private Integer side;
    /**
     * 动账数量
     */
    private BigDecimal amount;
    /**
     * 币种（如 USDT、ETH）
     */
    private String coinSymbol;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 变动前金额
     */
    private BigDecimal balanceBefore;
    /**
     * 变动后金额
     */
    private BigDecimal balanceAfter;
    /**
     * 充值或提现关联id
     */
    private Long fundId;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 更新时间
     */
    private Date updatedAt;
}
