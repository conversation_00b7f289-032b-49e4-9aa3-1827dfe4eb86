package com.letu.solutions.model.cms.response.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 系统参数
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Data
public class SysParamPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    private Long id;
    /**
     * 参数名
     */
    private String name;
    /**
     * 参数key
     */
    private String key;
    /**
     * 参数值
     */
    private String value;
    /**
     * 参数描述
     */
    private String describe;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
}
