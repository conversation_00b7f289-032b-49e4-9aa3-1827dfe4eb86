package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import com.letu.solutions.model.enums.cms.ProductStatusEnum;
import com.letu.solutions.model.enums.cms.ProductTypeEnum;
import lombok.Data;
import java.io.Serializable;

/**
 * 产品表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class ProductDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品logo的URL
     */
    private String logo;
    /**
     * 主图
     */
    private String hostUrl;
    /**
     * 甲方用户ID
     */
    private Long partyUserId;
    /**
     * 产品类型
     */
    private ProductTypeEnum productType;
    /**
     * 产品状态
     */
    private ProductStatusEnum productStatus;
}
