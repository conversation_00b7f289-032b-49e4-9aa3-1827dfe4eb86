package com.letu.solutions.model.request.task;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.List;

/**
 * 完成步骤请求
 */
@Data
public class FinishStepRequest {
    /**
     * 任务ID（可选，用于自动领取任务）
     */
    private Long taskId;
    
    /**
     * 用户任务ID（可选，如果已领取任务则使用此字段）
     */
    private Long userTaskId;
    
    /**
     * 步骤排序（可选，用于标识要完成的步骤）
     */
    private Integer srot;
    
    /**
     * 用户任务步骤ID（可选，如果已领取任务则使用此字段）
     */
    private Long userTaskStepId;
    
    /**
     * 任务文字操作（用户填写的文字内容）
     */
    private String taskTextOperate;
    
    /**
     * 任务图片操作（用户上传的图片URL列表）
     */
    private List<String> taskImageOperate;
} 