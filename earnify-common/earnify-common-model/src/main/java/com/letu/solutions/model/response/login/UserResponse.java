package com.letu.solutions.model.response.login;

import com.letu.solutions.share.model.enums.SexEnum;
import com.letu.solutions.share.model.enums.user.UserStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID 用户id
     */
    private Long id;

    /**
     * 手机号码
     */
    private String userPhone;

    /**
     * 用户头像
     */
    private String userImage;

    /**
     * 昵称
     */
    private String nickName;


    /**
     * 用户类别 NORMAL正常用户 VIP 用户
     */
    private String userType;


    /**
     * 是否创作者 1是 0否
     */
    private Integer creator;

    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;

    /**
     * 实名认证状态[1:已认证;0:未认证]
     */
    private Integer hasThreeCert;
    /**
     * 用户创建时间
     */
    private Date createTime;

    /**
     * 用户状态[common:普通用户;cert:已实名;]
     */
    private UserStatusEnum status;

    /**
     * 用户创建日期
     */
    private Integer day;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 性别
     */
    private SexEnum sex;
    /**
     * vip到期时间
     */
    private Date vipExpiredTime;

    /**
     * 登录类型：0--游客登录，1--用户登陆
     */
    private Integer loginType = 1;
}
