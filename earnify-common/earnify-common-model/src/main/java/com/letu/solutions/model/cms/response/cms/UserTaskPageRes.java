package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import lombok.Data;
import java.io.Serializable;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTaskPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 任务状态 枚举
     */
    private UserTaskStatusEnum state;
    /** 任务名称 */
    private String taskName;
    /** 甲方账号昵称 */
    private String partyANickName;
    /** 甲方id */
    private Long partyAId;
    /** 乙方账号昵称 */
    private String partyBNickName;
    /** 乙方id */
    private Long partyBId;
    /** 关联产品id */
    private Long productId;
    /** 产品名称 */
    private String productName;
    /** 任务属性 */
    private Integer attribute;
    /** 任务类型 */
    private TaskTypeEnum taskType;
    /** 是否排重 */
    private Integer weightSorting;
    /** 完成进度 */
    private String progress;
    /** 任务完成时限 */
    private Integer time;
    /** 任务奖励 */
    private BigDecimal price;
    /** 任务创建时间 */
    private String createTime;
    /**
     * 申诉到期时间
     */
    private Date appealTime;
    /**
     * 任务审核时间
     */
    private Date examineTime;
}
