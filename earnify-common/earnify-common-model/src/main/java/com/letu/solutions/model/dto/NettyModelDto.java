package com.letu.solutions.model.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NettyModelDto<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    // 客户端消息体
    private byte[] serverModelBytes;
    /**
     * 消费类型
     */
    private Integer type;
    /**
     * 业务消息体
     */
    private T data;
    /**
     * 客户端消息来源用户信息
     */
    private Long userId;

    public <T> T loadParam(Class<T> clazz) {
        if (null == data && ObjectUtil.isEmpty(data)) {
            try {
                return ReflectUtil.newInstance(clazz);
            } catch (Exception e) {
                return null;
            }
        }
        if (null != data && data instanceof LinkedHashMap) {
            try {
                return BeanUtil.mapToBean((LinkedHashMap) data, clazz, false, null);
            } catch (Exception e) {
                return ReflectUtil.newInstance(clazz);
            }
        }
        return (T) data;
    }
}
