package com.letu.solutions.model.cms.response.cms;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 甲方用户管理响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
public class ClientUserManagementRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 关联产品数
     */
    private Integer relatedProductCount;

    /**
     * 发布任务次数
     */
    private Integer publishTaskTimes;

    /**
     * 发布任务个数
     */
    private Integer publishTaskCount;

    /**
     * 任务完成已领取个数
     */
    private Integer taskCompletedReceivedCount;

    /**
     * 已完成个数
     */
    private Integer taskCompletedCount;

    /**
     * 已发放金额
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private BigDecimal totalGrantedAmount;

    /**
     * 发放奖励冻结中金额
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private BigDecimal rewardFrozenAmount;

    /**
     * 累计充值
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private BigDecimal totalRechargeAmount;

    /**
     * 当前余额
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private BigDecimal currentBalance;

    /**
     * 提现中
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private BigDecimal withdrawingAmount;

    /**
     * 已提现
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private BigDecimal totalWithdrawnAmount;

    /**
     * 创建时间
     */
    private Date createTime;
}
