package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 账户流水表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTransactionBillUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不可为空")
    private Long id;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 账号类型（0=甲方，1=乙方）
     */
    @NotNull(message = "账号类型（0=甲方，1=乙方）不可为空")
    private Integer accountType;
    /**
     * 动账类型（0=任务奖励/保证金冻结，1=任务奖励冻结，2=任务奖励到账，3=任务奖励解冻发放，4=任务奖励/保证金解冻返还，5=任务奖励解冻返还，6=充值，7=提现，8=提现审核驳回）
     */
    @NotNull(message = "动账类型（0=任务奖励/保证金冻结，1=任务奖励冻结，2=任务奖励到账，3=任务奖励解冻发放，4=任务奖励/保证金解冻返还，5=任务奖励解冻返还，6=充值，7=提现，8=提现审核驳回）不可为空")
    private Integer actionType;
    /**
     * 方向（1=入账，2=出账）
     */
    @NotNull(message = "方向（1=入账，2=出账）不可为空")
    private Integer side;
    /**
     * 动账金额
     */
    @NotNull(message = "动账金额不可为空")
    private BigDecimal amount;
    /**
     * 币种
     */
    @NotEmpty(message = "币种不可为空")
    private String coinSymbol;
    /**
     * 手续费
     */
    private BigDecimal fee;
    /**
     * 变动前金额
     */
    @NotNull(message = "变动前金额不可为空")
    private BigDecimal balanceBefore;
    /**
     * 变动后金额
     */
    @NotNull(message = "变动后金额不可为空")
    private BigDecimal balanceAfter;
    /**
     * 冻结金额
     */
    @NotNull(message = "冻结金额不可为空")
    private BigDecimal frozen;
    /**
     * 充值或提现关联id
     */
    private Long fundId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
