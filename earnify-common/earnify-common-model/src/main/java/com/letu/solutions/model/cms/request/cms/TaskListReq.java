package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    private Long id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 发布者id
     */
    private Long userId;
    /**
     * 发布者账号
     */
    private String nickName;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 开始时间
     */
    private String staTime;
    /**
     * 结束时间
     */
    private String finishTime;
}
