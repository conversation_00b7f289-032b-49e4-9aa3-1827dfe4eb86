package com.letu.solutions.model.request.invite;

import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 邀请列表查询请求
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InviteListQueryRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邀请状态
     */
    private String status;

    /**
     * 奖励状态
     */
    private String rewardStatus;

} 