package com.letu.solutions.model.cms.request.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户阅读记录表
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
public class UserReadingUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 设备ID
     */
    @NotEmpty(message = "设备ID不可为空")
    private String deviceId;
    /**
     * 书籍ID
     */
    @NotNull(message = "书籍ID不可为空")
    private Long bookId;
    /**
     * 作品类型（SHORT, LONG）
     */
    @NotEmpty(message = "作品类型（SHORT, LONG）不可为空")
    private String bookType;
    /**
     * 卷ID
     */
    private Long volumeId;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 章节序号
     */
    @NotNull(message = "章节序号不可为空")
    private Integer chapterNumber;
    /**
     * 段落ID
     */
    private Long paragraphId;
    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不可为空")
    private Date createdAt;
    /**
     * 修改时间
     */
    @NotNull(message = "修改时间不可为空")
    private Date updatedAt;
    /**
     * ID
     */
    @NotNull(message = "ID不可为空")
    private Long id;
}
