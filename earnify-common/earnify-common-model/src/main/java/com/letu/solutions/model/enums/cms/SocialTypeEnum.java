package com.letu.solutions.model.enums.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 社交媒体类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Getter
@AllArgsConstructor
public enum SocialTypeEnum {

    /**
     * Twitter
     */
    TWITTER("TWITTER", "推特"),

    /**
     * Telegram
     */
    TELEGRAM("TELEGRAM", "电报"),

    /**
     * Discord
     */
    DISCORD("DISCORD", "Discord"),

    /**
     * Reddit
     */
    REDDIT("REDDIT", "Reddit"),

    /**
     * TikTok
     */
    TIKTOK("TIKTOK", "抖音"),

    /**
     * Medium
     */
    MEDIUM("MEDIUM", "Medium");

    private final String code;
    private final String desc;

} 