package com.letu.solutions.model.cms.response.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serializable;

/**
 * 用户
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class UserDetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;
    /**
     * 手机号码
     */
    private String userPhone;
    /**
     * 用户头像
     */
    private String userImage;
    /**
     * 用户类别
     */
    private String userType;
    /**
     * 是否创作者 1是 0否
     */
    private Integer creator;
    /**
     * 密码
     */
    private String pwd;
    /**
     * 盐
     */
    private String salt;
    /**
     * 用户状态[common:普通用户;cert:已实名;]
     */
    private String status;
    /**
     * 用户创建日期
     */
    private Integer day;
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;
    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 渠道来源
     */
    private String sourceCode;
    /**
     *昵称
     */
    private String nickName;
    /**
     * 账户类型: 甲方(CLIENT), 乙方(PROVIDER)
     */
    private AccountTypeEnum accountRole;
}
