package com.letu.solutions.model.customer.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 提现申请请求
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class WithdrawApplyReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 协议网络（如 ERC20, TRC20, BEP20）
     */
    @NotBlank(message = "协议网络不能为空")
    private String network;

    /**
     * 提现金额
     */
    @NotNull(message = "提现金额不能为空")
    @DecimalMin(value = "0.01", message = "提现金额必须大于0.01")
    private BigDecimal amount;

    /**
     * 提现地址
     */
    @NotBlank(message = "提现地址不能为空")
    private String withdrawAddress;
}
