package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 用户充值记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class DepositRecordPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID，充值记录ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 协议网络（如 ERC20, TRC20, BEP20）
     */
    private String network;
    /**
     * 充值数量
     */
    private BigDecimal amount;
    /**
     * 币种（如 USDT, ETH）
     */
    private String currency;
    /**
     * 用户付款钱包地址
     */
    private String fromAddress;
    /**
     * 平台收款钱包地址
     */
    private String toAddress;
    /**
     * 链上交易哈希（Txn Hash）
     */
    private String txHash;
    /**
     * 截图凭证，最多支持3张图片URL
     */
    private List<String> evidenceImages;
    /**
     * 处理人员（后台管理员）
     */
    private String operator;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
