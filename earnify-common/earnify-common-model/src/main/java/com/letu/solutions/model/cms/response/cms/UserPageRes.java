package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 用户
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;
    /**
     * 手机号码
     */
    private String userPhone;
    /**
     * 用户头像
     */
    private String userImage;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 用户类别 NORMAL正常用户 VIP vip用户
     */
    private String userType;
    /**
     * 密码
     */
    private String pwd;
    /**
     * 盐
     */
    private String salt;
    /**
     * 用户状态[common:普通用户;cert:已实名;]
     */
    private String status;
    /**
     * 用户创建日期
     */
    private Integer day;
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;
    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 渠道来源
     */
    private String sourceCode;
    /**
     * 账户类型: 1=甲方(CLIENT), 2=乙方(PROVIDER)
     */
    private Integer accountRole;
    /**
     * 推荐人ID，引用user.id
     */
    private Long recommendId;
    /**
     * 用户推荐码
     */
    private String recommendCode;
    /**
     * 国家名称
     */
    private String countryName;
}
