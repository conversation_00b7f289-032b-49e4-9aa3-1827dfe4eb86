package com.letu.solutions.model.request.login;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class CertRegisterRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    @NotEmpty(message = "$手机号不可为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;
    /**
     * 密码
     */
    @NotEmpty(message = "$密码不可为空")
    @Size(min = 6, max = 16, message = "$请输入6-16位密码")
    private String pwd;

    /**
     * 验证码
     */
    @NotEmpty(message = "$验证码不可为空")
    @Size(min = 4, max = 4, message = "$请输入4位验证码")
    private String smsCode;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 身份证号
     */
    @NotEmpty(message = "$身份证号不可为空")
    @Pattern(regexp = "^(\\d{18}|\\d{17}x|\\d{17}X)$", message = "$身份证号不合法")
    private String idCard;
    /**
     * 真实姓名
     */
    @NotEmpty(message = "$真实姓名不可为空")
    @Pattern(regexp = "^[\\u00B7\\u3007\\u3400-\\u4DBF\\u4E00-\\u9FFF\\uE000-\\uF8FF\\uD840-\\uD8C0\\uDC00-\\uDFFF\\uF900-\\uFAFF]+$", message = "$真实姓名不合法")
    @Size(min = 2, message = "$真实姓名不合法")
    private String name;
}
