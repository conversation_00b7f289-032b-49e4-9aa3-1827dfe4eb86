package com.letu.solutions.model.log;

import lombok.Data;
import org.springframework.data.annotation.Id;

/**
 * <AUTHOR>
 * @date 2020/10/10 14:51
 */
@Data
public class IpEntity {

    @Id
    private Long id;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 国家
     */
    private String countryName;
    /**
     * 国家代码
     */
    private String countryCode;
    /**
     * 省份
     */
    private String provinceName;
    /**
     * 省份代码
     */
    private String provinceCode;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 城市邮政编码
     */
    private String cityCode;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;

}