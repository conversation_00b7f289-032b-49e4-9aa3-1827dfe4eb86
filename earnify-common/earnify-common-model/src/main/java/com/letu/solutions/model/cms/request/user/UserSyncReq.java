package com.letu.solutions.model.cms.request.user;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
public class UserSyncReq implements Serializable {

    private List<Long> ids;

    private List<Long> phones;
}
