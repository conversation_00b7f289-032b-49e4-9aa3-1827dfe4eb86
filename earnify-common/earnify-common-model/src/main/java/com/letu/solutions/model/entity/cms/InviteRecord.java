package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 邀请记录表
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@TableName("invite_record")
public class InviteRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邀请记录唯一标识符，自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀请人用户ID
     */
    private Long inviterId;

    /**
     * 顶级邀请人ID（一级邀请人）
     */
    private Long rootInviterId;

    /**
     * 被邀请人用户ID
     */
    private Long inviteeId;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请状态：pending-待注册，registered-已注册，expired-已过期
     */
    private String status;

    /**
     * 邀请奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 奖励状态：pending-待发放，granted-已发放，failed-发放失败
     */
    private String rewardStatus;

    /**
     * 被邀请人注册时间
     */
    private Date registerTime;

    /**
     * 奖励发放时间
     */
    private Date rewardTime;

    /**
     * 邀请过期时间
     */
    private Date expireTime;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 