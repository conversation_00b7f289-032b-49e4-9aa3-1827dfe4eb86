package com.letu.solutions.model.enums.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 钱包类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Getter
@AllArgsConstructor
public enum WalletTypeEnum {

    /**
     * TRC20协议
     */
    TRC20("TRC20", "TRC20"),

    /**
     * ERC20协议
     */
    ERC20("ERC20", "ERC20"),

    /**
     * BEP20协议
     */
    BEP20("BEP20", "BEP20");

    private final String code;
    private final String desc;
}