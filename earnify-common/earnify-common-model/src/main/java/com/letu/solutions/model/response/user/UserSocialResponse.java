package com.letu.solutions.model.response.user;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.letu.solutions.model.enums.cms.SocialTypeEnum;

/**
 * 用户社交媒体绑定响应
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class UserSocialResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 社交媒体绑定列表
     */
    private List<SocialBinding> socialBindings;

    /**
     * 社交媒体绑定项
     */
    @Data
    public static class SocialBinding implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 社交媒体类型
         */
        private String socialType;

        /**
         * 社交媒体类型描述
         */
        private String socialTypeDesc;

        /**
         * 社交媒体链接
         */
        private String socialUrl;

        /**
         * 绑定状态
         */
        private String status;

        /**
         * 状态描述
         */
        private String statusDesc;

        /**
         * 绑定时间
         */
        private Date bindTime;

        /**
         * 验证时间
         */
        private Date verifyTime;
    }
} 