package com.letu.solutions.model.cms.response.cms;

import lombok.Data;
import lombok.Builder;

import java.io.Serializable;
import java.util.List;

/**
 * 任务增长趋势响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
public class TaskGrowthTrendRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 开始日期
     */
    private Integer startDate;

    /**
     * 结束日期
     */
    private Integer endDate;

    /**
     * 每日任务增长数据列表
     */
    private List<DailyTaskGrowthData> dailyGrowthData;

    /**
     * 每日任务增长数据
     */
    @Data
    @Builder
    public static class DailyTaskGrowthData implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 日期，格式：yyyyMMdd
         */
        private Integer date;

        /**
         * 发布任务数
         */
        private Integer publishedTasks;

        /**
         * 领取任务数
         */
        private Integer receivedTasks;

        /**
         * 完成任务数
         */
        private Integer completedTasks;
    }
}
