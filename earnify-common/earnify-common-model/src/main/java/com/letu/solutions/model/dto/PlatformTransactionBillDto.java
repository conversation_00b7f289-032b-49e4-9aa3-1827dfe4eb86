package com.letu.solutions.model.dto;

import lombok.Builder;
import lombok.Data;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import java.math.BigDecimal;

@Data
@Builder
public class PlatformTransactionBillDto {
    private Long userId;
    private String userName;
    private AccountTypeEnum accountType;
    private FundTypeEnum fundType;
    private FundSideTypeEnum side;
    private BigDecimal amount;
    private String currency;
    private BigDecimal fee;
    private BigDecimal before;
    private BigDecimal after;
    private Long fundId;
    private String remark;
    private Long platformUserId;
} 