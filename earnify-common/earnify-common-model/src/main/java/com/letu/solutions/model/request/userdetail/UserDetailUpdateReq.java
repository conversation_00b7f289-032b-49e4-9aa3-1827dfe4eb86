package com.letu.solutions.model.request.userdetail;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * 用户详情表
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
public class UserDetailUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户详情唯一标识符
     */
    @NotNull(message = "$用户详情唯一标识符不可为空")
    private Long id;
    /**
     * 关联的用户ID
     */
    private Long userId;
    /**
     * 用户昵称
     */
    @NotEmpty(message = "$用户昵称不可为空")
    private String name;
    /**
     * 性别[SexEnum]
     */
    @NotEmpty(message = "$性别[SexEnum]不可为空")
    private String gender;
    /**
     *
     */
    @NotEmpty(message = "$不可为空")
    private String imgUrl;
    /**
     * 最初登录时间
     */
    private Date firstLoginTime;
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;
    /**
     * 最后离线时间[有Im业务才有]
     */
    private Date lastLeaveTime;
    /**
     * 最初登录平台
     */
    private String lastLoginPlatform;
    /**
     * 注册平台
     */
    @NotEmpty(message = "$注册平台不可为空")
    private String registerPlatform;
    /**
     * 真实姓名
     */
    @NotEmpty(message = "$$真实姓名不可为空")
    private String realName;
    /**
     * 身份证号
     */
    @NotEmpty(message = "$身份证号不可为空")
    private String idCard;
    /**
     * 身份证正面
     */
    @NotEmpty(message = "$身份证正面不可为空")
    private String idCardFront;
    /**
     * 身份证反面
     */
    @NotEmpty(message = "$身份证反面不可为空")
    private String idCardBack;
    /**
     * 生日
     */
    private Date birthday;
    /**
     * 身高
     */
    private Long height;
    /**
     * 重量
     */
    private Long weight;
    /**
     * 介绍
     */
    private String intro;
}
