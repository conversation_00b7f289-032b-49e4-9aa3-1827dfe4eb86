package com.letu.solutions.model.request.user;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;

@Data
public class CustomerUserRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @NotEmpty(message = "$手机号不可为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;
}
