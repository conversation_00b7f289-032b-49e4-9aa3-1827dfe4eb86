package com.letu.solutions.model.cms.response.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

import java.io.Serializable;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
public class SysOperatePageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *
     */
    private Long id;
    /**
     * 操作日期
     */
    private Integer day;
    /**
     * 日志类型['common','error']
     */
    private String logType;
    /**
     * 操作人员id
     */
    private Long userId;
    /**
     * 操作人员名称
     */
    private String userName;
    /**
     * 操作接口名称
     */
    private String methodNameAll;
    /**
     * 操作接口描述
     */
    private String methodDescAll;
    /**
     * 请求URL
     */
    private String requestUrl;
    /**
     * 主机地址
     */
    private String requestIp;
    /**
     * 操作地点(登陆地)
     */
    private String location;
    /**
     * 请求参数
     */
    private String requestParam;
    /**
     * 请求结果[1:操作成功;0:操作失败]
     */
    private Integer operateRes;
    /**
     * 返回参数
     */
    private String result;
    /**
     * 错误消息
     */
    private String errorMsg;
    /**
     * 创建时间
     */
    private Date createTime;
}
