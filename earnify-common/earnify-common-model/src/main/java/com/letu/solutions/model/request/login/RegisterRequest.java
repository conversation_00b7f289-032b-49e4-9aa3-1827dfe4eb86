package com.letu.solutions.model.request.login;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegisterRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 手机号
     */
    @NotEmpty(message = "$手机号不可为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;
    /**
     * 密码
     */
    @NotEmpty(message = "$密码不可为空")
    @Size(min = 6, max = 16, message = "$请输入6-16位密码")
    private String pwd;

    /**
     * 验证码
     */
    @NotEmpty(message = "$验证码不可为空")
    @Size(min = 4, max = 4, message = "$请输入4位验证码")
    private String smsCode;

    /**
     * 邀请码
     */
    private String inviteCode;
    /**
     * 邮箱地址
     */
    private String email;

    /**
     * google sub
     */
    private String googleSub;
}
