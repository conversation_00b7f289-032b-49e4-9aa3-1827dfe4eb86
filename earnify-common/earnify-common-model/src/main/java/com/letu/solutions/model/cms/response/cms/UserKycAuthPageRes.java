package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * KYC身份认证表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserKycAuthPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID，关联user表
     */
    private Long userId;
    /**
     * 证件类型：1=身份证，2=护照，3=驾驶执照
     */
    private Integer idType;
    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 证照图片数组
     */
    private String documentImages;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 审核状态：0待审核，1已通过，2未通过
     */
    private Integer verifyStatus;
    /**
     * 审核时间
     */
    private Date verifyTime;
    /**
     * 审核备注或失败原因
     */
    private String remark;
}
