package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.ActionTypeEnum;
import com.letu.solutions.model.enums.cms.WithdrawStatusEnum;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 提现记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WithdrawRecordListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID，提现记录ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 提现状态（未处理，未通过，已打款）
     */
    private WithdrawStatusEnum withdrawStatus;
    /**
     * 账户类型（甲方账户，乙方账户）
     */
    private AccountTypeEnum accountType;
    /**
     * 操作类型（用户操作，平台操作）
     */
    private ActionTypeEnum actionType;
    /**
     * 协议网络（如 ERC20, TRC20）
     */
    private String network;
}
