package com.letu.solutions.model.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BaseRequest {

    /**
     * 开始日期:yyyyMMdd 20210909
     */
    private Integer beginDate;
    /**
     * 结束日期:yyyyMMdd
     */
    private Integer endDate;

    /**
     * 开始时间:yyyyMMddHHmmss 20210909090900
     */
    private Long beginTime;
    /**
     * 结束时间:yyyyMMddHHmmss 20210909090900
     */
    private Long endTime;
    /**
     * yyyy-MM-dd HH:mm:ss 2025-03-26 00:00:00
     */
    private String startDateTime;
    /**
     * yyyy-MM-dd HH:mm:ss 2025-03-26 00:00:00
     */
    private String endDateTime;

    /**
     * 公共查询字符串
     */
    private String query;

    /**
     * 公用id查询
     */
    private Long id;

    /**
     * 当前页码
     */
    @TableField(exist = false)
    private Integer current = 1;
    /**
     * 每页大小
     */
    @TableField(exist = false)
    private Integer size = 10;

    /**
     * 当前坐标
     */
    @TableField(exist = false)
    private Integer start = 0;

    public Integer getStart() {
        this.current = null == this.current ? 1 : this.current;
        this.size = null == this.size ? 10 : this.size;
        return (this.current - 1) * this.size;
    }

    /**
     * 获取分页对象
     *
     * @return 分页
     */
    public <T> Page<T> getPage() {
        if (current == null || current <= 0) {
            current = 1;
        }
        if (size == null || size <= 0) {
            size = 10;
        }
        return new Page<T>(current, size);
    }

    public <T> Page<T> getExportPage() {
        if (current == null || current <= 0) {
            current = 1;
        }
        if (size == null || size <= 0) {
            size = 10000;
        }
        return new Page<T>(current, size);
    }
}
