package com.letu.solutions.model.response.customer;

import com.letu.solutions.model.cms.response.cms.TaskStepPageRes;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 我的任务分页VO
 */
@Data
public class UserTaskPageRes {
    /**
     * 用户任务ID
     */
    private Long id;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务步骤列表
     */
    private List<TaskStepPageRes> stepList;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 任务状态
     */
    private String state;
    /**
     * 任务奖励金额
     */
    private BigDecimal price;
    /**
     * 到期时间
     */
    private Date expireTime;
    /**
     * 审核中倒计时（秒）
     */
    private Long auditCountdown;
    /**
     * 状态展示内容
     */
    private String statusDisplay;
    /**
     * 领取时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 甲方用户信息
     */
    private PartyUserInfoRes partyUserInfo;
    /**
     * 申诉时间
     */
    private Date appealTime;
    
    /**
     * 任务到期时间
     */
    private Date time;

    /**
     * 任务到期倒计时（秒）
     */
    private Long expireCountdownSeconds;
    
    /**
     * 申诉倒计时（秒）
     */
    private Long appealCountdown;
    /**
     * 用户任务进度（如：3/5）
     */
    private String userTaskProgress;

} 