package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务属性 1人工 2自动
     */
    @NotNull(message = "任务属性 1人工 2自动不可为空")
    private Integer attribute;
    /**
     * 任务名称
     */
    @NotEmpty(message = "任务名称不可为空")
    private String name;
    /**
     * 发布者id
     */
    @NotNull(message = "发布者id不可为空")
    private Long userId;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 是否排重 1排重 2不排重
     */
    @NotNull(message = "是否排重 1排重 2不排重不可为空")
    private Integer weightSorting;
    /**
     * 任务类型 枚举
     */
    @NotNull(message = "任务类型 枚举不可为空")
    private TaskTypeEnum taskType;
    /**
     * 可接任务数量
     */
    @NotNull(message = "可接任务数量不可为空")
    @Min(value = 1, message = "可接任务数量必须大于 0")
    private Integer number;
    /**
     * 单价
     */
    @NotNull(message = "单价不可为空")
    @DecimalMin(value = "0.01", inclusive = true, message = "单价必须大于 0")
    private BigDecimal price;
    /**
     * 任务有效期 天数
     */
    @NotNull(message = "任务有效期 天数不可为空")
    private Integer time;
    /**
     * 步骤数量
     */
    @NotNull(message = "步骤数量不可为空")
    private Integer stepNumber;
    /**
     * 保证金
     */
    private Integer bond;
    /**
     * 任务图片
     */
    private String url;
    /**
     * 任务步骤集合
     */
    @NotNull(message = "任务步骤集合不可为空")
    @Valid
    private List<TaskStepSaveReq> taskStepSaveReqList;
}
