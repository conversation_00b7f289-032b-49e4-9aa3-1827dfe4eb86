package com.letu.solutions.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OSS文件上传目录枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OssEnum {
    /**
     * 常规业务
     */
    BUSINESS("常规业务", "business", 1),
    /**
     * banner相关图片
     */
    BANNER_PATH("banner相关图片", "banner", 2),
    /**
     * 用户头像相关
     */
    HEAD_IMG("用户头像相关", "head", 3),

    ;

    /**
     * 描述
     */
    private String describe;

    /**
     * 上传目录路径
     */
    private String path;

    /**
     * 类型
     */
    private Integer type;
}
