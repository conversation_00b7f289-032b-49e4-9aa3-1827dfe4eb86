package com.letu.solutions.model.enums.cms;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {


    /**
     * 待接单
     */
    pendingOrders( "待接单",1),
    /**
     * 进行中
     */
    inProgress( "进行中",2),
    /**
     * 已完成
     */
    completed( "已完成",3),
    /**
     * 已撤销
     */
    revoked( "已撤销",4),
    ;


    private String desc;
    private Integer value;
}
