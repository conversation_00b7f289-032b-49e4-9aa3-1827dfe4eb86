package com.letu.solutions.model.request.login;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @description 一键登录
 * <AUTHOR>
 * @createTime 2025/5/27 16:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OneClickLoginReq implements Serializable {

    /**
     * token
     */
    @NotBlank(message = "token不可为空")
    private String token;
}
