package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.letu.solutions.model.enums.cms.StepTypeEnum;

/**
 * 步骤
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("task_step")
public class TaskStep implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 产品自增ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 任务id
    */
    private Long taskId;

    /**
    * 任务描述
    */
    private String taskDescribe;

    /**
    * 单价
    */
    private BigDecimal price;

    /**
    * 排序
    */
    private Integer srot;

    /**
    * 步骤类型：支持多选，用逗号分隔，如 "text,image" 表示文字+图片
    * 可选值：text-文本，image-图片
    */
    private String stepType;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;
}
