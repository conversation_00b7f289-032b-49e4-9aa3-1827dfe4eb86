package com.letu.solutions.model.customer.request;

import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户资产查询请求
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserAssetListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

}
