package com.letu.solutions.model.enums.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.dubbo.remoting.http12.rest.Schema;

/**
 * 资金变化类型
 */
@Getter
@AllArgsConstructor
public enum FundTypeEnum {

    /**
     * 任务奖励冻结：余额增加 + 冻结金额增加
     */
    task_reward_freeze("任务奖励冻结", FundSideTypeEnum.in, FrozenSideTypeEnum.freeze),

    /**
     * 保证金冻结：余额增加 + 冻结金额增加
     */
    deposit_freeze("保证金冻结", FundSideTypeEnum.in, FrozenSideTypeEnum.freeze),

    /**
     * 任务奖励到账：直接入账，余额增加
     */
    task_reward_to_account("任务奖励到账", FundSideTypeEnum.in, null),

    /**
     * 任务奖励解冻并发放：余额减少 + 冻结金额减少
     */
    task_reward_unfreeze_send("任务奖励解冻发放", null, FrozenSideTypeEnum.unfreeze),

    /**
     * 任务奖励解冻返还：余额减少 + 冻结金额减少
     */
    task_reward_unfreeze_refund("任务奖励解冻返还", FundSideTypeEnum.out, FrozenSideTypeEnum.unfreeze),

    /**
     * 保证金解冻返还：余额减少 + 冻结金额减少
     */
    deposit_unfreeze_refund("保证金解冻返还", FundSideTypeEnum.out, FrozenSideTypeEnum.unfreeze),

    /**
     * 充值：余额增加
     */
    deposit("充值", FundSideTypeEnum.in, null),

    /**
     * 提现成功：余额减少
     */
    withdraw("提现", FundSideTypeEnum.out, null),

    /**
     * 提现申请-冻结资金：冻结金额增加，不变更余额
     */
    withdraw_freeze("提现申请-资金冻结", null, FrozenSideTypeEnum.freeze),

    /**
     * 提现审核通过-解冻资金：冻结金额减少，不变更余额
     */
    withdraw_unfreeze("提现审核通过-资金解冻", null, FrozenSideTypeEnum.unfreeze),

    /**
     * 提现审核失败-恢复余额：余额增加 + 冻结金额减少
     */
    withdraw_restore("提现审核失败-资金恢复", FundSideTypeEnum.in, FrozenSideTypeEnum.unfreeze),

    /**
     * 提现审核驳回：余额增加 + 冻结金额减少
     */
    withdraw_rejected("提现审核驳回", FundSideTypeEnum.in, FrozenSideTypeEnum.unfreeze);

    /**
     * 中文描述
     */
    private final String desc;

    /**
     * 金额方向：入账（in）或出账（out）
     */
    private final FundSideTypeEnum side;

    /**
     * 冻结方向：增加冻结金额（freeze），减少冻结金额（unfreeze）
     */
    private final FrozenSideTypeEnum frozenSide;
}
