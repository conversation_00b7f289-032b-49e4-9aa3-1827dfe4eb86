package com.letu.solutions.model.cms.response.cms;

import com.letu.solutions.model.enums.cms.CertificateTypeEnum;
import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户KYC认证信息响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserKycAuthInfoRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 证件类型
     */
    private CertificateTypeEnum idType;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 上传证件数量
     */
    private Integer documentCount;

    /**
     * 证件图片URL列表
     */
    private List<String> documentImages;

    /**
     * 审核状态
     */
    private UserVerifyStatusEnum verifyStatus;

    /**
     * 审核不通过原因
     */
    private String rejectReason;

}
