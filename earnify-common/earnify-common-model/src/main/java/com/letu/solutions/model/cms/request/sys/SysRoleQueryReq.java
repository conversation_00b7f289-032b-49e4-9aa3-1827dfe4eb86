package com.letu.solutions.model.cms.request.sys;

import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 系统角色分页入参
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleQueryReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 状态[1:启用;0:禁用]
     */
    private Integer enable;


}
