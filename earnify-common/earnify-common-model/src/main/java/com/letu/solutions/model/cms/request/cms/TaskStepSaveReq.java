package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.validation.constraints.Size;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import com.letu.solutions.model.enums.cms.StepTypeEnum;

/**
 * 步骤
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskStepSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务描述
     */
    @NotEmpty(message = "任务描述不可为空")
    private String taskDescribe;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 排序
     */
    @NotNull(message = "排序不可为空")
    private Integer srot;

    /**
     * 步骤类型列表（支持多选：TEXT、IMAGE等）
     */
    @NotNull(message = "stepTypes 不能为空")
    @Size(min = 1, message = "stepTypes 至少包含一个元素")
    private List<StepTypeEnum> stepTypes;
}
