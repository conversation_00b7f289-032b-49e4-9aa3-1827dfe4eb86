package com.letu.solutions.model.response.task;

import lombok.Data;

import java.io.Serializable;

/**
 * 任务领取响应
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class TaskReceiveResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户任务ID
     */
    private Long userTaskId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 消息
     */
    private String message;

    public TaskReceiveResponse() {
    }

    public TaskReceiveResponse(Long userTaskId, Long taskId, String message) {
        this.userTaskId = userTaskId;
        this.taskId = taskId;
        this.message = message;
    }

    /**
     * 创建成功响应
     */
    public static TaskReceiveResponse success(Long userTaskId, Long taskId) {
        return new TaskReceiveResponse(userTaskId, taskId, "领取成功");
    }
} 