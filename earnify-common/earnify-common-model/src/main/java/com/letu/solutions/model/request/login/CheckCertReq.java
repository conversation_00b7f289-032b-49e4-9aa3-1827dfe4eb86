package com.letu.solutions.model.request.login;

import com.letu.solutions.share.model.enums.MessageEnum;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 登录请求参数
 */
@Data
public class CheckCertReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 验证码
     */
    @NotEmpty(message = "$验证码不可为空")
    @Size(min = 4, max = 4, message = "$请输入4位验证码")
    private String smsCode;

    /**
     * 手机号 （修改登录密码时必须传，作忘记密码用）
     */
    @Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法")
    private String phone;

    /**
     * 业务类型
     */
    @NotNull(message = "$业务类型不可为空")
    private MessageEnum businessType;
}
