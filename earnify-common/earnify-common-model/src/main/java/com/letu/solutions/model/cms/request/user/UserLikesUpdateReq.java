package com.letu.solutions.model.cms.request.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户点赞记录表
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
public class UserLikesUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @NotNull(message = "主键id不可为空")
    private Long likeId;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 
     */
    @NotNull(message = "不可为空")
    private Long bookId;
    /**
     * 应用appId
     */
    private String appId;
    /**
     * 0取消点赞 1点赞
     */
    @NotNull(message = "0取消点赞 1点赞不可为空")
    private Long likeType;
    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不可为空")
    private Date createdAt;
    /**
     * 修改时间
     */
    @NotNull(message = "修改时间不可为空")
    private Date updatedAt;
}
