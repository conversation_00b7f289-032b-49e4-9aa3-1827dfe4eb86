package com.letu.solutions.model.cms.response.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import java.io.Serializable;

/**
 * 平台钱包地址表
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class PlatformWalletAddressPageRes implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 
     */
    private Long id;
    /**
     * 链名称（如 TRC20、ERC20、BEP20）
     */
    private String network;
    /**
     * 平台钱包地址
     */
    private String address;
    /**
     * 地址用途说明（可选）
     */
    private String tag;
    /**
     * 是否启用：1=启用，0=禁用
     */
    private Integer isEnabled;
    /**
     * 
     */
    private String remark;
    /**
     * 
     */
    private Date createTime;
    /**
     * 
     */
    private Date updateTime;
}
