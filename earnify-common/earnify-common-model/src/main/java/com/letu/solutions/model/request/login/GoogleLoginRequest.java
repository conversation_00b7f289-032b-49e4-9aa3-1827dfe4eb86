package com.letu.solutions.model.request.login;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jack ma
 * @CreateTime: 2025-04-09  15:31
 * @Version: 1.0
 */
@Data
public class GoogleLoginRequest extends BaseLoginRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "$token不能为空")
    private String token;

}
