package com.letu.solutions.model.cms.response.sys;

import com.letu.solutions.model.enums.IconTypeEnum;
import com.letu.solutions.model.enums.SysMenuTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @createDate 2021/9/6 18:58
 */
@Data
public class SysMenuVo implements Serializable {
    private static final long serialVersionUID = -5440146512023383588L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 父菜单id
     */
    private Long parentId;

    /**
     * 菜单链接PATH
     */
    private String path;

    /**
     * 目录类型[menu:菜单,list:目录]
     */
    private SysMenuTypeEnum menuType;

    /**
     * 路由名称
     */
    private String component;

    /**
     * 路由名称
     */
    private String routeName;

    /**
     * 菜单显示名称
     */
    private String title;

    /**
     * icon来源类型
     */
    private IconTypeEnum iconType;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 隐藏标志（0-不隐藏 1-隐藏）
     */
    private Integer hidden;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 子集
     */
    private List<SysMenuVo> children;

}
