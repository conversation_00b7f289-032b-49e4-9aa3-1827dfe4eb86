package com.letu.solutions.model.request.task;

import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import lombok.Data;
import com.letu.solutions.model.request.BaseRequest;

import java.util.List;

/**
 * 任务展示列表查询参数
 * 用于公共任务展示页的筛选和排序
 */
@Data
public class TaskListQueryRequest extends BaseRequest {

    /**
     * 查询名字：name
     */
    private String name;
    /**
     * 查询完成时间：1d=一天内，3d=三天内，7d=七天内，other=其它
     */
    private String time;
    /**
     * 排序字段：create_time=发布时间，price=奖励数量
     */
    private String orderBy;
    /**
     * 排序方式：asc/desc
     */
    private String order;

} 