package com.letu.solutions.model.cms.response.cms;

import com.letu.solutions.model.enums.cms.StepTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务步骤响应
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class TaskStepRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 步骤描述
     */
    private String taskDescribe;

    /**
     * 步骤价格
     */
    private BigDecimal price;

    /**
     * 排序
     */
    private Integer srot;

    /**
     * 步骤类型列表（转换后的枚举列表）
     */
    private List<StepTypeEnum> stepTypes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
