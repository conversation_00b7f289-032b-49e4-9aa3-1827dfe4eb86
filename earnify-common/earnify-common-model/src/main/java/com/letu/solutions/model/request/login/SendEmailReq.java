package com.letu.solutions.model.request.login;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: jack ma
 * @CreateTime: 2025-06-03  15:38
 * @Version: 1.0
 */
@Data
public class SendEmailReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "$邮箱不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "$邮箱格式不合法")
    private String email;
}
