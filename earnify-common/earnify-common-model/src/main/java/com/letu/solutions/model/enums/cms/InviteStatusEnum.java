package com.letu.solutions.model.enums.cms;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 邀请状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Getter
@AllArgsConstructor
public enum InviteStatusEnum {

    /**
     * 待注册
     */
    PENDING("pending", "待注册"),

    /**
     * 已注册
     */
    REGISTERED("registered", "已注册"),

    /**
     * 已过期
     */
    EXPIRED("expired", "已过期");

    private final String code;
    private final String desc;

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static InviteStatusEnum fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (InviteStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 