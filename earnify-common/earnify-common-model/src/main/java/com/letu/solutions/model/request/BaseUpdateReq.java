package com.letu.solutions.model.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 启用禁用实体
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
public class BaseUpdateReq<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @NotNull(message = "$id不可为空")
    private Long id;
    /**
     * id
     */
    @NotNull(message = "$属性不可为空")
    private T data;

}
