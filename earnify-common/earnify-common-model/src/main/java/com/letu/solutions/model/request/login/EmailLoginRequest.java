package com.letu.solutions.model.request.login;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @Author: jack ma
 * @CreateTime: 2025-04-09  14:30
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class EmailLoginRequest extends BaseLoginRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "$邮箱不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "$邮箱格式不合法")
    private String email;

    /**
     * 验证码
     */
    @NotEmpty(message = "$邮箱验证码不能为空")
    @Length(min = 6, max = 6, message = "$邮箱验证码格式不合法")
    private String emailCode;


}
