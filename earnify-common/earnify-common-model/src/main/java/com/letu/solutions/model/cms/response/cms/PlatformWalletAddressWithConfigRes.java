package com.letu.solutions.model.cms.response.cms;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class PlatformWalletAddressWithConfigRes {
    // 地址表字段
    private String network;
    private String address;

    // 配置表字段
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private BigDecimal minWithdraw;
    private BigDecimal maxWithdraw;
    private Integer maxWithdrawTimesPerDay;
    private BigDecimal feeRate;
    private BigDecimal fixedFee;
} 