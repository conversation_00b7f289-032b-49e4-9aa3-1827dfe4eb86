package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import lombok.Data;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("task")
public class Task implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 产品自增ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 任务属性 1人工 2自动
    */
    private Integer attribute;

    /**
    * 任务名称
    */
    private String name;

    /**
    * 发布者id
    */
    private Long userId;

    /**
    * 产品id
    */
    private Long productId;

    /**
    * 是否排重 1排重 2不排重
    */
    private Integer weightSorting;

    /**
    * 任务类型 枚举
    */
    private TaskTypeEnum taskType;

    /**
    * 可接任务数量
    */
    private Integer number;

    /**
    * 单价
    */
    private BigDecimal price;

    /**
    * 任务有效期 小时数
    */
    private Integer time;

    /**
    * 任务状态 枚举
    */
    private TaskStatusEnum state;

    /**
    * 步骤数量
    */
    private Integer stepNumber;

    /**
    * 任务图片
    */
    private String url;
    /**
     * 保证金
     */
    private Integer bond;
    /**
     * 任务领取个数
     */
    private Integer taskReceiveNum;
    /**
     * 任务完成个数
     */
    private Integer taskFinishNum;
    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;
}
