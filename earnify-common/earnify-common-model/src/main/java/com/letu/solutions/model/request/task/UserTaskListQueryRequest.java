package com.letu.solutions.model.request.task;

import lombok.Data;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import com.letu.solutions.model.request.BaseRequest;
import java.util.List;

/**
 * 我的任务列表查询参数
 * 用于用户已领取任务的筛选和排序
 */
@Data
public class UserTaskListQueryRequest extends BaseRequest {
    /**
     * 排序字段：expireTime=到期时间，reward=奖励数量
     */
    private String orderBy;
    /**
     * 排序方式：asc/desc
     */
    private String order;
    /**
     * 任务类型多选
     */
    private List<TaskTypeEnum> typeList;
    /**
     * 任务状态多选
     */
    private List<UserTaskStatusEnum> statusList;
    // 可扩展其它筛选项
} 