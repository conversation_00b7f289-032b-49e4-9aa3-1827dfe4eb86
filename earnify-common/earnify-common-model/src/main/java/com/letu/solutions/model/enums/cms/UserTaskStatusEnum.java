package com.letu.solutions.model.enums.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态
 */
@Getter
@AllArgsConstructor
public enum UserTaskStatusEnum {


    /**
     * 已结束
     */
    ended( "已结束",1),
    /**
     * 进行中
     */
    incomplete( "进行中",2),
    /**
     * 已完成
     */
    completed( "已完成",3),
    /**
     * 待审核
     */
    pendingApproval( "待审核",4),
    /**
     * 待修正
     */
    toBeRevised( "待修正",5),
    /**
     * 甲方驳回
     */
    partyARejects( "甲方驳回",6),
    /**
     * 系统判定失败
     */
    systemFailure( "系统判定失败",7),
    /**
     * 申诉中
     */
    appealInProgress( "申诉中",8),
    /**
     * 申诉不通过
     */
    appealNotPass( "申诉不通过",9),
    /**
     * 申诉通过
     */
    appealPass( "申诉通过",10),
    ;


    private String desc;
    private Integer value;
}
