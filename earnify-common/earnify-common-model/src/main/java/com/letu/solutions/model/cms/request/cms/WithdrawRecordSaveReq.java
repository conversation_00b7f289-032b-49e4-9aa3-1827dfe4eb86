package com.letu.solutions.model.cms.request.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.validation.constraints.Size;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 提现记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class WithdrawRecordSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不可为空")
    private Long userId;
    /**
     * 协议网络（如 ERC20, TRC20）
     */
    @NotEmpty(message = "协议网络（如 ERC20, TRC20）不可为空")
    private String network;
    /**
     * 提现数量
     */
    @NotNull(message = "提现数量不可为空")
    private BigDecimal amount;
    /**
     * 平台付款钱包地址
     */
    private String fromAddress;
    /**
     * 用户收款钱包地址
     */
    private String toAddress;
    /**
     * 链上交易哈希（Txn Hash）
     */
    private String txHash;
    /**
     * 截图凭证，最多支持3张图片URL
     */
    @Size(min = 0, max = 3)
    private List<String> evidenceImages;

}
