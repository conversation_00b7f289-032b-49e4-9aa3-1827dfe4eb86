package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.ActionTypeEnum;
import com.letu.solutions.model.enums.cms.WithdrawStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提现记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("withdraw_record")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID，提现记录ID
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 用户ID
    */
    private Long userId;

    /**
    * 用户名
    */
    private String username;

    /**
    * 提现状态（未处理，未通过，已完成）
    */
    private WithdrawStatusEnum withdrawStatus;

    /**
    * 账户类型（甲方账户，乙方账户）
    */
    private AccountTypeEnum accountType;

    /**
    * 操作类型（用户操作，平台操作）
    */
    private ActionTypeEnum actionType;

    /**
    * 协议网络（如 ERC20, TRC20）
    */
    private String network;

    /**
    * 提现数量
    */
    private BigDecimal amount;

    /**
    * 币种（如 USDT, ETH）
    */
    private String currency;

    /**
    * 手续费
    */
    private BigDecimal fee;

    /**
     * 手续费比例
     */
    private BigDecimal feeRate;
    /**
     * 每笔提现固定手续费
     */
    private BigDecimal fixedFee;

    /**
    * 平台付款钱包地址
    */
    private String fromAddress;

    /**
    * 用户收款钱包地址
    */
    private String toAddress;

    /**
    * 链上交易哈希（Txn Hash）
    */
    private String txHash;

    /**
    * 截图凭证，最多支持3张图片URL
    */
    @TableField(typeHandler = com.letu.solutions.model.hanlder.StringListTypeHandler.class)
    private List<String> evidenceImages;

    /**
    * 处理人员（管理员）
    */
    private String operator;

    /**
    * 备注信息
    */
    private String remark;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 处理完成时间
    */
    private Date finishTime;

    /**
     * 日期字段（格式：yyyyMMdd，如：20250725）
     */
    private Integer day;

    /**
     * 处理完成日期字段（格式：yyyyMMdd，如：20250725）
     */
    private Integer finishDay;
}
