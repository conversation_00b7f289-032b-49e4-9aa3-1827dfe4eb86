package com.letu.solutions.model.enums.cms;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.dubbo.remoting.http12.rest.Schema;

/**
 * 账户类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    /**
     *  甲方账户
     */
    client("甲方账户"),
    /**
     * 乙方账户
     */
    provider("乙方账户"),

    /**
     *  @hidden
     */
    platform("平台账户");
    ;

    private String desc;

}
