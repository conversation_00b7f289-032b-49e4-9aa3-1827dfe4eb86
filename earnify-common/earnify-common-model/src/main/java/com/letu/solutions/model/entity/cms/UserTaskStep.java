package com.letu.solutions.model.entity.cms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import com.letu.solutions.model.enums.cms.StepTypeEnum;

/**
 * 用户-任务-步骤表
 * 对应表：user_task_step
 */
@Data
@TableName("user_task_step")
public class UserTaskStep implements Serializable {
    /**
     * 产品自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 任务描述
     */
    private String taskDescribe;
    /**
     * 任务文字操作（用户填写的文字内容）
     */
    private String taskTextOperate;
    /**
     * 任务图片操作（用户上传的图片URL）
     */
    private String taskImageOperate;
    /**
     * 用户任务ID
     */
    private Long userTaskId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 排序
     */
    private Integer srot;
    /**
     * 是否保存，0-未保存，1-已保存
     */
    private Boolean isSaved;

    /**
     * 步骤类型：支持多选，用逗号分隔，如 "text,image" 表示文字+图片
     * 可选值：text-文本，image-图片
     */
    private String stepType;
} 