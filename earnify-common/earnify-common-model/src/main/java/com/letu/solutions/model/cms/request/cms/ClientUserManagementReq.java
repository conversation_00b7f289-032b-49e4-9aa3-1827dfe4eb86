package com.letu.solutions.model.cms.request.cms;

import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 甲方用户管理查询请求
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientUserManagementReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;
}
