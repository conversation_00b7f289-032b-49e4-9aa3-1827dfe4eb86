package com.letu.solutions.model.cms.request.sys;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 系统角色修改入参
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "$角色id不可为空")
    private Long id;

    /**
     * 角色名称
     */
    @NotEmpty(message = "$角色名称不可为空")
    private String name;

    /**
     * 角色描述
     */
    private String roleDesc;

    /**
     * 状态[1:启用;0:禁用]
     */
    @NotNull(message = "$角色状态不可为空")
    private Integer enable;


}
