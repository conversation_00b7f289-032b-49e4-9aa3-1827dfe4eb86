package com.letu.solutions.model.cms.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 启用禁用实体
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CmsEnableReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @NotNull(message = "$id不可为空")
    private Long id;

    /**
     * 状态[1:启用;0:禁用]
     */
    @NotNull(message = "$状态不可为空")
    @Range(min = 0, max = 1, message = "$状态值不正确")
    private Integer enable;

}
