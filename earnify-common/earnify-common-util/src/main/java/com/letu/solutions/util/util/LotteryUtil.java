package com.letu.solutions.util.util;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */

public class LotteryUtil {

    /**
     * 抽奖算法
     *
     * @param prizeList 奖品列表(去除无库存的奖品)
     * @return 奖品信息
     */
    public static Prize lottery(List<Prize> prizeList) {
        List<BigDecimal> probabilityList = prizeList.stream().map(Prize::getProbability).collect(Collectors.toList());
        // 计算总概率，这样可以保证不一定总概率是1
        BigDecimal sumProbability = BigDecimal.ZERO;
        for (BigDecimal probability : probabilityList) {
            sumProbability = NumberUtil.add(sumProbability, probability);
        }
        // 重新计算每个物品在总概率的基础下的概率情况
        List<BigDecimal> actualProbabilityList = new ArrayList<>();
        BigDecimal tempSumProbability = BigDecimal.ZERO;
        for (BigDecimal probability : probabilityList) {
            tempSumProbability = NumberUtil.add(tempSumProbability, NumberUtil.div(probability, sumProbability));

            actualProbabilityList.add(tempSumProbability);
        }
        // 根据区块值来获取抽取到的物品索引
        BigDecimal nextNumbers = BigDecimal.valueOf(Math.random());

        actualProbabilityList.add(nextNumbers);
        Collections.sort(actualProbabilityList);
        return prizeList.get(actualProbabilityList.indexOf(nextNumbers));
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Prize {
        /**
         * 奖品ID
         */
        private Long id;
        /**
         * 概率
         */
        private BigDecimal probability;
    }

    public static void main(String[] args) {
        Prize prize1 = Prize.builder().id(1L).probability(BigDecimal.valueOf(0.1)).build();
        Prize prize2 = Prize.builder().id(2L).probability(BigDecimal.valueOf(0.2)).build();
        Prize prize3 = Prize.builder().id(3L).probability(BigDecimal.valueOf(0.3)).build();
        Prize prize4 = Prize.builder().id(4L).probability(BigDecimal.valueOf(0.4)).build();
        Prize prize5 = Prize.builder().id(5L).probability(BigDecimal.valueOf(0.5)).build();

        List<Prize> prizeList = new ArrayList<>();
        for (int i = 0; i < 100000; i++) {
            prizeList.add(lottery(ListUtil.toList(prize1, prize2, prize3, prize4, prize5)));
        }
        System.out.println(prizeList.stream().collect(Collectors.groupingBy(Prize::getId, Collectors.counting())));
    }
}
