package com.letu.solutions.util.util.captcha;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/8
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "captcha")
public class CaptchaConfig {

    /**
     * 是否开启验证码
     */
    private Boolean verifyCaptcha = true;

    /**
     * 白名单用户
     */
    private List<Long> whiteUserIds = new ArrayList<>();
}
