package com.letu.solutions.util.util.captcha;

import com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaRequest;
import com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaResponse;
import com.aliyun.tea.TeaException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/5/7
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CaptchaUtil {
    @Resource
    private CaptchaConfiguration captchaConfiguration;

    /**
     * 验证验证码
     *
     * @param captchaVerifyParam 请求参数
     * @return 验证结果
     */
    public boolean checkPass(String captchaVerifyParam) {
        // 创建APi请求
        VerifyIntelligentCaptchaRequest request = new VerifyIntelligentCaptchaRequest();
        // 本次验证的场景ID，建议传入，防止前端被篡改场景
        request.sceneId = "1pb062pa";
        // 前端传来的验证参数 CaptchaVerifyParam
        request.captchaVerifyParam = captchaVerifyParam;
        // ====================== 发起请求 ======================
        boolean captchaVerifyResult = false;
        try {
            VerifyIntelligentCaptchaResponse resp = captchaConfiguration.client().verifyIntelligentCaptcha(request);
            // 建议使用您系统中的日志组件，打印返回
            // 获取验证码验证结果（请注意判空），将结果返回给前端。出现异常建议认为验证通过，优先保证业务可用，然后尽快排查异常原因。
            captchaVerifyResult = resp.body.result.verifyResult;
            // 原因code
            String captchaVerifyCode = resp.body.result.verifyCode;
            log.info("客户端参数：" + captchaVerifyParam);
            log.info("验证结果：" + captchaVerifyResult + "，原因code：" + captchaVerifyCode);
        } catch (TeaException error) {
            // 建议使用您系统中的日志组件，打印异常
            // 出现异常建议认为验证通过，优先保证业务可用，然后尽快排查异常原因。
            log.error("验证异常", error);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 建议使用您系统中的日志组件，打印异常
            // 出现异常建议认为验证通过，优先保证业务可用，然后尽快排查异常原因。
            log.error("验证异常", error);
        }
        return captchaVerifyResult;
    }
}
