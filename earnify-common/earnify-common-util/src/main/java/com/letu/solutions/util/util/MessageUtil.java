package com.letu.solutions.util.util;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.letu.solutions.core.configuration.RocketMqConfig;
import com.letu.solutions.core.utils.SleuthDto;
import com.letu.solutions.share.model.enums.MessageEnum;
import com.letu.solutions.share.model.enums.mq.MqEnum;
import com.letu.solutions.share.model.model.NoticeModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.message.Message;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * 通知工具
 */
@Component
@Slf4j
public class MessageUtil {
    @Resource
    protected Producer producer;
    @Resource
    protected TraceUtil traceUtil;
    @Resource
    private RocketMqConfig rocketMqConfig;

    /**
     * 通知发送
     *
     * @param messageEnum
     * @param params
     */
    public void send(MessageEnum messageEnum, Long messageId, Object... params) {
        try {
            log.info("发送通知相关入参,messageEnum:{},messageId:{},params:{}", messageEnum, messageId, params);
            NoticeModel noticeModel = NoticeModel.builder()
                    .historyId(messageId)
                    .createTime(DateUtil.now())
                    .build();
            MqEnum mqEnum = MqEnum.MESSAGE_NOTICE;
            Message message = ClientServiceProvider.loadService().newMessageBuilder()
                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
                    .setTag(mqEnum.getTag())
                    .setBody(JSON.toJSONString(noticeModel, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
                    .build();
            SendReceipt send = producer.send(message);
            log.info("通知发送消息id:{}", send.getMessageId());
        } catch (Exception e) {
            log.error("通知发送失败:messageEnum:{},params:{}", messageEnum, params, e);
        }

    }
//
//    /**
//     * 通知发送
//     *
//     * @param messageEnum
//     * @param params
//     */
//    public void send(MessageEnum messageEnum, ExtendData extendData, Object... params) {
//        try {
//            HeaderDto headerDto = extendData.getHeaderDto();
//            log.info("发送通知相关入参,messageEnum:{},params:{}", messageEnum, params);
//            NoticeModel noticeModel = NoticeModel.builder()
//                    .code(messageEnum.toString())
//                    .param(setParam(messageEnum, params))
//                    .createTime(DateUtil.now())
//                    .build();
//            MqEnum mqEnum = MqEnum.MESSAGE_NOTICE;
//            Message message = ClientServiceProvider.loadService().newMessageBuilder()
//                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
//                    .setTag(mqEnum.getTag())
//                    .setBody(JSON.toJSONString(noticeModel, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
//                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
//                    .build();
//            SendReceipt send = producer.send(message);
//            log.info("通知发送消息id:{}", send.getMessageId());
//        } catch (Exception e) {
//            log.error("通知发送失败:messageEnum:{},params:{}", messageEnum, params, e);
//        }
//
//    }
//
//    /**
//     * 通知发送
//     *
//     * @param messageEnum
//     * @param params
//     */
//    public void sendDelay(MessageEnum messageEnum, Long afterTime, Object... params) {
//        try {
//            log.info("发送通知相关入参,messageEnum:{},params:{}", messageEnum, params);
//            NoticeModel noticeModel = NoticeModel.builder()
//                    .code(messageEnum.toString())
//                    .param(setParam(messageEnum, params))
//                    .createTime(DateUtil.now())
//                    .build();
//            MqEnum mqEnum = MqEnum.MESSAGE_NOTICE_DELAY;
//            Message message = ClientServiceProvider.loadService().newMessageBuilder()
//                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
//                    .setTag(mqEnum.getTag())
//                    .setBody(JSON.toJSONString(noticeModel, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
//                    .setDeliveryTimestamp(System.currentTimeMillis() + afterTime)
//                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
//                    .build();
//            SendReceipt send = producer.send(message);
//            log.info("通知发送消息id:{}", send.getMessageId());
//        } catch (Exception e) {
//            log.error("通知发送失败:messageEnum:{},params:{}", messageEnum, params, e);
//        }
//
//    }
//
//    /**
//     * 通知发送
//     *
//     * @param messageEnum
//     * @param params
//     */
//    public void sendDelay(MessageEnum messageEnum, ExtendData extendData, Long afterTime, Object... params) {
//        try {
//            HeaderDto headerDto = extendData.getHeaderDto();
//            log.info("发送通知相关入参,messageEnum:{},params:{}", messageEnum, params);
//            NoticeModel noticeModel = NoticeModel.builder()
//                    .code(messageEnum.toString())
//                    .param(setParam(messageEnum, params))
//                    .createTime(DateUtil.now())
//                    .build();
//            MqEnum mqEnum = MqEnum.MESSAGE_NOTICE_DELAY;
//            Message message = ClientServiceProvider.loadService().newMessageBuilder()
//                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
//                    .setTag(mqEnum.getTag())
//                    .setBody(JSON.toJSONString(noticeModel, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
//                    .setDeliveryTimestamp(System.currentTimeMillis() + afterTime)
//                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
//                    .build();
//            SendReceipt send = producer.send(message);
//            log.info("通知发送消息id:{}", send.getMessageId());
//        } catch (Exception e) {
//            log.error("通知发送失败:messageEnum:{},params:{}", messageEnum, params, e);
//        }
//
//    }
//
//    /**
//     * 通知发送
//     *
//     * @param messageEnum
//     * @param params
//     */
//    public void sendBatch(MessageEnum messageEnum, Object... params) {
//        try {
//            log.info("发送通知相关入参,messageEnum:{},params:{}", messageEnum, params);
//            NoticeModel noticeModel = NoticeModel.builder()
//                    .code(messageEnum.toString())
//                    .param(setParam(messageEnum, params))
//                    .createTime(DateUtil.now())
//                    .batch(true)
//                    .build();
//            MqEnum mqEnum = MqEnum.MESSAGE_NOTICE;
//            Message message = ClientServiceProvider.loadService().newMessageBuilder()
//                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
//                    .setTag(mqEnum.getTag())
//                    .setBody(JSON.toJSONString(noticeModel, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
//                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
//                    .build();
//            producer.send(message);
//        } catch (Exception e) {
//            log.error("通知发送失败:messageEnum:{},params:{}", messageEnum, params);
//        }
//
//    }

}
