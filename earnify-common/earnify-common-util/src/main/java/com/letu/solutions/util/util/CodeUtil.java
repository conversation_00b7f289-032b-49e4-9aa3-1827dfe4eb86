package com.letu.solutions.util.util;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.letu.solutions.core.exception.ThrowException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 邀请码工具
 *
 * <AUTHOR>
 */
@Slf4j
public class CodeUtil {
    /**
     * 获取邀请码
     *
     * @param userId 用户ID
     * @return 邀请码
     */
    public static String inviteCode(Long userId) {
        String s = StrUtil.padPre(userId.toString(), 9, "0");
        char[] chars = s.toCharArray();
        StringBuilder stringBuffer = new StringBuilder();
        for (int index : new int[]{8, 6, 7, 0, 3, 5, 1, 4, 2}) {
            stringBuffer.append(NumEnum.loadEnum(chars[index]));
        }
        long i = Long.parseLong(stringBuffer.toString());
        long num = i / 11881376;
        long strNum = i % 11881376;
        String str = tranString((int) strNum);
        StringBuffer resSb = new StringBuffer();
        return resSb.append(StrUtil.padPre("" + num, 2, "0")).append(str).toString();
    }

    /**
     * 逆向通过邀请码获取用户ID
     *
     * @param inviteCode 邀请码
     * @return 用户ID
     */
    public static Long inviteCodeToUserId(String inviteCode) {
        try {
            long num = Long.parseLong(inviteCode.substring(0, 2)); // 解析strNum部分
            long strNum = tranInt(inviteCode.substring(2)).longValue(); // 解析num部分
            // 根据数学运算还原原始的整数
            long originalUserId = num * 11881376 + strNum; // 原始数值
            StringBuilder stringBuffer = new StringBuilder();
            char[] chars = StrUtil.padPre(Long.valueOf(originalUserId).toString(), 9, "0").toCharArray();
            for (int index : new int[]{3, 6, 8, 4, 7, 5, 1, 2, 0}) {
                stringBuffer.append(NumEnum.decodeEnum(chars[index]));
            }
            return Long.parseLong(stringBuffer.toString());
        } catch (Exception e) {
            throw new ThrowException("#邀请码不合法");
        }
    }

    /**
     * 获取藏品编号前缀
     *
     * @param id 藏品ID
     * @return 编号
     */
    public static String genPrefix(Long id) {
        String s = StrUtil.padPre(id.toString(), 10, "0");
        char[] chars = s.toCharArray();
        StringBuilder stringBuffer = new StringBuilder();
        for (int index : new int[]{8, 6, 7, 0, 3, 5, 9, 1, 4, 2}) {
            stringBuffer.append(NumEnum.loadEnum(chars[index]));
        }
        long i = Long.parseLong(stringBuffer.toString());
        int num = (int) (i / 11881376);
        int strNum = (int) (i % 11881376);
        String str = tranCapitalString(strNum, 2);
        StringBuffer resSb = new StringBuffer();
        return resSb.append(str).append(StrUtil.padPre("" + num, 4, "0")).toString();
    }

    /**
     * 编号乱码
     *
     * @param id 编号
     * @return 乱码后的编号
     */
    public static String garbledCode(Long id) {
        String s = StrUtil.padPre(id.toString(), 7, "0");
        char[] chars = s.toCharArray();
        StringBuilder stringBuffer = new StringBuilder();
        for (int index : new int[]{6, 0, 3, 5, 1, 4, 2}) {
            stringBuffer.append(NumEnum.loadEnum(chars[index]));
        }
        return stringBuffer.toString();
    }

    public static String tranCapitalString(int n, int length) {
        String s = "";
        for (int i = 0; i < length; i++) {
            int m = n % 26;
            s = (char) (m + 'A') + s;
            n = (n - m) / 26;
            if (n < 0) {
                n = 0;
            }
        }
        return s;
    }

    public static String tranString(int n) {
        String s = "";
        for (int i = 0; i < 5; i++) {
            int m = n % 26;
            s = (char) (m + 'a') + s;
            n = (n - m) / 26;
            if (n < 0) {
                n = 0;
            }
        }
        return s;
    }

    public static Integer tranInt(String s) {
        int n = 0;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            int m = c - 'a';
            n = n * 26 + m;
        }
        return n;
    }


    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    enum NumEnum {
        num0(9),
        num1(7),
        num2(8),
        num3(4),
        num4(2),
        num5(3),
        num6(5),
        num7(1),
        num8(0),
        num9(6),
        ;
        int value;

        static Integer loadEnum(char c) {
            return NumEnum.valueOf("num" + c).getValue();
        }

        static Integer decodeEnum(char numChar) {
            Integer num = Integer.parseInt(String.valueOf(numChar));
            NumEnum numEnum = EnumUtil.getBy(NumEnum::getValue, num);
            return Integer.parseInt(numEnum.name().substring(3));
        }
    }
}
