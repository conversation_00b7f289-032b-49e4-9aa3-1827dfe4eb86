package com.letu.solutions.util.util;

import com.letu.solutions.core.exception.ThrowException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 主动调用Spring valid相关工具
 */
public class ValidUtil {

    /**
     * 只返回第一个异常信息
     *
     * @param obj
     * @return null 表示无异常
     */
    public static String sValid(Object obj) {
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        Validator validator = vf.getValidator();
        Set<ConstraintViolation<Object>> set = validator.validate(obj);
        for (ConstraintViolation<Object> constraintViolation : set) {
            return constraintViolation.getMessage();
        }
        return null;
    }

    /**
     * 只返回第一个异常信息
     *
     * @param obj
     * @return null 表示无异常
     */
    public static String validException(Object obj) {
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        Validator validator = vf.getValidator();
        Set<ConstraintViolation<Object>> set = validator.validate(obj);
        for (ConstraintViolation<Object> constraintViolation : set) {
            throw new ThrowException(constraintViolation.getMessage());
        }
        return null;
    }

    /**
     * 只返回第一个异常信息
     *
     * @param obj
     * @return null 表示无异常
     */
    public static String validException(Object obj, Class<?>... groups) {
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        Validator validator = vf.getValidator();
        Set<ConstraintViolation<Object>> set = validator.validate(obj, groups);
        for (ConstraintViolation<Object> constraintViolation : set) {
            throw new ThrowException(constraintViolation.getMessage());
        }
        return null;
    }

    /**
     * 返回所有异常信息
     *
     * @param obj
     * @return null 表示无异常
     */
    public static List<String> sListValid(Object obj) {
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        Validator validator = vf.getValidator();
        Set<ConstraintViolation<Object>> set = validator.validate(obj);
        List<String> res = new ArrayList<String>();
        for (ConstraintViolation<Object> constraintViolation : set) {
            res.add(constraintViolation.getMessage());
        }
        return res.size() > 0 ? res : null;
    }
}
