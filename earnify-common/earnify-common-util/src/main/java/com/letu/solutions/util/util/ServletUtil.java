package com.letu.solutions.util.util;

import com.google.common.collect.Maps;
import jakarta.servlet.http.HttpServletRequest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

/**
 * @description
 * <AUTHOR>
 * @createTime 2025/5/28 19:37
 */
public class ServletUtil {

    public static String getBody(HttpServletRequest request) {
        try {
            // 读取请求体
            StringBuilder builder = new StringBuilder();
            String line;
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()))) {
                while ((line = reader.readLine()) != null) {
                    builder.append(line);
                }
            }
            return builder.toString();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Map<String, String> getParamsMap(HttpServletRequest request) {
        Map<String, String> paramMap = Maps.newHashMap();

        Map<String, String[]> parameterMap = request.getParameterMap();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            if (entry.getValue() != null && entry.getValue().length > 0) {
                paramMap.put(entry.getKey(), entry.getValue()[0]);
            }
        }
        return paramMap;
    }
}
