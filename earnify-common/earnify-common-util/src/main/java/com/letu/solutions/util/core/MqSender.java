package com.letu.solutions.util.core;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.letu.solutions.core.configuration.RocketMqConfig;
import com.letu.solutions.core.enums.mq.MqEnum;
import com.letu.solutions.model.dto.SleuthDto;
import com.letu.solutions.util.util.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.message.Message;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 * mq消息发送唯一入口
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MqSender {
    protected final Producer producer;
    protected final TraceUtil traceUtil;
    protected final RocketMqConfig rocketMqConfig;

//    /**
//     * 埋点公用mq生产者
//     *
//     * @param buryModel
//     */
//    public boolean record(BuryModel buryModel, RecordMqEnum recordMqEnum) {
//        try {
//            log.info("埋点消息生产,recordMqEnum:{},message:{}", recordMqEnum, JSON.toJSONString(buryModel));
//            Message message = ClientServiceProvider.loadService().newMessageBuilder()
//                    .setTopic(rocketMqConfig.getTopicKey() + recordMqEnum.getTopic())
//                    .setTag(recordMqEnum.getTag())
//                    .setBody(JSON.toJSONString(buryModel, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
//                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
//                    .build();
//            // 异步发送，不处理结果
//            SendReceipt send = producer.send(message);
//            log.info("埋点消息生产结果,messageId:{},recordMqEnum:{},message:{}", send.getMessageId(), recordMqEnum, JSON.toJSONString(buryModel));
//            return true;
//        } catch (Exception e) {
//            log.error("mq统计消息发送失败,enum:{}", recordMqEnum, e);
//            return false;
//        }
//    }

    /**
     * 业务mq发送者
     *
     * @param object
     */
    public boolean send(Object object, MqEnum mqEnum) {
        try {
            String business = mqEnum.getMessage();
            log.info("业务mq发送者生产, business： {}, message:{}", business, JSON.toJSONString(object));
            Message message = ClientServiceProvider.loadService().newMessageBuilder()
                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
                    .setTag(mqEnum.getTag())
                    .setBody(JSON.toJSONString(object, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
                    .build();
            SendReceipt sendResult = producer.send(message);
            log.info("发送结果, business： {} topic:{},tag:{},messageId:{}", business,
                    mqEnum.getTopic(), mqEnum.getTag(), sendResult.getMessageId());
            return true;
        } catch (Exception e) {
            log.error("业务mq发送消息失败,enum:{}", mqEnum, e);
            return false;
        }
    }


    /**
     * 业务mq发送者
     *
     * @param object
     */
    @Async("asyncPool")
    public void asyncSend(Object object, MqEnum mqEnum) {
        try {
            String business = mqEnum.getMessage();
            log.info("业务mq发送者生产, business： {}, message:{}", business, JSON.toJSONString(object));
            Message message = ClientServiceProvider.loadService().newMessageBuilder()
                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
                    .setTag(mqEnum.getTag())
                    .setBody(JSON.toJSONString(object, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
                    .build();
            SendReceipt sendResult = producer.send(message);
            log.info("发送结果, business： {} topic:{},tag:{},messageId:{}", business,
                    mqEnum.getTopic(), mqEnum.getTag(), sendResult.getMessageId());
        } catch (Exception e) {
            log.error("业务mq发送消息失败,enum:{}", mqEnum, e);
        }
    }

    /**
     * afterTime 豪秒，发送后多少豪秒后消费
     *
     * @param object
     * @param mqEnum
     * @return
     */
    public boolean sendDelay(Object object, MqEnum mqEnum, Long afterTime) {
        String business = mqEnum.getMessage();
        log.info("业务mq发送者生产, business：{},message:{}", business, JSON.toJSONString(object));
        try {
            // 延时消息需要测试
            Message message = ClientServiceProvider.loadService().newMessageBuilder()
                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
                    .setTag(mqEnum.getTag())
                    .setBody(JSON.toJSONString(object, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
                    .setDeliveryTimestamp(System.currentTimeMillis() + afterTime)
                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
                    .build();
            SendReceipt sendResult = producer.send(message);
            log.info("发送结果, business：{},topic:{},tag:{},messageId:{}", business,
                    mqEnum.getTopic(), mqEnum.getTag(), sendResult.getMessageId());
            return true;
        } catch (Exception e) {
            log.error("发送结果失败{},enum:{}", e, mqEnum, e);
            return false;
        }
    }

    /**
     * 发送延迟消息，时间为发送消息的时间
     *
     * @param timestamp 延迟消息发送时间戳
     * @param mqEnum    消费枚举
     */
    public boolean sendDelayOnTimestamp(Object object, MqEnum mqEnum, Long timestamp) {
        String business = mqEnum.getMessage();
        log.info("业务mq发送者生产, business：{},message:{}", business, JSON.toJSONString(object));
        try {
            // 延时消息需要测试
            Message message = ClientServiceProvider.loadService().newMessageBuilder()
                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
                    .setTag(mqEnum.getTag())
                    .setBody(JSON.toJSONString(object, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
                    .setDeliveryTimestamp(timestamp)
                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
                    .build();
            SendReceipt sendResult = producer.send(message);
            log.info("发送结果, business：{},topic:{},tag:{},messageId:{}", business,
                    mqEnum.getTopic(), mqEnum.getTag(), sendResult.getMessageId());
            return true;
        } catch (Exception e) {
            log.error("发送结果失败{},enum:{}", e, mqEnum, e);
            return false;
        }
    }

    /**
     * afterTime 豪秒，发送后多少豪秒后消费
     *
     * @param object
     * @param mqEnum
     * @return
     */
    @Async("asyncPool")
    public void asyncSendDelay(Object object, MqEnum mqEnum, Long afterTime) {
        String business = mqEnum.getMessage();
        log.info("业务mq发送者生产, business：{},message:{}", business, JSON.toJSONString(object));
        try {
            // 延时消息需要测试
            Message message = ClientServiceProvider.loadService().newMessageBuilder()
                    .setTopic(rocketMqConfig.getTopicKey() + mqEnum.getTopic())
                    .setTag(mqEnum.getTag())
                    .setBody(JSON.toJSONString(object, JSONWriter.Feature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8))
                    .setDeliveryTimestamp(System.currentTimeMillis() + afterTime)
                    .addProperty(SleuthDto.MQ_KEY, traceUtil.sleuth())
                    .build();
            SendReceipt sendResult = producer.send(message);
            log.info("发送结果, business：{},topic:{},tag:{},messageId:{}", business,
                    mqEnum.getTopic(), mqEnum.getTag(), sendResult.getMessageId());
//            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("发送结果失败{},enum:{}", e, mqEnum, e);
//            return Boolean.FALSE;
        }
    }
}
