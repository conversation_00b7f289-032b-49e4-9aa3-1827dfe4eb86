package com.letu.solutions.util.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * beanList转换器
 *
 * <AUTHOR>
 * @date 2020/9/9 11:56
 */
@Slf4j
public class ListBeanConvertUtil {

    /**
     * 默认批量查询大小
     */
    private static long DEFAULT_BATCH_PAGE_SIZE = 1000L;

    private static <T, V extends Object> T convert(V v, Class<T> classT) {
        T t = null;
        try {
            t = classT.newInstance();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        BeanUtils.copyProperties(v, t);
        return t;
    }

    public static <T, V extends Object> List<T> convert(List<V> sourceList, Class<T> targetClass) {
        return sourceList.stream().map(e ->
                convert(e, targetClass)
        ).collect(Collectors.toList());
    }

    public static <T> List<T> convertPage2List(Page<T> pageData) {
        return convertPage2List(pageData, DEFAULT_BATCH_PAGE_SIZE);
    }

    public static <T> List<T> convertPage2List(Page<T> pageData, Long batchPageSize) {
        StopWatch stopWatch = new StopWatch("分批查询数据");
        Page page = new Page<>(1L, Objects.isNull(batchPageSize) ? DEFAULT_BATCH_PAGE_SIZE : batchPageSize);
        List<T> list = Lists.newArrayList();
        while (true) {
            stopWatch.start(String.format("批量查询数据, 开始页：%s, 查询数量: %s", page.getCurrent(), page.getSize()));
            if (CollectionUtils.isEmpty(pageData.getRecords())) {
                //取不到数据了，那么就返回
                stopWatch.stop();
                break;
            }
            List records = pageData.getRecords();
            list.addAll(records);

            if (pageData.getPages() == pageData.getCurrent()) {
                //总数小于分页大小，那么就返回
                stopWatch.stop();
                break;
            }
            //下一页
            page.setCurrent(page.getCurrent() + 1L);
            stopWatch.stop();
        }
        log.info(stopWatch.prettyPrint());
        return list;
    }
}
