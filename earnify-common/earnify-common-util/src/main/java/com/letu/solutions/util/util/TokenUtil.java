package com.letu.solutions.util.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.letu.solutions.core.enums.BusinessTypeEnum;
import com.letu.solutions.core.enums.OsEnum;
import com.letu.solutions.core.enums.OsTypeEnum;
import com.letu.solutions.core.model.Authentication;
import com.letu.solutions.share.model.enums.ClientEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TokenUtil {
    private final IdUtil idUtil;
    private final RedisTemplate<String, String> stringRedisTemplate;
    private final static String tokenKey = "token:%s:%s:%s:%s:%s";
    private final static String redisKey = "token:%s:%s:%s:%s";
    private final static String tokenIdRule = "%s|%s";

    public String createToken(Long userId, BusinessTypeEnum businessType, Integer osType, Integer serverType, Authentication authentication) {
        return createToken(userId, businessType, osType, serverType, authentication, 7);
    }

    public String createToken(Long userId, BusinessTypeEnum businessType, Integer osType, Integer serverType, Authentication authentication, int dayNum) {
        // 根据唯一规则生成uuid
        String uuid = SecureUtil.md5(String.format(tokenIdRule, idUtil.loadSnowflakeId().toString(), userId.toString())).toLowerCase();
        authentication.setUuid(uuid);
        String thisRedisKey = String.format(redisKey, businessType.name(), osType, serverType, userId);
        // 删除旧所有账户下token
        stringRedisTemplate.delete(thisRedisKey);
        // 生成redisKey
        String key = String.format(tokenKey, businessType.name(), osType, serverType, userId, uuid);
        String token = Base64.encode(key);
        stringRedisTemplate.opsForValue().set(thisRedisKey, JSONObject.toJSONString(authentication), dayNum, TimeUnit.DAYS);
        // 返回token
        return token;
    }

    /**
     * 刷新使用，校验在网关处校验
     *
     * @param token
     * @return
     */
    public Authentication valiteToken(String token) {
        try {
            if (StrUtil.isEmpty(token)) {
                return new Authentication();
            }
            String key = Base64.decodeStr(token);
            String redisKey = loadRedisKey(key);
            String uuid = loadUuid(key);
            if (null == redisKey || null == uuid) {
                return new Authentication();
            }
            String authenticationStr = stringRedisTemplate.opsForValue().get(redisKey);
            if (StrUtil.isEmpty(authenticationStr)) {
                return null;
            }
            Authentication authentication = JSONObject.parseObject(authenticationStr, Authentication.class);
            if (uuid.equals(authentication.getUuid())) {
                return authentication;
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 删除token
     *
     * @param token
     */
    public void removeToken(String token) {
        String redisKey = loadRedisKey(Base64.decodeStr(token));
        if (null != redisKey) {
            stringRedisTemplate.delete(redisKey);
        }
    }

    /**
     * 删除token
     *
     * @param userId
     */
    public void removeUser(Long userId, BusinessTypeEnum businessType) {
        for (OsTypeEnum osType : OsTypeEnum.values()) {
            for (OsEnum osEnum : OsEnum.values()) {
                String thisRedisKey = String.format(redisKey, businessType.name(), osType.getCode(), osEnum.getServerType(), userId);
                stringRedisTemplate.delete(thisRedisKey);
            }
        }
    }

    /**
     * 刷新token
     *
     * @param token
     * @param authentication
     */
    public void refreshToken(String token, Authentication authentication) {
        String key = Base64.decodeStr(token);
        String redisKey = loadRedisKey(key);
        String uuid = loadUuid(key);
        authentication.setUuid(uuid);
        Long expire = stringRedisTemplate.getExpire(redisKey);
        if (null == expire || expire <= 0) {
            return;
        }
        stringRedisTemplate.opsForValue().setIfPresent(redisKey, JSONObject.toJSONString(authentication), expire, TimeUnit.SECONDS);
    }

    /**
     * 刷新token
     *
     * @param token
     * @param authentication
     */
    public String putToken(String token, Authentication authentication, BusinessTypeEnum businessType, Long expire) {
        String key = Base64.decodeStr(token);
        key = key.replaceAll(ClientEnum.middle.getBusinessType().name(), businessType.name());
        String redisKey = loadRedisKey(key);
        stringRedisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(authentication), expire, TimeUnit.SECONDS);
        return Base64.encode(key);
    }

    /**
     * 刷新token
     */
    public void refreshToken(Long userId, BusinessTypeEnum businessType, Authentication authentication) {
        OsEnum[] values = OsEnum.values();
        OsTypeEnum[] osTypeEnums = OsTypeEnum.values();
        for (OsTypeEnum osTypeEnum : osTypeEnums) {
            for (OsEnum value : values) {
                String key = String.format(redisKey, businessType.name(), osTypeEnum.getCode(), value.getServerType(), userId);
                long expire = stringRedisTemplate.getExpire(key);
                if (expire <= 0) {
                    return;
                }
                stringRedisTemplate.opsForValue().setIfPresent(key, JSONObject.toJSONString(authentication), expire, TimeUnit.SECONDS);
            }
        }
    }

    public void removeToken(BusinessTypeEnum businessType, Long userId) {
        OsEnum[] values = OsEnum.values();
        OsTypeEnum[] osTypeEnums = OsTypeEnum.values();
        for (OsTypeEnum osTypeEnum : osTypeEnums) {
            for (OsEnum value : values) {
                stringRedisTemplate.delete(String.format(redisKey, businessType.name(), osTypeEnum.getCode(), value.getServerType(), userId));
            }
        }
    }

    private String loadRedisKey(String key) {
        if (StrUtil.isEmpty(key)) {
            return null;
        }
        return key.substring(0, key.lastIndexOf(":"));
    }

    private String loadUuid(String key) {
        if (StrUtil.isEmpty(key)) {
            return null;
        }
        return key.substring(key.lastIndexOf(":") + 1);
    }

    public static String createRedisKey(BusinessTypeEnum businessType, OsTypeEnum osType, OsEnum os, Long userId) {
        return String.format(redisKey, businessType.name(), osType.getCode(), os.getServerType(), userId);
    }
}
