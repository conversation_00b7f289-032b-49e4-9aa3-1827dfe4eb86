package com.letu.solutions.util.util;

import java.util.Random;

/**
 * <AUTHOR>
 * @version Id: HeatUtils.java, v 0.1 2025/4/27 16:46 lai_kd Exp $$
 */
public class HeatUtils {

    private final static Random rnd = new Random();

    private final static Double hot=1.1;


    public static Long generateHeat() {
        double result=hot*rnd.nextInt(500)+ 1l;
        return Math.round(result);
    }


    public static void main(String[] args) {
        for (int i = 0; i <10 ; i++) {
            System.out.println(generateHeat());
        }
    }
}
