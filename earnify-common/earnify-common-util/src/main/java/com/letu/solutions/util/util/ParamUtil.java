package com.letu.solutions.util.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 参数替换工具
 *
 * <AUTHOR>
 */
public class ParamUtil {

    /**
     * 替换参数
     *
     * @param teContent
     * @param params
     * @return
     */
    public static String replaceParam(String teContent, Map<String, String> params) {
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        //把方法参数放入SPEL上下文中
        if (CollectionUtil.isNotEmpty(params)) {
            for (String key : params.keySet()) {
                context.setVariable(key, params.get(key));
            }
        } else {
            return teContent;
        }
        ParserContext parserContext = new TemplateParserContext();
        return parser.parseExpression(teContent, parserContext).getValue(context, String.class);
    }

    /**
     * 替换参数
     *
     * @param teContent
     * @return
     */
    public static String clearParam(String teContent) {
        List<String> list = loadKey(teContent);
        String content = teContent + "";
        for (String s : list) {
            content = content.replaceAll(s, "");
        }
        return content;
    }

    public static List<String> loadKey(String content) {
        return ReUtil.findAll("\\#\\{\\#([^}]*)\\}", content, 1, new ArrayList<>());
    }
}
