package com.letu.solutions.util.util;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.Optional;

public enum AreaEnum {

    CITY_0(0, "未知", "未知", 1),
    CITY_1101(1101, "北京市", "北京市", 1),
    CITY_1102(1102, "北京市", "北京市", 2),
    CITY_1201(1201, "天津市", "天津市", 1),
    CITY_1202(1202, "天津市", "天津市", 2),
    CITY_1300(1300, "河北省", "河北省", 1),
    CITY_1302(1302, "唐山市", "河北省", 2),
    CITY_1308(1308, "承德市", "河北省", 2),
    CITY_1310(1310, "廊坊市", "河北省", 2),
    CITY_1303(1303, "秦皇岛市", "河北省", 2),
    CITY_1306(1306, "保定市", "河北省", 2),
    CITY_1301(1301, "石家庄市", "河北省", 2),
    CITY_1304(1304, "邯郸市", "河北省", 2),
    CITY_1305(1305, "邢台市", "河北省", 2),
    CITY_1307(1307, "张家口市", "河北省", 2),
    CITY_1309(1309, "沧州市", "河北省", 2),
    CITY_1311(1311, "衡水市", "河北省", 2),
    CITY_1400(1400, "山西省", "山西省", 1),
    CITY_1403(1403, "阳泉市", "山西省", 2),
    CITY_1401(1401, "太原市", "山西省", 2),
    CITY_1410(1410, "临汾市", "山西省", 2),
    CITY_1408(1408, "运城市", "山西省", 2),
    CITY_1404(1404, "长治市", "山西省", 2),
    CITY_1406(1406, "朔州市", "山西省", 2),
    CITY_1405(1405, "晋城市", "山西省", 2),
    CITY_1409(1409, "忻州市", "山西省", 2),
    CITY_1407(1407, "晋中市", "山西省", 2),
    CITY_1411(1411, "吕梁市", "山西省", 2),
    CITY_1402(1402, "大同市", "山西省", 2),
    CITY_1500(1500, "内蒙古自治区", "内蒙古自治区", 1),
    CITY_1503(1503, "乌海市", "内蒙古自治区", 2),
    CITY_1502(1502, "包头市", "内蒙古自治区", 2),
    CITY_1508(1508, "巴彦淖尔市", "内蒙古自治区", 2),
    CITY_1507(1507, "呼伦贝尔市", "内蒙古自治区", 2),
    CITY_1506(1506, "鄂尔多斯市", "内蒙古自治区", 2),
    CITY_1530(1530, "阿拉善盟", "内蒙古自治区", 2),
    CITY_1504(1504, "赤峰市", "内蒙古自治区", 2),
    CITY_1505(1505, "通辽市", "内蒙古自治区", 2),
    CITY_1522(1522, "兴安盟", "内蒙古自治区", 2),
    CITY_1509(1509, "乌兰察布市", "内蒙古自治区", 2),
    CITY_1525(1525, "锡林郭勒盟", "内蒙古自治区", 2),
    CITY_1529(1529, "锡林郭勒盟", "内蒙古自治区", 2),
    CITY_1501(1501, "呼和浩特市", "内蒙古自治区", 2),
    CITY_2100(2100, "辽宁省", "辽宁省", 1),
    CITY_2114(2114, "葫芦岛市", "辽宁省", 2),
    CITY_2102(2102, "大连市", "辽宁省", 2),
    CITY_2106(2106, "丹东市", "辽宁省", 2),
    CITY_2107(2107, "锦州市", "辽宁省", 2),
    CITY_2104(2104, "抚顺市", "辽宁省", 2),
    CITY_2101(2101, "沈阳市", "辽宁省", 2),
    CITY_2112(2112, "铁岭市", "辽宁省", 2),
    CITY_2108(2108, "营口市", "辽宁省", 2),
    CITY_2113(2113, "朝阳市", "辽宁省", 2),
    CITY_2110(2110, "辽阳市", "辽宁省", 2),
    CITY_2103(2103, "鞍山市", "辽宁省", 2),
    CITY_2109(2109, "阜新市", "辽宁省", 2),
    CITY_2111(2111, "盘锦市", "辽宁省", 2),
    CITY_2105(2105, "本溪市", "辽宁省", 2),
    CITY_2200(2200, "吉林省", "吉林省", 1),
    CITY_2202(2202, "吉林市", "吉林省", 2),
    CITY_2201(2201, "长春市", "吉林省", 2),
    CITY_2207(2207, "松原市", "吉林省", 2),
    CITY_2204(2204, "辽源市", "吉林省", 2),
    CITY_2208(2208, "白城市", "吉林省", 2),
    CITY_2203(2203, "四平市", "吉林省", 2),
    CITY_2224(2224, "延边朝鲜族自治州", "吉林省", 2),
    CITY_2206(2206, "白山市", "吉林省", 2),
    CITY_2205(2205, "通化市", "吉林省", 2),
    CITY_2300(2300, "黑龙江省", "黑龙江省", 1),
    CITY_2327(2327, "大兴安岭地区", "黑龙江省", 2),
    CITY_2309(2309, "七台河市", "黑龙江省", 2),
    CITY_2304(2304, "鹤岗市", "黑龙江省", 2),
    CITY_2307(2307, "伊春市", "黑龙江省", 2),
    CITY_2312(2312, "绥化市", "黑龙江省", 2),
    CITY_2311(2311, "黑河市", "黑龙江省", 2),
    CITY_2301(2301, "哈尔滨市", "黑龙江省", 2),
    CITY_2302(2302, "齐齐哈尔市", "黑龙江省", 2),
    CITY_2310(2310, "牡丹江市", "黑龙江省", 2),
    CITY_2303(2303, "鸡西市", "黑龙江省", 2),
    CITY_2305(2305, "双鸭山市", "黑龙江省", 2),
    CITY_2306(2306, "大庆市", "黑龙江省", 2),
    CITY_2308(2308, "佳木斯市", "黑龙江省", 2),
    CITY_3101(3101, "上海市", "上海市", 1),
    CITY_3102(3102, "上海市", "上海市", 2),
    CITY_3200(3200, "江苏省", "江苏省", 1),
    CITY_3207(3207, "连云港市", "江苏省", 2),
    CITY_3213(3213, "宿迁市", "江苏省", 2),
    CITY_3201(3201, "南京市", "江苏省", 2),
    CITY_3211(3211, "镇江市", "江苏省", 2),
    CITY_3206(3206, "南通市", "江苏省", 2),
    CITY_3208(3208, "淮安市", "江苏省", 2),
    CITY_3203(3203, "徐州市", "江苏省", 2),
    CITY_3209(3209, "盐城市", "江苏省", 2),
    CITY_3210(3210, "扬州市", "江苏省", 2),
    CITY_3212(3212, "泰州市", "江苏省", 2),
    CITY_3202(3202, "无锡市", "江苏省", 2),
    CITY_3204(3204, "常州市", "江苏省", 2),
    CITY_3205(3205, "苏州市", "江苏省", 2),
    CITY_3300(3300, "浙江省", "浙江省", 1),
    CITY_3309(3309, "舟山市", "浙江省", 2),
    CITY_3304(3304, "嘉兴市", "浙江省", 2),
    CITY_3302(3302, "宁波市", "浙江省", 2),
    CITY_3310(3310, "台州市", "浙江省", 2),
    CITY_3303(3303, "温州市", "浙江省", 2),
    CITY_3311(3311, "丽水市", "浙江省", 2),
    CITY_3301(3301, "杭州市", "浙江省", 2),
    CITY_3306(3306, "绍兴市", "浙江省", 2),
    CITY_3305(3305, "湖州市", "浙江省", 2),
    CITY_3307(3307, "金华市", "浙江省", 2),
    CITY_3308(3308, "衢州市", "浙江省", 2),
    CITY_3400(3400, "安徽省", "安徽省", 1),
    CITY_3406(3406, "淮北市", "安徽省", 2),
    CITY_3412(3412, "阜阳市", "安徽省", 2),
    CITY_3405(3405, "马鞍山市", "安徽省", 2),
    CITY_3407(3407, "铜陵市", "安徽省", 2),
    CITY_3417(3417, "池州市", "安徽省", 2),
    CITY_3416(3416, "亳州市", "安徽省", 2),
    CITY_3403(3403, "蚌埠市", "安徽省", 2),
    CITY_3411(3411, "滁州市", "安徽省", 2),
    CITY_3415(3415, "六安市", "安徽省", 2),
    CITY_3408(3408, "安庆市", "安徽省", 2),
    CITY_3410(3410, "黄山市", "安徽省", 2),
    CITY_3418(3418, "宣城市", "安徽省", 2),
    CITY_3404(3404, "淮南市", "安徽省", 2),
    CITY_3401(3401, "合肥市", "安徽省", 2),
    CITY_3413(3413, "宿州市", "安徽省", 2),
    CITY_3402(3402, "芜湖市", "安徽省", 2),
    CITY_3500(3500, "福建省", "福建省", 1),
    CITY_3509(3509, "宁德市", "福建省", 2),
    CITY_3501(3501, "福州市", "福建省", 2),
    CITY_3503(3503, "莆田市", "福建省", 2),
    CITY_3508(3508, "龙岩市", "福建省", 2),
    CITY_3502(3502, "厦门市", "福建省", 2),
    CITY_3505(3505, "泉州市", "福建省", 2),
    CITY_3506(3506, "漳州市", "福建省", 2),
    CITY_3507(3507, "南平市", "福建省", 2),
    CITY_3504(3504, "三明市", "福建省", 2),
    CITY_3600(3600, "江西省", "江西省", 1),
    CITY_3604(3604, "九江市", "江西省", 2),
    CITY_3605(3605, "新余市", "江西省", 2),
    CITY_3610(3610, "抚州市", "江西省", 2),
    CITY_3606(3606, "鹰潭市", "江西省", 2),
    CITY_3607(3607, "赣州市", "江西省", 2),
    CITY_3601(3601, "南昌市", "江西省", 2),
    CITY_3609(3609, "宜春市", "江西省", 2),
    CITY_3608(3608, "吉安市", "江西省", 2),
    CITY_3602(3602, "景德镇市", "江西省", 2),
    CITY_3611(3611, "上饶市", "江西省", 2),
    CITY_3603(3603, "萍乡市", "江西省", 2),
    CITY_3700(3700, "山东省", "山东省", 1),
    CITY_3706(3706, "烟台市", "山东省", 2),
    CITY_3710(3710, "威海市", "山东省", 2),
    CITY_3702(3702, "青岛市", "山东省", 2),
    CITY_3703(3703, "淄博市", "山东省", 2),
    CITY_3715(3715, "聊城市", "山东省", 2),
    CITY_3713(3713, "临沂市", "山东省", 2),
    CITY_3707(3707, "潍坊市", "山东省", 2),
    CITY_3704(3704, "枣庄市", "山东省", 2),
    CITY_3711(3711, "日照市", "山东省", 2),
    CITY_3716(3716, "滨州市", "山东省", 2),
    CITY_3705(3705, "东营市", "山东省", 2),
    CITY_3709(3709, "泰安市", "山东省", 2),
    CITY_3714(3714, "德州市", "山东省", 2),
    CITY_3701(3701, "济南市", "山东省", 2),
    CITY_3708(3708, "济宁市", "山东省", 2),
    CITY_3717(3717, "菏泽市", "山东省", 2),
    CITY_4100(4100, "河南省", "河南省", 1),
    CITY_4103(4103, "洛阳市", "河南省", 2),
    CITY_4112(4112, "三门峡市", "河南省", 2),
    CITY_4111(4111, "漯河市", "河南省", 2),
    CITY_4110(4110, "许昌市", "河南省", 2),
    CITY_4113(4113, "南阳市", "河南省", 2),
    CITY_4115(4115, "信阳市", "河南省", 2),
    CITY_4190(4190, "济源市", "河南省", 2),
    CITY_4117(4117, "驻马店市", "河南省", 2),
    CITY_4109(4109, "濮阳市", "河南省", 2),
    CITY_4108(4108, "焦作市", "河南省", 2),
    CITY_4106(4106, "鹤壁市", "河南省", 2),
    CITY_4107(4107, "新乡市", "河南省", 2),
    CITY_4104(4104, "平顶山市", "河南省", 2),
    CITY_4116(4116, "周口市", "河南省", 2),
    CITY_4114(4114, "商丘市", "河南省", 2),
    CITY_4102(4102, "开封市", "河南省", 2),
    CITY_4101(4101, "郑州市", "河南省", 2),
    CITY_4105(4105, "安阳市", "河南省", 2),
    CITY_4200(4200, "湖北省", "湖北省", 1),
    CITY_4203(4203, "十堰市", "湖北省", 2),
    CITY_4206(4206, "襄阳市", "湖北省", 2),
    CITY_4208(4208, "荆门市", "湖北省", 2),
    CITY_4205(4205, "宜昌市", "湖北省", 2),
    CITY_4201(4201, "武汉市", "湖北省", 2),
    CITY_4211(4211, "黄冈市", "湖北省", 2),
    CITY_429006(429006, "天门市", "湖北省", 2),
    CITY_4209(4209, "孝感市", "湖北省", 2),
    CITY_429005(429005, "潜江市", "湖北省", 2),
    CITY_4228(4228, "恩施土家族苗族自治州", "湖北省", 2),
    CITY_429004(429004, "仙桃市", "湖北省", 2),
    CITY_4210(4210, "荆州市", "湖北省", 2),
    CITY_4212(4212, "咸宁市", "湖北省", 2),
    CITY_429021(429021, "神农架林区", "湖北省", 2),
    CITY_4213(4213, "随州市", "湖北省", 2),
    CITY_4207(4207, "鄂州市", "湖北省", 2),
    CITY_4202(4202, "黄石市", "湖北省", 2),
    CITY_4300(4300, "湖南省", "湖南省", 1),
    CITY_4306(4306, "岳阳市", "湖南省", 2),
    CITY_4304(4304, "衡阳市", "湖南省", 2),
    CITY_4309(4309, "益阳市", "湖南省", 2),
    CITY_4301(4301, "长沙市", "湖南省", 2),
    CITY_4312(4312, "怀化市", "湖南省", 2),
    CITY_4313(4313, "娄底市", "湖南省", 2),
    CITY_4308(4308, "张家界市", "湖南省", 2),
    CITY_4331(4331, "湘西土家族苗族自治州", "湖南省", 2),
    CITY_4307(4307, "常德市", "湖南省", 2),
    CITY_4305(4305, "邵阳市", "湖南省", 2),
    CITY_4303(4303, "湘潭市", "湖南省", 2),
    CITY_4311(4311, "永州市", "湖南省", 2),
    CITY_4310(4310, "郴州市", "湖南省", 2),
    CITY_4302(4302, "株洲市", "湖南省", 2),
    CITY_4400(4400, "广东省", "广东省", 1),
    CITY_4405(4405, "汕头市", "广东省", 2),
    CITY_4406(4406, "佛山市", "广东省", 2),
    CITY_4412(4412, "肇庆市", "广东省", 2),
    CITY_4413(4413, "惠州市", "广东省", 2),
    CITY_4403(4403, "深圳市", "广东省", 2),
    CITY_4404(4404, "珠海市", "广东省", 2),
    CITY_4408(4408, "湛江市", "广东省", 2),
    CITY_4407(4407, "江门市", "广东省", 2),
    CITY_4409(4409, "茂名市", "广东省", 2),
    CITY_4415(4415, "汕尾市", "广东省", 2),
    CITY_4453(4453, "云浮市", "广东省", 2),
    CITY_4451(4451, "潮州市", "广东省", 2),
    CITY_4417(4417, "阳江市", "广东省", 2),
    CITY_4421(4421, "东沙群岛", "广东省", 2),
    CITY_4416(4416, "河源市", "广东省", 2),
    CITY_4414(4414, "梅州市", "广东省", 2),
    CITY_4419(4419, "东莞市", "广东省", 2),
    CITY_4418(4418, "清远市", "广东省", 2),
    CITY_4402(4402, "韶关市", "广东省", 2),
    CITY_4452(4452, "揭阳市", "广东省", 2),
    CITY_4401(4401, "广州市", "广东省", 2),
    CITY_4420(4420, "中山市", "广东省", 2),
    CITY_4500(4500, "广西壮族自治区", "广西壮族自治区", 1),
    CITY_4510(4510, "百色市", "广西壮族自治区", 2),
    CITY_4507(4507, "钦州市", "广西壮族自治区", 2),
    CITY_4505(4505, "北海市", "广西壮族自治区", 2),
    CITY_4503(4503, "桂林市", "广西壮族自治区", 2),
    CITY_4512(4512, "河池市", "广西壮族自治区", 2),
    CITY_4502(4502, "柳州市", "广西壮族自治区", 2),
    CITY_4513(4513, "来宾市", "广西壮族自治区", 2),
    CITY_4501(4501, "南宁市", "广西壮族自治区", 2),
    CITY_4514(4514, "崇左市", "广西壮族自治区", 2),
    CITY_4506(4506, "防城港市", "广西壮族自治区", 2),
    CITY_4511(4511, "贺州市", "广西壮族自治区", 2),
    CITY_4509(4509, "玉林市", "广西壮族自治区", 2),
    CITY_4508(4508, "贵港市", "广西壮族自治区", 2),
    CITY_4504(4504, "梧州市", "广西壮族自治区", 2),
    CITY_4600(4600, "海南省", "海南省", 1),
    CITY_469024(469024, "临高县", "海南省", 2),
    CITY_469021(469021, "定安县", "海南省", 2),
    CITY_469022(469022, "屯昌县", "海南省", 2),
    CITY_469026(469026, "昌江黎族自治县", "海南省", 2),
    CITY_469025(469025, "白沙黎族自治县", "海南省", 2),
    CITY_469002(469002, "琼海市", "海南省", 2),
    CITY_469030(469030, "琼中黎族苗族自治县", "海南省", 2),
    CITY_469007(469007, "东方市", "海南省", 2),
    CITY_469006(469006, "万宁市", "海南省", 2),
    CITY_469001(469001, "五指山市", "海南省", 2),
    CITY_469027(469027, "乐东黎族自治县", "海南省", 2),
    CITY_469029(469029, "保亭黎族苗族自治县", "海南省", 2),
    CITY_469028(469028, "陵水黎族自治县", "海南省", 2),
    CITY_4603(4603, "三沙市", "海南省", 2),
    CITY_469005(469005, "文昌市", "海南省", 2),
    CITY_4604(4604, "儋州市", "海南省", 2),
    CITY_469023(469023, "澄迈县", "海南省", 2),
    CITY_4602(4602, "三亚市", "海南省", 2),
    CITY_4601(4601, "海口市", "海南省", 2),
    CITY_5001(5001, "重庆市", "重庆市", 1),
    CITY_5002(5002, "重庆市", "重庆市", 2),
    CITY_5100(5100, "四川省", "四川省", 1),
    CITY_5108(5108, "广元市", "四川省", 2),
    CITY_5113(5113, "南充市", "四川省", 2),
    CITY_5119(5119, "巴中市", "四川省", 2),
    CITY_5106(5106, "德阳市", "四川省", 2),
    CITY_5107(5107, "绵阳市", "四川省", 2),
    CITY_5101(5101, "成都市", "四川省", 2),
    CITY_5116(5116, "广安市", "四川省", 2),
    CITY_5111(5111, "乐山市", "四川省", 2),
    CITY_5117(5117, "达州市", "四川省", 2),
    CITY_5120(5120, "资阳市", "四川省", 2),
    CITY_5110(5110, "内江市", "四川省", 2),
    CITY_5109(5109, "遂宁市", "四川省", 2),
    CITY_5114(5114, "眉山市", "四川省", 2),
    CITY_5103(5103, "自贡市", "四川省", 2),
    CITY_5105(5105, "泸州市", "四川省", 2),
    CITY_5115(5115, "宜宾市", "四川省", 2),
    CITY_5134(5134, "凉山彝族自治州", "四川省", 2),
    CITY_5104(5104, "攀枝花市", "四川省", 2),
    CITY_5132(5132, "阿坝藏族羌族自治州", "四川省", 2),
    CITY_5118(5118, "雅安市", "四川省", 2),
    CITY_5133(5133, "甘孜藏族自治州", "四川省", 2),
    CITY_5200(5200, "贵州省", "贵州省", 1),
    CITY_5203(5203, "遵义市", "贵州省", 2),
    CITY_5206(5206, "铜仁市", "贵州省", 2),
    CITY_5202(5202, "六盘水市", "贵州省", 2),
    CITY_5226(5226, "黔东南苗族侗族自治州", "贵州省", 2),
    CITY_5227(5227, "黔南布依族苗族自治州", "贵州省", 2),
    CITY_5204(5204, "安顺市", "贵州省", 2),
    CITY_5223(5223, "黔西南布依族苗族自治州", "贵州省", 2),
    CITY_5205(5205, "毕节市", "贵州省", 2),
    CITY_5201(5201, "贵阳市", "贵州省", 2),
    CITY_5300(5300, "云南省", "云南省", 1),
    CITY_5306(5306, "昭通市", "云南省", 2),
    CITY_5303(5303, "曲靖市", "云南省", 2),
    CITY_5325(5325, "红河哈尼族彝族自治州", "云南省", 2),
    CITY_5333(5333, "怒江傈僳族自治州", "云南省", 2),
    CITY_5328(5328, "西双版纳傣族自治州", "云南省", 2),
    CITY_5304(5304, "玉溪市", "云南省", 2),
    CITY_5329(5329, "大理白族自治州", "云南省", 2),
    CITY_5307(5307, "丽江市", "云南省", 2),
    CITY_5334(5334, "迪庆藏族自治州", "云南省", 2),
    CITY_5326(5326, "文山壮族苗族自治州", "云南省", 2),
    CITY_5305(5305, "保山市", "云南省", 2),
    CITY_5308(5308, "普洱市", "云南省", 2),
    CITY_5301(5301, "昆明市", "云南省", 2),
    CITY_5323(5323, "楚雄彝族自治州", "云南省", 2),
    CITY_5309(5309, "临沧市", "云南省", 2),
    CITY_5331(5331, "德宏傣族景颇族自治州", "云南省", 2),
    CITY_5400(5400, "西藏自治区", "西藏自治区", 1),
    CITY_5403(5403, "昌都市", "西藏自治区", 2),
    CITY_5406(5406, "那曲市", "西藏自治区", 2),
    CITY_5401(5401, "拉萨市", "西藏自治区", 2),
    CITY_5402(5402, "日喀则市", "西藏自治区", 2),
    CITY_5405(5405, "山南市", "西藏自治区", 2),
    CITY_5404(5404, "林芝市", "西藏自治区", 2),
    CITY_5425(5425, "阿里地区", "西藏自治区", 2),
    CITY_6100(6100, "陕西省", "陕西省", 1),
    CITY_6110(6110, "商洛市", "陕西省", 2),
    CITY_6101(6101, "西安市", "陕西省", 2),
    CITY_6107(6107, "汉中市", "陕西省", 2),
    CITY_6102(6102, "铜川市", "陕西省", 2),
    CITY_6108(6108, "榆林市", "陕西省", 2),
    CITY_6109(6109, "安康市", "陕西省", 2),
    CITY_6106(6106, "延安市", "陕西省", 2),
    CITY_6103(6103, "宝鸡市", "陕西省", 2),
    CITY_6104(6104, "咸阳市", "陕西省", 2),
    CITY_6105(6105, "渭南市", "陕西省", 2),
    CITY_6200(6200, "甘肃省", "甘肃省", 1),
    CITY_6202(6202, "嘉峪关市", "甘肃省", 2),
    CITY_6209(6209, "酒泉市", "甘肃省", 2),
    CITY_6203(6203, "金昌市", "甘肃省", 2),
    CITY_6201(6201, "兰州市", "甘肃省", 2),
    CITY_6208(6208, "平凉市", "甘肃省", 2),
    CITY_6204(6204, "白银市", "甘肃省", 2),
    CITY_6205(6205, "天水市", "甘肃省", 2),
    CITY_6206(6206, "武威市", "甘肃省", 2),
    CITY_6212(6212, "陇南市", "甘肃省", 2),
    CITY_6230(6230, "甘南藏族自治州", "甘肃省", 2),
    CITY_6229(6229, "临夏回族自治州", "甘肃省", 2),
    CITY_6207(6207, "张掖市", "甘肃省", 2),
    CITY_6210(6210, "庆阳市", "甘肃省", 2),
    CITY_6211(6211, "定西市", "甘肃省", 2),
    CITY_6300(6300, "青海省", "青海省", 1),
    CITY_6302(6302, "海东市", "青海省", 2),
    CITY_6325(6325, "海南藏族自治州", "青海省", 2),
    CITY_6328(6328, "海西蒙古族藏族自治州", "青海省", 2),
    CITY_6327(6327, "玉树藏族自治州", "青海省", 2),
    CITY_6323(6323, "黄南藏族自治州", "青海省", 2),
    CITY_6326(6326, "果洛藏族自治州", "青海省", 2),
    CITY_6301(6301, "西宁市", "青海省", 2),
    CITY_6322(6322, "海北藏族自治州", "青海省", 2),
    CITY_6400(6400, "宁夏回族自治区", "宁夏回族自治区", 1),
    CITY_6404(6404, "固原市", "宁夏回族自治区", 2),
    CITY_6405(6405, "中卫市", "宁夏回族自治区", 2),
    CITY_6401(6401, "银川市", "宁夏回族自治区", 2),
    CITY_6402(6402, "石嘴山市", "宁夏回族自治区", 2),
    CITY_6403(6403, "吴忠市", "宁夏回族自治区", 2),
    CITY_6500(6500, "新疆维吾尔自治区", "新疆维吾尔自治区", 1),
    CITY_659005(659005, "北屯市", "新疆维吾尔自治区", 2),
    CITY_659007(659007, "双河市", "新疆维吾尔自治区", 2),
    CITY_659006(659006, "铁门关市", "新疆维吾尔自治区", 2),
    CITY_659008(659008, "可克达拉市", "新疆维吾尔自治区", 2),
    CITY_6527(6527, "博尔塔拉蒙古自治州", "新疆维吾尔自治区", 2),
    CITY_6542(6542, "塔城地区", "新疆维吾尔自治区", 2),
    CITY_6502(6502, "克拉玛依市", "新疆维吾尔自治区", 2),
    CITY_6532(6532, "和田地区", "新疆维吾尔自治区", 2),
    CITY_659009(659009, "昆玉市", "新疆维吾尔自治区", 2),
    CITY_6543(6543, "阿勒泰地区", "新疆维吾尔自治区", 2),
    CITY_659001(659001, "石河子市", "新疆维吾尔自治区", 2),
    CITY_6523(6523, "昌吉回族自治州", "新疆维吾尔自治区", 2),
    CITY_659004(659004, "五家渠市", "新疆维吾尔自治区", 2),
    CITY_6528(6528, "巴音郭楞蒙古自治州", "新疆维吾尔自治区", 2),
    CITY_6529(6529, "阿克苏地区", "新疆维吾尔自治区", 2),
    CITY_6540(6540, "伊犁哈萨克自治州", "新疆维吾尔自治区", 2),
    CITY_659002(659002, "阿拉尔市", "新疆维吾尔自治区", 2),
    CITY_6501(6501, "乌鲁木齐市", "新疆维吾尔自治区", 2),
    CITY_659003(659003, "图木舒克市", "新疆维吾尔自治区", 2),
    CITY_6531(6531, "喀什地区", "新疆维吾尔自治区", 2),
    CITY_6530(6530, "克孜勒苏柯尔克孜自治州", "新疆维吾尔自治区", 2),
    CITY_6505(6505, "哈密市", "新疆维吾尔自治区", 2),
    CITY_6504(6504, "吐鲁番市", "新疆维吾尔自治区", 2),
    CITY_7101(7101, "台湾省", "台湾省", 1),
    CITY_7100(7100, "台湾省", "台湾省", 1),
    CITY_8100(8100, "香港特别行政区", "香港特别行政区", 1),
    CITY_8200(8200, "澳门特别行政区", "澳门特别行政区", 1);

    private Integer code;
    private String city;
    private String province;
    /**
     * 层级 1省级 2市
     */
    private Integer type;

    AreaEnum(Integer code, String city, String province, Integer type) {
        this.code = code;
        this.city = city;
        this.province = province;
        this.type = type;
    }

    public static String getByIdcard(String idcard) {
        if (idcard != null && idcard.length() >= 15) {
            int code = Integer.parseInt(idcard.substring(0, 4));
            AreaEnum[] roles = AreaEnum.values();
            Optional<AreaEnum> optional = Arrays.stream(roles).filter(item -> item.code == code).findFirst();
            AreaEnum areaEnum = optional.orElse(AreaEnum.CITY_0);
            if (areaEnum.code == 0) {
                int code6 = Integer.parseInt(idcard.substring(0, 6));
                optional = Arrays.stream(roles).filter(item -> item.code == code6).findFirst();
                areaEnum = optional.orElse(AreaEnum.CITY_0);
                return areaEnum.city;
            } else {
                return areaEnum.city;
            }
        }
        return AreaEnum.CITY_0.city;
    }

    public static String getByCity(String city) {
        if (StrUtil.isEmpty(city)) {
            return AreaEnum.CITY_0.city;
        }
        AreaEnum[] roles = AreaEnum.values();
        Optional<AreaEnum> optional = Arrays.stream(roles).filter(item -> item.city.contains(city)).findFirst();
        AreaEnum areaEnum = optional.orElse(AreaEnum.CITY_0);
        if (areaEnum.code == 0) {
            return city;
        }
        return areaEnum.city;
    }

    public static String getByCityNull(String city) {
        if (StrUtil.isEmpty(city)) {
            return null;
        }
        AreaEnum[] roles = AreaEnum.values();
        Optional<AreaEnum> optional = Arrays.stream(roles).filter(item -> item.city.contains(city) && item.type == 2).findFirst();
        AreaEnum areaEnum = optional.orElse(AreaEnum.CITY_0);
        if (areaEnum.code == 0) {
            return null;
        }
        return areaEnum.city;
    }

    public static String getByProvince(String province) {
        if (StrUtil.isEmpty(province)) {
            return AreaEnum.CITY_0.city;
        }
        AreaEnum[] roles = AreaEnum.values();
        Optional<AreaEnum> optional = Arrays.stream(roles).filter(item -> item.city.contains(province)).findFirst();
        AreaEnum areaEnum = optional.orElse(AreaEnum.CITY_0);
        if (areaEnum.code == 0) {
            return province;
        }
        return areaEnum.city;
    }

    public static String getProvinceByCity(String city) {
        if (StrUtil.isEmpty(city)) {
            return AreaEnum.CITY_0.province;
        }
        AreaEnum[] roles = AreaEnum.values();
        Optional<AreaEnum> optional = Arrays.stream(roles).filter(item -> item.city.contains(city)).findFirst();
        AreaEnum areaEnum = optional.orElse(AreaEnum.CITY_0);
        if (areaEnum.code == 0) {
            return AreaEnum.CITY_0.province;
        }
        return areaEnum.province;
    }

    public static void main(String[] args) {
        System.out.println(getByCityNull("四川"));
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}
