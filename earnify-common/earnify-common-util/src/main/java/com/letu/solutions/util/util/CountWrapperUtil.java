package com.letu.solutions.util.util;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import java.util.ArrayList;
import java.util.List;

/**
 * 统计wrapper工具
 *
 * <AUTHOR>
 */
public class CountWrapperUtil {
    public final static String uvTemplate = "%sUv";
    public final static String newTemplate = "%sNew";
    public final static String uvNewTemplate = "%sNewUv";
    public final static String isNewKey = "isNewUser";
    public final static Integer newValue = new Integer(1);

    public static UpdateWrapper setValue(UpdateWrapper updateWrapper, Object record, String valueKey, String[] uniqueKeys, boolean setUv, boolean setNew) {
        for (String uniqueKey : uniqueKeys) {
            updateWrapper.eq(StrUtil.toUnderlineCase(uniqueKey), ReflectUtil.getFieldValue(record, uniqueKey));
        }
//        String[] valueKeys = new String[]{valueKey, valueKey + "Uv"};
        List<String> valueKeys = new ArrayList<>();
        valueKeys.add(valueKey);
        if (setNew) {
            valueKeys.add(String.format(newTemplate, valueKey));
        }
        if (setUv) {
            valueKeys.add(String.format(uvTemplate, valueKey));
        }
        if (setUv && setNew) {
            valueKeys.add(String.format(uvNewTemplate, valueKey));
        }
        for (String key : valueKeys) {
            try {
                Object fieldValue = ReflectUtil.getFieldValue(record, key);
                String underKey = StrUtil.toUnderlineCase(key);
                if (null != fieldValue) {
                    updateWrapper.setSql(String.format("`%s` = `%s` + %s", underKey, underKey, fieldValue));
                }
            } catch (Exception e) {
            }
        }
        return updateWrapper;

    }
}
