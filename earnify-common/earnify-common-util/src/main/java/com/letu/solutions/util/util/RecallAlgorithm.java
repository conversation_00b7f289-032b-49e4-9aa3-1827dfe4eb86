package com.letu.solutions.util.util;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * 推荐召回算法
 * <AUTHOR>
 * @version Id: RecallAlgorithm.java, v 0.1 2025/6/4 20:54 lai_kd Exp $$
 */
public class RecallAlgorithm {

    /**
     * 公式：W=e^(-lambda * deltaT)
     * 计算书籍的时间权重算法
     * @param tUpload 上传时间
     * @param tNow 当前时间
     * @return
     */
    public static double calculateTimeWeight(Date tUpload, Date tNow) {
        double lambda = 0.05; // 时间衰减因子
        double e = Math.E; // 自然常数 e ≈ 2.71828
        long oneDayMillis = 1000 * 60 * 60 * 24;
        // 计算时间差（单位：天）
        long timeDiffMillis = tNow.getTime() - tUpload.getTime();
        double deltaT = timeDiffMillis / oneDayMillis;
        // 计算时间权重
        double weight = Math.pow(e, -lambda * deltaT);
        return weight;
    }

    public static void main(String[] args) {
        String uploadTime="2025-06-03 20:16:00";
        System.out.println(calculateTimeWeight(DateUtil.parse(uploadTime), new Date()));
    }
}
