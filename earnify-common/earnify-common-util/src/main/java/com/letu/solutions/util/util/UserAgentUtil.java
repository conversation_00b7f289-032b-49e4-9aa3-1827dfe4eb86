package com.letu.solutions.util.util;

import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;

/**
 * userAgent解析工具
 * 解析为单线程 建议异步操作
 */
public class UserAgentUtil {
    private static final UserAgentAnalyzer USER_AGENT_ANALYZER = UserAgentAnalyzer
            .newBuilder()
            .hideMatcherLoadStats()
            .withCache(10000)
            .withField(UserAgent.DEVICE_NAME)
            .withField(UserAgent.DEVICE_BRAND)
            .withField(UserAgent.OPERATING_SYSTEM_NAME)
            .withField(UserAgent.OPERATING_SYSTEM_VERSION)
            .withField(UserAgent.AGENT_NAME)
            .withField(UserAgent.AGENT_VERSION)
            .build();

    public static UserAgent loadUserAgent(String userAgent) {
        return USER_AGENT_ANALYZER.parse(userAgent);
    }
}
