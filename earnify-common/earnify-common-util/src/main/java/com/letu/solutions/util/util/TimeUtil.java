package com.letu.solutions.util.util;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * 时间格式工具
 */
public class TimeUtil {
    public static Integer today() {
        return today(new Date());
    }

    public static Long todayHour() {
        return todayHour(new Date());
    }

    public static Integer today(Date date) {
        return Integer.parseInt(DateUtil.format(date, "yyyyMMdd"));
    }

    public static Long todayHour(Date date) {
        return Long.parseLong(DateUtil.format(date, "yyyyMMddHH"));
    }

    public static Long now() {
        return formatTime(new Date());
    }

    public static Long formatTime(Date date) {
        return Long.parseLong(DateUtil.format(date, "yyyyMMddHHmmss"));
    }

    public static Date parseTime(String time) {
        return DateUtil.parse(time, "yyyyMMddHHmmss");
    }
}
