package com.letu.solutions.util.util;

import java.math.BigDecimal;

/**
 * 价格转换工具
 */
public class PriceUtil {

    /**
     * 元转分
     *
     * @param price
     * @return
     */
    public static Integer yuanToFen(Object price) {
        if (null == price) {
            return null;
        }
        BigDecimal yuan = new BigDecimal(price.toString());
        Integer fen = yuan.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_FLOOR).intValue();
        return fen;
    }

    /**
     * 分转元
     *
     * @param price
     * @return
     */
    public static String fenToYuan(Object price) {
        if (null == price) {
            return null;
        }
        BigDecimal fen = new BigDecimal(price.toString());
        String yuan = fen.divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
        return yuan;
    }

    /**
     * 分转元
     *
     * @param price
     * @return
     */
    public static String stripTrailingZeros(Object price) {
        if (null == price) {
            return null;
        }
        BigDecimal fen = new BigDecimal(price.toString());
        String yuan = fen.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
        return yuan;
    }

    /**
     * 分折扣
     *
     * @param price
     * @return
     */
    public static String fenDiscount(Integer price, Integer discount) {
        if (null == price || null == discount) {
            return null;
        }
        String discountStr = fenToYuan(discount);
        BigDecimal fen = new BigDecimal(price.toString());
        String resPrice = fen.multiply(new BigDecimal(discountStr)).setScale(0, BigDecimal.ROUND_UP).stripTrailingZeros().toPlainString();
        return resPrice;
    }

    public static String priceNum(Object price, Integer num) {
        if (null == price || null == num) {
            return null;
        }
        BigDecimal fen = new BigDecimal(price.toString());
        String yuan = fen.multiply(new BigDecimal(num)).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
        return yuan;
    }
}
