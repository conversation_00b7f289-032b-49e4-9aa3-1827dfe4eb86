package com.letu.solutions.util.util;

import com.letu.solutions.model.dto.FinanceConfigCacheDTO;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

@Component
public class FinanceConfigCacheUtil {
    private static final String KEY_PREFIX = "finance:config:";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;




    public void setConfig(String network, FinanceConfigCacheDTO dto) {
        redisTemplate.opsForValue().set(KEY_PREFIX + network, dto);
    }

    public FinanceConfigCacheDTO getConfig(String network) {
        Object obj = redisTemplate.opsForValue().get(KEY_PREFIX + network);
        if (obj instanceof FinanceConfigCacheDTO) {
            return (FinanceConfigCacheDTO) obj;
        }
        return null;
    }

    public void deleteConfig(String network) {
        redisTemplate.delete(KEY_PREFIX + network);
    }
} 