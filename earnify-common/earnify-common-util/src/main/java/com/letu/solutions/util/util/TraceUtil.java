package com.letu.solutions.util.util;

import brave.Tracer;
import brave.propagation.TraceContext;
import brave.rpc.RpcTracing;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.letu.solutions.model.dto.SleuthDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * sleuth工具
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TraceUtil {
    private final RpcTracing rpcTracing;
    private final Tracer tracer;
    private final Environment environment;

    /**
     * 获取sleuth id
     * SleuthDto
     */
    public String sleuth() {
        try {
            TraceContext context = rpcTracing.tracing().currentTraceContext().get();
            return JSONObject.toJSONString(new SleuthDto(context));
        } catch (Exception e) {
            log.warn("获取链路id异常", e);
            return SleuthDto.DEFAULT;
        }
    }

    private TraceContext decorateContext(SleuthDto sleuthDto) {
        TraceContext build = TraceContext.newBuilder()
                .traceIdHigh(sleuthDto.getTraceIdHigh())
                .traceId(sleuthDto.getTraceId())
                .parentId(sleuthDto.getParentId())
                .spanId(sleuthDto.getSpanId())
                .clearExtra()
                .build();
        ReflectUtil.setFieldValue(build, "flags", sleuthDto.getFlags());
        return build;
    }

    public void injectContext(MessageView message) {
        try {
            String s = message.getProperties().get(SleuthDto.MQ_KEY);
            if (StrUtil.isEmpty(s)) {
                log.info("未包含链路信息访问,message:{}", message.getMessageId());
                return;
            }
            if (s.equals(SleuthDto.DEFAULT)) {
                return;
            }
            SleuthDto sleuthDto = JSONObject.parseObject(s, SleuthDto.class);
            TraceContext traceContext = decorateContext(sleuthDto);
            tracer.startScopedSpanWithParent(environment.getProperty("spring.application.name"), traceContext);
        } catch (Exception e) {
            log.error("初始化mq链路追踪失败", e);
        }
    }

    public TraceContext loadTraceContext(MessageView message) {
        try {
            String s = message.getProperties().get(SleuthDto.MQ_KEY);
            if (StrUtil.isEmpty(s) || s.equals(SleuthDto.DEFAULT)) {
                log.info("未包含链路信息访问,message:{}", message.getMessageId());
                return null;
            }
            SleuthDto sleuthDto = JSONObject.parseObject(s, SleuthDto.class);
            TraceContext traceContext = decorateContext(sleuthDto);
            tracer.startScopedSpanWithParent(environment.getProperty("spring.application.name"), traceContext);
            return traceContext;
        } catch (Exception e) {
            log.error("初始化mq链路追踪失败", e);
        }
        return null;
    }

    public void injectContext(TraceContext traceContext) {
        try {
            if (ObjectUtil.isNull(traceContext)) {
                return;
            }
            tracer.startScopedSpanWithParent(environment.getProperty("spring.application.name"), traceContext);
        } catch (Exception e) {
            log.error("初始化mq链路追踪失败", e);
        }
    }
}