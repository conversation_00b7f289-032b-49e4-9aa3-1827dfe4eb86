package com.letu.solutions.util.util;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.letu.solutions.core.annotation.CacheRedis;
import com.letu.solutions.core.configuration.AccountConfiguration;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.share.model.enums.SexEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 三要素认证工具
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CertThreeUtil {
    private final AccountConfiguration accountConfiguration;

    @CacheRedis(expireTime = 3600)
    public Pair<Boolean, SexEnum> threeCert(String idCard, String mobile, String name) {
        if (!accountConfiguration.isThreeCertOpen()) {
            return new Pair<>(true, SexEnum.boy);
        }
        if (StrUtil.isEmpty(accountConfiguration.getThreeCertCode())) {
            throw new ThrowException("#未读取到配置秘钥");
        }
        SexEnum sex = SexEnum.boy;
        if (null != accountConfiguration.getWhiteMap() && accountConfiguration.getWhiteMap().containsKey(mobile)) {
            String propertiesUserInfo = accountConfiguration.getWhiteMap().get(mobile);
            String[] split = propertiesUserInfo.split(",");
            if (split.length == 2 && split[0].trim().equals(name) && split[1].trim().equals(idCard)) {
                return new Pair<>(true, sex);
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("idcard", idCard);
        params.put("mobile", mobile);
        params.put("name", name);
        String paramStr = HttpUtil.toParams(params);
        String allUrl = "https://mobile3elements.shumaidata.com/mobile/verify_real_name?" + paramStr;
        HttpRequest post = HttpUtil.createPost(allUrl);
        post.auth(String.format("APPCODE %s", accountConfiguration.getThreeCertCode()));
        post.timeout(5000);
        log.info("三要素请求调用,allUrl:{}", allUrl);
        String body = post.execute().body();
        log.info("三要素请求出参,allUrl:{},body:{}", allUrl, body);
        JSONObject jsonObject = JSONObject.parseObject(body);
        boolean res = jsonObject.getInteger("code") == 0 && jsonObject.getJSONObject("result").getInteger("res") == 1;
        if (!res && StrUtil.isNotEmpty(jsonObject.getString("message"))) {
            if (!"成功".equals(jsonObject.getString("message"))) {
                throw new ThrowException(jsonObject.getString("message"));
            }
        }
        try {
            sex = res ? EnumUtil.getBy(SexEnum::getDesc, jsonObject.getJSONObject("result").getString("sex")) : null;
        } catch (Exception e) {
        }
        return new Pair<>(res, sex);
    }

    public String threeCertValidate(String idCard, String mobile, String name) {
        if (StrUtil.isEmpty(accountConfiguration.getThreeCertCode())) {
            throw new ThrowException("#未读取到配置秘钥");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("idcard", idCard);
        params.put("mobile", mobile);
        params.put("name", name);
        String paramStr = HttpUtil.toParams(params);
        String allUrl = "https://mobile3elements.shumaidata.com/mobile/verify_real_name?" + paramStr;
        HttpRequest post = HttpUtil.createPost(allUrl);
        post.auth(String.format("APPCODE %s", accountConfiguration.getThreeCertCode()));
        post.timeout(5000);
        log.info("三要素请求调用,allUrl:{}", allUrl);
        String body = post.execute().body();
        log.info("三要素请求出参,allUrl:{},body:{}", allUrl, body);
        return body;
    }
}
