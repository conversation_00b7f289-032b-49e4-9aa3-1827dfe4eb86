package com.letu.solutions.util.util;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;

import java.io.*;
import java.nio.charset.Charset;
import java.util.List;

/**
 * <AUTHOR>
 * @version Id: FileConvterUtil.java, v 0.1 2025/4/7 21:53 lai_kd Exp $$
 */
@Slf4j
public class FileConvterUtil {

    /**
     * Convert word file to text file.
     *
     * @param wordFilePath word文件
     * @param textFilePath text文件
     * @throws IOException
     */
    public static void convertWordToText(String wordFilePath, String textFilePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(wordFilePath);
             XWPFDocument document = new XWPFDocument(fis);
             FileWriter fw = new FileWriter(textFilePath)) {

            List<XWPFParagraph> paragraphs = document.getParagraphs();
            for (XWPFParagraph para : paragraphs) {
                fw.write(para.getText());
                fw.write(System.lineSeparator());
            }
        } catch (Exception e) {
            log.error("convert word to text error", e);
        }
    }

    /**
     * 把word文件转换为txt文件
     *
     * @param inputStream
     * @param txtFile
     */
    public static File convertWordToTxt(InputStream inputStream, String txtFile) {
        File file = new File(txtFile);
        try {
            XWPFDocument docx = new XWPFDocument(inputStream);
            List<XWPFParagraph> paragraphs = docx.getParagraphs();
            for (XWPFParagraph para : paragraphs) {
                FileUtil.appendString(para.getText() + "\n", file, Charset.forName("UTF-8"));
            }
            return file;
        } catch (IOException e) {
            log.error("convert word to text error", e);
            return file;
        }
    }
}
