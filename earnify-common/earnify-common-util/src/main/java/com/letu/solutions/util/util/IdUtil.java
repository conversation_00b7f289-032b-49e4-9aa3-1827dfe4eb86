package com.letu.solutions.util.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.letu.solutions.core.constant.CacheConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class IdUtil {
    private final Environment environment;
    private final RedisTemplate<String, String> redisTemplate;
    @Autowired(required = false)
    private Snowflake snowflake;

    @Bean
    public Snowflake snowflake() {
        Long thisId = loadId(IdBusinessEnum.workId);
        long workerId;
        try {
            workerId = (thisId / 31) % 31;
        } catch (Exception e) {
            workerId = 1L;
        }
        long datacenterId;
        try {
            datacenterId = thisId % 31;
        } catch (Exception e) {
            datacenterId = 1;
        }
        return cn.hutool.core.util.IdUtil.getSnowflake(workerId, datacenterId);
    }

    public Long loadSnowflakeId() {
        return snowflake.nextId();
    }

    public String loadBusinessId(String business) {
        String time = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        Long increment = redisTemplate.opsForValue().increment(String.format(CacheConstant.timeIdRedisKey, business, time), 1);
        if (increment == 1) {
            redisTemplate.expire(String.format(CacheConstant.timeIdRedisKey, business, time), 5, TimeUnit.SECONDS);
        }
        return business + time + StrUtil.padPre(increment.toString(), 4, '0');
    }

    public Long loadId(IdBusinessEnum business) {
        String key = String.format(CacheConstant.idRedisKey, business);
        return redisTemplate.opsForValue().increment(key, 1L);
    }

    @Getter
    @AllArgsConstructor
    public static enum IdBusinessEnum {
        workId("Snowflake的workId"),
        datacenterId("Snowflake的datacenterId"),
        ;
        private String desc;
    }
}
