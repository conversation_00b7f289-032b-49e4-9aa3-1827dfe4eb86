package com.letu.solutions.util.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/8
 */

public class PageUtil {

    public static <K, T> Page<K> builderPage(Page<T> page, Class<K> kClass) {
        List<K> voList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            voList = BeanUtil.copyToList(page.getRecords(), kClass);
        }
        Page<K> resultPage = new Page<>();
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setTotal(page.getTotal());
        resultPage.setRecords(voList);
        return resultPage;
    }

    public static <K, T> IPage<K> builderIPage(IPage<T> page, Class<K> kClass) {
        List<K> voList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            voList = BeanUtil.copyToList(page.getRecords(), kClass);
        }
        IPage<K> resultPage = new Page<>();
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setTotal(page.getTotal());
        resultPage.setRecords(voList);
        return resultPage;
    }
    /**
     * 计算页数
     */
    public static long calcTotalPage(long totalRow, long pageSize) {
        long totalPage = 1;

        if (totalRow == 0) {
            totalPage = 1;
        } else if (totalRow % pageSize == 0) {
            totalPage = totalRow / pageSize;
        } else {
            totalPage = totalRow / pageSize + 1;
        }
        return totalPage;
    }
}
