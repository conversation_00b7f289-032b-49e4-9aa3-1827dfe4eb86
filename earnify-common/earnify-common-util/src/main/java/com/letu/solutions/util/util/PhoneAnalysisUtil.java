package com.letu.solutions.util.util;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 手机号解析
 *
 * <AUTHOR>
 * @date 2023年03月28日 9:59
 */
@Slf4j
public class PhoneAnalysisUtil {

    private static final String URL = "https://cx.shouji.360.cn/phonearea.php?number=";

    /**
     * 手机号解析城市
     *
     * @param phone
     */
    public static Map<String, String> analysisCity(String phone) {
        try {
            String body = HttpUtil.createGet(URL + phone).timeout(1000).execute().body();
            log.info("360手机号解析结果 body:{}", body);
            JSONObject result = JSONUtil.parseObj(body);
            JSONObject data = result.getJSONObject("data");
            Map<String, String> map = new HashMap<>(3);
            map.put("province", data.getStr("province"));
            map.put("city", data.getStr("city"));
            map.put("sp", data.getStr("sp"));
            return map;
        } catch (Exception e) {
        }
        return null;
    }
}
