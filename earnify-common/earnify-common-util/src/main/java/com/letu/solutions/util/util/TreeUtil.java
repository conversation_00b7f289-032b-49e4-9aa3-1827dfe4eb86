package com.letu.solutions.util.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * list转树
 *
 * <AUTHOR>
 * @since 2024/1/8
 */

@Slf4j
public class TreeUtil {
    public static <T> List<T> listToTree(List<T> list) {
        if (CollectionUtil.isEmpty(list)) {
            return list;
        }
        List<T> resList = new ArrayList<>();
        Map<Long, T> idValMap = new HashMap<>();
        try {
            for (T detail : list) {
                Long parentId = (Long) ReflectUtil.getFieldValue(detail, "parentId");
                Long id = (Long) ReflectUtil.getFieldValue(detail, "id");
                ReflectUtil.setFieldValue(detail, "children", new ArrayList<T>());
                idValMap.put(id, detail);
                if (NumberUtil.equals(Long.valueOf(0), parentId)) {
                    resList.add(detail);
                    continue;
                }
                T parent = idValMap.get(parentId);
                if (null != parent) {
                    List<T> children = (List<T>) ReflectUtil.getFieldValue(parent, "children");
                    children.add(detail);
                }
            }
        } catch (Exception e) {
            log.error("字段格式不合法，请明确实体字段", e);
        }
        return resList;
    }

}
