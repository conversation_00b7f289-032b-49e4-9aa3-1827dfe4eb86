package com.letu.solutions.util.util.upload;

import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.ObjectListing;
import com.aliyun.oss.model.OSSObjectSummary;

import java.io.File;
import java.util.List;

public class OSSFileRenamer {

    // OSS配置信息
    private static final String ENDPOINT = "https://oss-cn-hangzhou.aliyuncs.com";
    private static final String ACCESS_KEY_ID = "LTAI5tAgh28TtqF9vz9pguZd";
    private static final String ACCESS_KEY_SECRET = "******************************";
    private static final String BUCKET_NAME = "qdn";
    private static final String DIRECTORY = "prod/txt/business/"; // 指定目录，例如 "test/"

    public static void main(String[] args) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
        try {
            File file = new File("/Users/<USER>/Downloads/aaaa.txt");
            // 下载文件到本地
            ossClient.getObject(new GetObjectRequest("qdn", "prod/txt/business/1910621314492440576.txt"),file);
            System.out.println("文件下载成功！");
            System.out.println("---->"+file.getName());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭OSSClient
            ossClient.shutdown();
        }
        //try {
        //    // 列出指定目录下的所有文件
        //    ListObjectsRequest listObjectsRequest = new ListObjectsRequest(BUCKET_NAME);
        //    listObjectsRequest.setPrefix(DIRECTORY); // 设置前缀为指定目录
        //    listObjectsRequest.setDelimiter("/"); // 设置分隔符为"/"，避免列出子目录
        //    listObjectsRequest.setMaxKeys(1000); // 设置最大返回数量
        //
        //    ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);
        //
        //    // 遍历文件列表
        //    List<OSSObjectSummary> objectSummaries = objectListing.getObjectSummaries();
        //    int size = objectSummaries.size();
        //    System.out.println("size = " + size);
        //    for (OSSObjectSummary objectSummary : objectSummaries) {
        //        String objectKey = objectSummary.getKey();
        //        // 检查文件是否在指定目录下
        //        if (objectKey.startsWith(DIRECTORY) && !objectKey.endsWith("/")) {
        //            // 获取文件名和后缀
        //            String fileName = objectKey.substring(DIRECTORY.length());
        //            String[] parts = fileName.split("\\.");
        //            if (parts.length > 1 && parts[parts.length - 1].equals("text")) {
        //                // 构建新的文件名（将text后缀改为txt）
        //                String newFileName = fileName.substring(0, fileName.lastIndexOf("text")) + "txt";
        //                String newObjectKey = DIRECTORY + newFileName;
        //
        //                // 检查目标文件是否已存在
        //                if (!ossClient.doesObjectExist(BUCKET_NAME, newObjectKey)) {
        //                    // 重命名文件
        //                    ossClient.copyObject(BUCKET_NAME, objectKey, BUCKET_NAME, newObjectKey);
        //                    System.out.println("文件已重命名: " + objectKey + " -> " + newObjectKey);
        //
        //                    // 删除原始文件
        //                    ossClient.deleteObject(BUCKET_NAME, objectKey);
        //                    System.out.println("原始文件已删除: " + objectKey);
        //                } else {
        //                    System.out.println("目标文件已存在，跳过: " + objectKey);
        //                }
        //            }
        //        }
        //    }
        //
        //    System.out.println("文件后缀修改完成！");
        //} catch (Exception e) {
        //    System.err.println("操作失败: " + e.getMessage());
        //    e.printStackTrace();
        //} finally {
        //    // 关闭OSSClient
        //    ossClient.shutdown();
        //}
    }
}