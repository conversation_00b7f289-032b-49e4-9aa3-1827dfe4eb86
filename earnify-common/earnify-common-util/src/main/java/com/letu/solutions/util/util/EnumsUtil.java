package com.letu.solutions.util.util;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ReflectUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EnumsUtil {
    public static List<Map<String, Object>> getEnumKeyValueList(Class<? extends Enum<?>> clazz) {
        List<String> fieldNames = EnumUtil.getFieldNames(clazz);
        Enum<?>[] enumConstants = clazz.getEnumConstants();
        List<Map<String, Object>> keyValueList = new ArrayList<>();
        for (Enum<?> enumConstant : enumConstants) {
            Map<String, Object> keyValueMap = new HashMap<>();
            for (String fieldName : fieldNames) {
                keyValueMap.put(fieldName, ReflectUtil.getFieldValue(enumConstant, fieldName));
            }
            keyValueList.add(keyValueMap);
        }
        return keyValueList;
    }
}
