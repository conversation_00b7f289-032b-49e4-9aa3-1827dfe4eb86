package com.letu.solutions.util.util.captcha;

import com.aliyun.captcha20230305.Client;
import com.aliyun.teaopenapi.models.Config;
import com.letu.solutions.core.configuration.UploadConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/5/7
 */
@Slf4j
@Configuration
public class CaptchaConfiguration {
    @Resource
    private UploadConfig uploadConfig;

    @Bean
    public Client client() throws Exception {
        Config config = new Config();
        // 设置您的AccessKey ID 和 AccessKey Secret。
        // getEnvProperty只是个示例方法，需要您自己实现AccessKey ID 和 AccessKey Secret安全的获取方式。
        config.accessKeyId = uploadConfig.getAccessKeyId();
        config.accessKeySecret = uploadConfig.getAccessKeySecret();
        //设置请求地址 国内调用地址 captcha.cn-shanghai.aliyuncs.com   新加坡调用地址 xxxxx.captcha-open-southeast.aliyuncs.com （xxx为用户身份标）
        config.endpoint = "captcha.cn-shanghai.aliyuncs.com";
        // 设置连接超时为5000毫秒
        config.connectTimeout = 5000;
        // 设置读超时为5000毫秒
        config.readTimeout = 5000;
        return new Client(config);
    }


}
