package com.letu.solutions.util.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.letu.solutions.core.config.SpringUtil;
import com.letu.solutions.core.configuration.UploadConfig;
import com.letu.solutions.core.exception.ThrowException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class ChuangLanReqUtil {

    /**
     * 身份证认证地址
     */
    public final String idCardUrl = "https://api.253.com/open/i/ocr/id-ocr-cl";
    public final String htUrl = "https://api.253.com/sdk/liveSDK/livingDetection";
    public final String faceUrl = "https://api.253.com/open/idmatch/idmatch-new";
    public final String h5RenXiang = "https://sdk.253.com/identity_auth/faceAlive/v3/match";
    public final String appId = "GTN3ESQs";
    public final String appKey = "XKOcK0d7";


    public final String h5AppKey = "gsj3VI1Bq27P34bf";
    public final String h5AppSecret = "QbBIhiFvq2LV1i3u";
    public final RSA rsa = SecureUtil.rsa(null, Base64.decode("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCuQue3tJHQi+wm0vDThx/YUgSE+IVlJ7K2aHtmzbflmZDP1ruVZlRxBvxzT2aw0savKRNOCc/brHqv2xEoMRRM39NFLuDvtshM0gA8pCfUypukml1dye6sDrUbMprndaAEgsPaSBQOOkzpHvLq3wsTr3jyDUUYn9oyokXmuCUT5wIDAQAB"));
    public final Integer postTime = 30;

    public final Integer countTime = 24;


    public String h5RenXiangRes(String authToken, String idCardNo, String idCardName) {
        try {
            Map<String, Object> map = new HashMap<>(16);
            map.put("app_key", h5AppKey);
            map.put("app_secret", h5AppSecret);
            map.put("auth_token", authToken);
            map.put("id_card_no", idCardNo);
            map.put("id_card_name", idCardName);
            HttpRequest post = HttpUtil.createPost(h5RenXiang);
            post.body(JSONUtil.toJsonStr(map));
            String body = post.execute().body();
            log.info("创蓝获取人像比对出入参,req:{},res:{}", JSONUtil.toJsonStr(map), body);
            JSONObject res = JSONUtil.parseObj(body);
            if (res.getStr("code").equals("000000")
                    && StrUtil.isNotEmpty(res.getJSONObject("data").getStr("match"))
                    && "pass".equals(res.getJSONObject("data").getStr("match"))) {
                String imgBase64 = res.getJSONObject("data").getStr("image");
                return imgBase64;
            } else {
                throw new ThrowException("#认证失败");
            }
        } catch (ThrowException e) {
            throw e;
        } catch (Exception e) {
            log.error("创蓝获取活体结果获取异常", e);
            throw new ThrowException("#创蓝获取活体结果获取异常，请联系管理员");
        }
    }

    /**
     * 身份证ocr
     *
     * @param image   图片url地址
     * @param ocrType ocr类型，0表示身份证正面，1表示身份证反面
     * @return
     */
    public JSONObject getOcr(String image, Integer ocrType) {
        try {
            Map<String, Object> map = new HashMap<>(5);
            map.put("imageType", "URL");
            map.put("appId", appId);
            map.put("appKey", appKey);
            map.put("ocrType", ocrType);
            map.put("image", SpringUtil.getBean(UploadConfig.class).getBaseUrl() + image);
            String body = HttpUtil.post(idCardUrl, map, 3000);
            log.info("ChuanglanSdkUtils.getOcr param:" + map + ",body:" + body);
            JSONObject result = JSONUtil.parseObj(body);
            String code = result.getStr("code");
            if ("200000".equals(code)) {
                String data = result.getStr("data");
                JSONObject dataObject = JSONUtil.parseObj(data);
                return dataObject;
            }
        } catch (Exception e) {
            log.error("ChuanglanSdkUtils.getOcr 异常:{}", e);
            throw new ThrowException("#身份证识别异常，请联系管理员");
        }
        return null;
    }

    /**
     * SDK活体检测结果查询
     *
     * @param certifyId sdk活体认证标识
     * @param appkey    应⽤appkey
     * @return
     */
    public boolean getHt(String certifyId, String appkey) {
        try {
            Long timeStamp = System.currentTimeMillis();
            Map<String, Object> map = new HashMap<>(4);
            map.put("certifyId", certifyId);
            map.put("timeStamp", timeStamp);
            map.put("sign", DigestUtil.md5Hex(certifyId + timeStamp.toString() + appkey));
            String body = HttpUtil.post(htUrl, map, 3000);
            log.info("ChuanglanSdkUtils.getHt param:" + map + ",body:" + body);
            JSONObject result = JSONUtil.parseObj(body);
            String code = result.getStr("code");
            if ("200000".equals(code)) {
                String data = result.getStr("data");
                JSONObject jsonObject = JSONUtil.parseObj(data);
                String passed = jsonObject.getStr("passed");
                if ("T".equals(passed)) {
                    return Boolean.TRUE;
                } else {
                    log.info("ChuanglanSdkUtils.getHt SDK活体检测结果查询失败:{}", data);
                    throw new ThrowException(jsonObject.getStr("message"));
                }
            } else {
                throw new ThrowException(result.getStr("message"));
            }
        } catch (ThrowException e) {
            throw e;
        } catch (Exception e) {
            log.error("ChuanglanSdkUtils.getHt 异常:{}", e);
            throw new ThrowException("#活体检测异常，请联系管理员");
        }
    }

    /**
     * 人像比对
     *
     * @param image 图片url地址
     * @param idNum 身份证号
     * @param name  姓名
     * @return
     */
    public boolean getFace(String image, String idNum, String name) {
        try {
            String imageUrl = SpringUtil.getBean(UploadConfig.class).getBaseUrl() + image;
            Map<String, Object> map = new HashMap<>(5);
            map.put("appId", appId);
            map.put("appKey", appKey);
            map.put("image", Base64.encode(HttpUtil.downloadBytes(imageUrl)));
            map.put("idNum", idNum);
            map.put("name", name);
            String body = HttpUtil.post(faceUrl, map, 3000);
            log.info("ChuanglanSdkUtils.getFace param:" + map + ",body:" + body);
            JSONObject result = JSONUtil.parseObj(body);
            String code = result.getStr("code");
            if ("200000".equals(code)) {
                String data = result.getStr("data");
                JSONObject jsonObject = JSONUtil.parseObj(data);
                String faceResult = jsonObject.getStr("result");
                String idcardResult = jsonObject.getStr("idcardResult");
                String photoResult = jsonObject.getStr("photoResult");
                if ("01".equals(faceResult) && "01".equals(idcardResult) && "01".equals(photoResult)) {
                    return Boolean.TRUE;
                } else {
                    log.info("ChuanglanSdkUtils.getFace 人像比对失败:{}", data);
                    if (StrUtil.isNotEmpty(jsonObject.getStr("message"))) {
                        throw new ThrowException(jsonObject.getStr("message"));
                    }
                    if (StrUtil.isNotEmpty(jsonObject.getStr("photoMessage"))) {
                        throw new ThrowException(jsonObject.getStr("photoMessage"));
                    }
                    if (StrUtil.isNotEmpty(jsonObject.getStr("idcardMessage"))) {
                        throw new ThrowException(jsonObject.getStr("idcardMessage"));
                    }
                    throw new ThrowException("#人像比对失败");
                }
            } else {
                throw new ThrowException(result.getStr("message"));
            }
        } catch (ThrowException e) {
            throw e;
        } catch (Exception e) {
            log.error("ChuanglanSdkUtils.getFace 异常:{}", e);
            throw new ThrowException("#人像比对异常，请联系管理员");
        }
    }
}
