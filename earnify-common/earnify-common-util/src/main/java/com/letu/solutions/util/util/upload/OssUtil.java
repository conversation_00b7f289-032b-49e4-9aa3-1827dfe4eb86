package com.letu.solutions.util.util.upload;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.utils.IOUtils;
import com.letu.solutions.core.configuration.UploadConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * oss上传工具
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class OssUtil implements OssFileUtil {
    private final UploadConfig uploadConfig;

    /**
     * 获取oss client
     *
     * @return
     */
    public OSSClient getOSSClient() {
        CredentialsProvider provider = new DefaultCredentialProvider(uploadConfig.getAccessKeyId(), uploadConfig.getAccessKeySecret());
        return new OSSClient(uploadConfig.getEndpoint(), provider, null);
    }

    /**
     * 上传工具
     *
     * @param in
     * @param path
     */
    public void upload(InputStream in, String path) {
        log.info("OSSEngine oss filePath :" + path);
        OSSClient ossClient = null;
        try {
            ossClient = getOSSClient();
            ossClient.putObject(uploadConfig.getBucketName(), path, in);
        } catch (Exception e) {
            log.error("上传文件失败!", e);
        } finally {
            IOUtils.safeClose(in);
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 上传工具
     *
     * @param bytes
     * @param path
     * @return
     */
    public boolean upload(byte[] bytes, String path) {
        log.info("OSSEngine oss filePath :" + path);
        OSSClient ossClient = null;
        InputStream in = null;
        try {
            ossClient = getOSSClient();
            in = new ByteArrayInputStream(bytes);
            ossClient.putObject(uploadConfig.getBucketName(), path, in);
            return true;
        } catch (Exception e) {
            log.error("上传文件失败!", e);
        } finally {
            IOUtils.safeClose(in);
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return false;
    }
}
