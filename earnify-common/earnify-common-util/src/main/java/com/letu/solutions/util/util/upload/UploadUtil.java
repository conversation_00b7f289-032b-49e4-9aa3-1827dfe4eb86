package com.letu.solutions.util.util.upload;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.letu.solutions.core.configuration.UploadConfig;
import com.letu.solutions.core.constant.CommonConstant;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.util.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.awt.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * 上传工具
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UploadUtil {
    private final UploadConfig uploadConfig;
    private final OssFileUtil ossFileUtil;
    private final IdUtil idUtil;


    /**
     * 官网上传的资源
     *
     * @param file
     * @return
     * @throws Exception
     */
    public String uploadWeb(MultipartFile file) throws Exception {
        String filename = file.getOriginalFilename().trim();
        String type = filename.substring(filename.lastIndexOf(".") + 1);
        if (file.getSize() > 1024 * 1024 * 100) {//官网统一上传不能大于100M
            throw new ThrowException("文件大小不合法！");
        }
        String videoName = idUtil.loadSnowflakeId() + "." + type;
        String fullName = "test/web/" + videoName;
        ossFileUtil.upload(file.getInputStream(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }


    /**
     * 添加水印公共方法.
     *
     * @param streamName 原始数据流名称.
     * @param oldStream  原始数据流.
     * @param text       水印文字.
     * @return
     * @throws IOException
     */
    private byte[] buildWatermark(String streamName, InputStream oldStream, String text) throws IOException, FontFormatException {
        //添加水印.
        byte[] bs;
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            final InputStream resourceAsStream = UploadUtil.class.getClassLoader().getResourceAsStream("simhei.ttf");
            if (resourceAsStream == null) {
                log.error("生成文件[{}]时,无法获取生成水印文字的字体文件!", streamName);
                throw new ThrowException("系统错误,上传图片失败!");
            }
            final Font font = Font.createFont(Font.TRUETYPE_FONT, resourceAsStream).deriveFont(25F);

            ImgUtil.pressText(oldStream, bos,
                    text, new Color(215, 215, 215), font,
                    0, 0, 0.5f);

            //基于内存获取图片数据数组.
            bs = bos.toByteArray();
        }
        return bs;
    }


    public String uploadImage(MultipartFile file, String modPath) throws Exception {
        String filename = file.getOriginalFilename();
        log.info("接收到图片文件上传文件,fileName:{},modPath:{}", filename, modPath);
        String type = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        if (!Arrays.asList(CommonConstant.FILE_CONSTANT.IMG_SUFFIX).contains(type.toLowerCase())) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        if (file.getSize() > uploadConfig.getImgMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String imgName = idUtil.loadSnowflakeId() + "." + type;
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getImgPrefix() + "/" + imgName : uploadConfig.getImgPrefix() + "/" + modPath + "/" + imgName;
        ossFileUtil.upload(file.getInputStream(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadImage(byte[] bytes, String modPath) throws Exception {
        log.info("接收到图片文件上传文件,modPath:{}", modPath);
        if (bytes.length > uploadConfig.getImgMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String imgName = idUtil.loadSnowflakeId() + ".jpg";
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getImgPrefix() + "/" + imgName : uploadConfig.getImgPrefix() + "/" + modPath + "/" + imgName;
        ossFileUtil.upload(bytes, fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadImage(File file, String modPath) throws Exception {
        String filename = FileUtil.getName(file);
        String type = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        List<String> picTypes = new ArrayList<>();
        picTypes.add("jpg");
        picTypes.add("png");
        picTypes.add("gif");
        picTypes.add("jpeg");
        picTypes.add("webp");
        picTypes.add("ico");
        if (!picTypes.contains(type.toLowerCase())) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        String imgName = idUtil.loadSnowflakeId() + "." + type;
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getImgPrefix() + "/" + imgName : uploadConfig.getImgPrefix() + "/" + modPath + "/" + imgName;
        ossFileUtil.upload(FileUtil.getInputStream(file), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadApk(MultipartFile file, String modPath, boolean needRename) throws Exception {
        String filename = file.getOriginalFilename();
        String type = filename.substring(filename.lastIndexOf(".") + 1);
        String name = filename.substring(0, filename.lastIndexOf("."));
        if (!"apk".equals(type)) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        String imgName = needRename ? name + "_" + idUtil.loadSnowflakeId() + "." + type : filename;
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getApkPrefix() + "/" + imgName : uploadConfig.getApkPrefix() + "/" + modPath + "/" + imgName;
        ossFileUtil.upload(file.getInputStream(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadTxt(MultipartFile file, String modPath) throws Exception {
        String filename = file.getOriginalFilename();
        String type = filename.substring(filename.lastIndexOf(".") + 1);
        if (!"txt".equals(type)) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        if (file.getSize() > uploadConfig.getTxtMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String imgName = idUtil.loadSnowflakeId() + "." + type;
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getTxtPrefix() + "/" + imgName : uploadConfig.getTxtPrefix() + "/" + modPath + "/" + imgName;
        ossFileUtil.upload(file.getInputStream(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadExcel(MultipartFile file, String modPath) throws Exception {
        String filename = file.getOriginalFilename();
        String type = FileUtil.getSuffix(filename);

        if (!"xlsx".equals(type)) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        if (file.getSize() > uploadConfig.getExcelMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }

        Long loadSnowflakeId = idUtil.loadSnowflakeId();
        StringJoiner fullName = new StringJoiner("/");
        if (StringUtils.isBlank(modPath)) {
            fullName.add(uploadConfig.getExcelPrefix());
        } else {
            fullName.add(uploadConfig.getExcelPrefix() + modPath);
        }
        fullName.add(String.valueOf(loadSnowflakeId));
        fullName.add(filename);
        ossFileUtil.upload(file.getInputStream(), fullName.toString());
        return uploadConfig.getBaseUrl() + "/" + fullName.toString();
    }

    public String uploadExcel(byte[] bytes, String path) throws Exception {
        StringJoiner fullName = new StringJoiner("/");
        fullName.add(uploadConfig.getExcelPrefix() + path);
        ossFileUtil.upload(bytes, fullName.toString());
        return uploadConfig.getBaseUrl() + "/" + fullName.toString();
    }

    public String uploadPdf(byte[] bytes, String path) throws Exception {
        StringJoiner fullName = new StringJoiner("/");
        fullName.add(uploadConfig.getPdfPrefix() + path);
        ossFileUtil.upload(bytes, fullName.toString());
        return uploadConfig.getBaseUrl() + "/" + fullName.toString();
    }


    /**
     * 视频上传
     *
     * @param file
     * @param modPath
     * @return
     * @throws Exception
     */
    public String uploadVideo(MultipartFile file, String modPath) throws Exception {
        String filename = file.getOriginalFilename();
        String type = filename.substring(filename.lastIndexOf(".") + 1);
        if (!Arrays.asList(CommonConstant.FILE_CONSTANT.GAME_SUFFIX).contains(type.toLowerCase())) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        if (file.getSize() > uploadConfig.getVideoMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String videoName = idUtil.loadSnowflakeId() + "." + type;
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getVideoPrefix() + "/" + videoName : uploadConfig.getVideoPrefix() + "/" + modPath + "/" + videoName;
        ossFileUtil.upload(file.getInputStream(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    /**
     * 视频上传
     *
     * @param file
     * @param modPath
     * @return
     * @throws Exception
     */
    public String uploadMedia(MultipartFile file, String modPath) throws Exception {
        String filename = file.getOriginalFilename();
        String type = filename.substring(filename.lastIndexOf(".") + 1);
        if (!Arrays.asList(CommonConstant.FILE_CONSTANT.MEDIA_SUFFIX).contains(type.toLowerCase())) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        if (file.getSize() > uploadConfig.getVideoMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String mediaName = idUtil.loadSnowflakeId() + "." + type;
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getMediaPrefix() + "/" + mediaName : uploadConfig.getMediaPrefix() + "/" + modPath + "/" + mediaName;
        ossFileUtil.upload(file.getInputStream(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadHtml(String detail, String modPath) throws Exception {
        if (detail.length() > uploadConfig.getTxtMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String htmlName = idUtil.loadSnowflakeId() + ".html";
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getTxtPrefix() + "/" + htmlName : uploadConfig.getTxtPrefix() + "/" + modPath + "/" + htmlName;
        ossFileUtil.upload(detail.getBytes(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadText(String detail, String modPath) throws Exception {
        String textName = idUtil.loadSnowflakeId() + ".text";
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getTxtPrefix() + "/" + textName : uploadConfig.getTxtPrefix() + "/" + modPath + "/" + textName;

        ossFileUtil.upload(StrUtil.utf8Bytes(detail), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public String uploadText(byte[] data, String modPath) throws Exception {
        String textName = idUtil.loadSnowflakeId() + ".text";
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getTxtPrefix() + "/" + textName : uploadConfig.getTxtPrefix() + "/" + modPath + "/" + textName;

        boolean upload = ossFileUtil.upload(data, fullName);
        return upload ? uploadConfig.getBaseUrl() + "/" + fullName : null;
    }

    public String uploadJson(String detail, String modPath) throws Exception {
        if (detail.length() > uploadConfig.getTxtMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String jsonName = idUtil.loadSnowflakeId() + ".json";
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getTxtPrefix() + "/" + jsonName : uploadConfig.getTxtPrefix() + "/" + modPath + "/" + jsonName;
        ossFileUtil.upload(detail.getBytes(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

    public void uploadDomain(String content, Integer type, String bodyCode) throws Exception {
        String baseUrl = uploadConfig.getDomain();
        if (StrUtil.isNotEmpty(bodyCode)) {
            baseUrl = baseUrl + "/" + bodyCode;
        }
        String fullName = baseUrl + "/" + type;
        ossFileUtil.upload(StrUtil.utf8Bytes(content), fullName);
    }

    //获取图片二进制
    private static byte[] downloadPicture(String url) {
        URL urlConnection = null;
        HttpURLConnection httpURLConnection = null;
        try {
            urlConnection = new URL(url);
            httpURLConnection = (HttpURLConnection) urlConnection.openConnection();
            InputStream in = httpURLConnection.getInputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            in.close();
            out.close();
            return out.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            httpURLConnection.disconnect();
        }
        return null;
    }

    //二进制文件转换MultipartFile
    public static MultipartFile getMultipartFile(String name, byte[] bytes) {
        MultipartFile mfile = null;
        ByteArrayInputStream in = null;
        try {
            in = new ByteArrayInputStream(bytes);
            mfile = new MockMultipartFile(name, in);
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mfile;
    }

    /**
     * 下载并上传图片
     *
     * @param url
     * @param modPath
     * @return
     * @throws Exception
     */
    public String uploadImageForUrl(String url, String modPath) throws Exception {
        byte[] bytes = downloadPicture(url);
        String name = url.substring(url.lastIndexOf("."));
        MultipartFile file = getMultipartFile(name, bytes);
        String filename = file.getOriginalFilename();
        log.info("接收到图片文件上传文件,fileName:{},modPath:{}", filename, modPath);
        String type = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        if (!Arrays.asList(CommonConstant.FILE_CONSTANT.IMG_SUFFIX).contains(type.toLowerCase())) {
            throw new ThrowException("文件类型不合法！");
        }
        if (StringUtils.contains(filename, " ")) {
            throw new ThrowException("文件名不能有空格！");
        }
        if (file.getSize() > uploadConfig.getImgMaxSize()) {
            throw new ThrowException("文件大小不合法！");
        }
        String imgName = idUtil.loadSnowflakeId() + "." + type;
        String fullName = StrUtil.isEmpty(modPath) ? uploadConfig.getImgPrefix() + "/" + imgName : uploadConfig.getImgPrefix() + "/" + modPath + "/" + imgName;
        ossFileUtil.upload(file.getInputStream(), fullName);
        return uploadConfig.getBaseUrl() + "/" + fullName;
    }

}
