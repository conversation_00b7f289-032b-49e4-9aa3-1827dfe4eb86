FROM crpi-yopl8jz481bxz0wq.cn-hangzhou.personal.cr.aliyuncs.com/sytl/jdk21:v1
ARG jarName
ARG namespace
ARG serverAddr
RUN echo "args: $jarName $namespace $serverAddr"
MAINTAINER 木鱼
ADD $jarName.jar /data/$jarName.jar
ENV jarName=$jarName \
    serverAddr=$serverAddr \
    namespace=$namespace
ENTRYPOINT ["sh", "-c", "java -Dfile.encoding=utf-8  -Dsun.jnu.encoding=UTF-8 -jar /data/$jarName.jar --spring.cloud.nacos.serverAddr=$serverAddr"]