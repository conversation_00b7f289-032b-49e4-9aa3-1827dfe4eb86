package com.letu.solutions.async.consumer;

import cn.hutool.core.lang.Pair;
import com.letu.solutions.core.configuration.RocketMqConfig;
import com.letu.solutions.core.enums.mq.MqEnum;
import com.letu.solutions.util.util.TraceUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.SessionCredentialsProvider;
import org.apache.rocketmq.client.apis.StaticSessionCredentialsProvider;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Configuration
@DependsOn({"cacheRedisConfig"})
@Slf4j
public abstract class AbstractConsumer implements InitializingBean {

    @Autowired
    private RocketMqConfig rocketMqConfig;
    @Resource
    private TraceUtil traceUtil;

    public final static List<Pair<PushConsumer,String>> consumerList = new ArrayList<Pair<PushConsumer,String>>();

    /**
     * 开启消费者监听服务
     */
    public void listener(MqEnum mqEnum) throws Exception {
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        SessionCredentialsProvider sessionCredentialsProvider =
                new StaticSessionCredentialsProvider(rocketMqConfig.getAccessKey(), rocketMqConfig.getSecretKey());
        ClientConfiguration clientConfiguration = ClientConfiguration.newBuilder()
                .setEndpoints(rocketMqConfig.getNameSrvAddr())
                .setCredentialProvider(sessionCredentialsProvider)
                .setRequestTimeout(Duration.ofSeconds(10))
                .build();
        if(mqEnum.getThreadNum() == 0 ){
            return;
        }
        String group = mqEnum.getGroupId() + "_" + rocketMqConfig.getKey().toUpperCase();
        PushConsumer consumer = provider.newPushConsumerBuilder()
                .setClientConfiguration(clientConfiguration)
                .setConsumptionThreadCount(mqEnum.getThreadNum())
                .setConsumerGroup(group)
                .setSubscriptionExpressions(Collections.singletonMap(rocketMqConfig.getTopicKey() + mqEnum.getTopic(), new FilterExpression(mqEnum.getTag(), FilterExpressionType.TAG)))
                .setMessageListener(AbstractConsumer.this::doConsume)
                .build();
        consumerList.add(new Pair<>(consumer, rocketMqConfig.getTopicKey() + mqEnum.getTopic()));
        log.info("开启 {} - {} - 启动成功 ---", mqEnum.getTopic(), mqEnum.getTag());
    }

    public abstract MqEnum getEnum();

    public ConsumeResult doConsume(MessageView message) {
        traceUtil.injectContext(message);
        log.info("Receive message - {} - {} - {}", getEnum().getMessage(), message.getTopic(), message.getMessageId());
        try {
            return solve(message);
        } catch (Exception e) {
            log.error("{} - 消费失败 - e:{}", getEnum().getMessage(), e, e);
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 执行埋点操作
     *
     * @param message
     */
    public abstract ConsumeResult solve(MessageView message);

    @Override
    public void afterPropertiesSet() {
        try {
            log.info("开始启动监听");
            listener(getEnum());
        } catch (Exception e) {
            log.error("{} - 消费者监听器启动失败 - {}", getEnum().getMessage(), e, e);
        }
    }

}
