package com.letu.solutions.async.consumer;

import com.letu.solutions.core.enums.mq.MqEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Configuration;

/**
 * @description 订单取消任务消费者
 * <AUTHOR>
 * @createTime 2025/5/19 17:15
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class OrderCancelTaskConsumer extends AbstractConsumer {

    //    private final OrdersService ordersService;
    @Override
    public MqEnum getEnum() {
        return MqEnum.ORDER_CANCEL_DELAY;
    }

    @Override
    public ConsumeResult solve(MessageView message) {
//        String orderId = StandardCharsets.UTF_8.decode(message.getBody()).toString();
//        log.info("订单过期回调任务处理:orderId={}",orderId);
//        //获取订单数据
//        Orders orders = ordersService.selectById(Long.parseLong(orderId));
//        //只处理未支付的订单
//        if (orders == null || !Objects.equals(OrderEnum.STATUS_NOT_PAY.getKey(),orders.getOrderStatus())){
//            return ConsumeResult.SUCCESS;
//        }
//        //修改订单状态为已取消
//        orders.setOrderStatus(OrderEnum.STATUS_CANCELLED.getKey());
//        ordersService.updateEntity(orders);
        return ConsumeResult.SUCCESS;
    }
}
