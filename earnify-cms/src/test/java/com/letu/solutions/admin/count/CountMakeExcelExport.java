package com.letu.solutions.admin.count;

import java.util.ArrayList;
import java.util.List;

public class CountMakeExcelExport {
    static String str = "/**\n" +
            "     * 数据日期\n" +
            "     */\n" +
            "    private Integer day;\n" +
            "    /**\n" +
            "     * 渠道名称\n" +
            "     */\n" +
            "    private String channelName;\n" +
            "\n" +
            "    /**\n" +
            "     * 访问pv\n" +
            "     */\n" +
            "    private String visit;\n" +
            "\n" +
            "    /**\n" +
            "     * 访问uv\n" +
            "     */\n" +
            "    private String visitUv;\n" +
            "\n" +
            "    /**\n" +
            "     * h5登录总\n" +
            "     */\n" +
            "    private String allLoginSuccessUv;\n" +
            "\n" +
            "    /**\n" +
            "     * h5登录新\n" +
            "     */\n" +
            "    private String allLoginSuccessNewUv;\n" +
            "\n" +
            "    /**\n" +
            "     * h5访登率\n" +
            "     */\n" +
            "    private String visitLogin;\n" +
            "\n" +
            "    /**\n" +
            "     * 留资页访问uv\n" +
            "     */\n" +
            "    private String remainMeans1Uv;\n" +
            "\n" +
            "    /**\n" +
            "     * 留资完成uv\n" +
            "     */\n" +
            "    private String remainSuccessUv;\n" +
            "\n" +
            "    /**\n" +
            "     * 匹配助贷成功数\n" +
            "     */\n" +
            "    private String refereeProductUv;\n" +
            "\n" +
            "    /**\n" +
            "     * 匹配信贷api成功数\n" +
            "     */\n" +
            "    private String refereeApiLoanUv;\n" +
            "\n" +
            "    /**\n" +
            "     * 匹配回传数\n" +
            "     */\n" +
            "    private String registerNum;\n" +
            "\n" +
            "    /**\n" +
            "     * 提交助贷产品申请订单数\n" +
            "     */\n" +
            "    private String applySuccess;\n" +
            "\n" +
            "    /**\n" +
            "     * 助贷收益\n" +
            "     */\n" +
            "    private String income;\n" +
            "\n" +
            "    /**\n" +
            "     * 信贷api申请订单\n" +
            "     */\n" +
            "    private String apiApplySuccess;";

    public static void main(String[] args) {
        String template = "    @ExcelField(title = \"%s\",sort = %s)";
        String[] split = str.split("\n");
        int num = 0;
        List<String> list = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            String st = split[i];
            if (st.contains("/**")) {
                list.add(st);
                String st1 = split[i + 1];
                String title = st1.substring(7);
                list.add(st1);
                list.add(split[i + 2]);
                list.add(String.format(template, title, num++));
                list.add(split[i + 3]);
            }
        }
        for (String s : list) {
            System.out.println(s);
        }
    }
}
