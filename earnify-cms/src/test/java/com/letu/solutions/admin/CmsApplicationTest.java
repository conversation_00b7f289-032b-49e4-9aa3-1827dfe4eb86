package com.letu.solutions.admin;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.letu.solutions.util.util.MessageUtil;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class CmsApplicationTest {
    @Resource
    private MessageUtil messageUtil;

    public static void main(String[] args) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", "hello");
        jsonObject.put("code", 1);
        String jsonString = JSONUtil.toJsonStr(jsonObject);
        //String jsonString = JSONObject.toJSONString(jsonObject, SerializerFeature.DisableCircularReferenceDetect);
        System.out.println(jsonString);
    }
}

