/*
package com.letu.solutions.cms.count;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.cms.mapper.stat.StatCountTableKeyMapper;
import com.letu.solutions.cms.mapper.stat.StatCountTableKeyUniqueMapper;
import com.letu.solutions.cms.mapper.stat.StatCountTypeParamMapper;
import com.letu.solutions.model.entity.stat.StatCountTableKey;
import com.letu.solutions.model.entity.stat.StatCountTableKeyUnique;
import com.letu.solutions.model.entity.stat.StatCountTypeParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class CountStatTest {
    @Resource
    private StatCountTableKeyMapper statCountTableKeyMapper;
    @Resource
    private StatCountTableKeyUniqueMapper statCountTableKeyUniqueMapper;
    @Resource
    private StatCountTypeParamMapper statCountTypeParamMapper;

    */
/**
     * 统一增加osType
     *//*

    @Test
    public void insertType() {
        List<StatCountTableKey> statCountTableKeys = statCountTableKeyMapper.selectList(Wrappers.<StatCountTableKey>lambdaQuery().eq(StatCountTableKey::getHasUv, 1).eq(StatCountTableKey::getType, 1));
        for (StatCountTableKey statCountTableKey : statCountTableKeys) {
            StatCountTableKeyUnique statCountTableKeyUnique = statCountTableKeyUniqueMapper.selectOne(Wrappers.<StatCountTableKeyUnique>lambdaQuery().eq(StatCountTableKeyUnique::getKeyId, statCountTableKey.getId()).eq(StatCountTableKeyUnique::getParam, "osType").last("limit 1"));
            if (null != statCountTableKeyUnique) {
                continue;
            }
            StatCountTypeParam osType = statCountTypeParamMapper.selectOne(Wrappers.<StatCountTypeParam>lambdaQuery().eq(StatCountTypeParam::getTypeId, statCountTableKey.getTypeId()).eq(StatCountTypeParam::getParam, "osType").last("limit 1"));
            StatCountTableKeyUnique record = new StatCountTableKeyUnique();
            record.setKeyId(statCountTableKey.getId());
            record.setParamId(osType.getId());
            record.setTableId(statCountTableKey.getTableId());
            record.setTableDescribe(statCountTableKey.getTableDescribe());
            record.setKeyName(statCountTableKey.getKeyName());
            record.setParam(osType.getParam());
            record.setParamDescribe(osType.getDescribe());
            StatCountTableKeyUnique sort = statCountTableKeyUniqueMapper.selectOne(Wrappers.<StatCountTableKeyUnique>lambdaQuery().eq(StatCountTableKeyUnique::getKeyId, statCountTableKey.getId()).last("limit 1"));
            statCountTableKeyUniqueMapper.insert(record);
//            System.out.println(1);

        }
    }

}
*/
