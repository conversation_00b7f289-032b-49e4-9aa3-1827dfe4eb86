package com.letu.solutions.admin;

import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@Slf4j
@SpringBootTest
public class ExcelTest {

    private static final String template = "/**\n" +
            "* %s\n" +
            "*/\n" +
            "@NotEmpty(message = \"%s不可为空\")\n" +
            "private String %s;\n";

    public static void main(String[] args) {
        ExcelReader reader = ExcelUtil.getReader("E:\\新建 XLSX 工作表.xlsx");
        List<Map<String, Object>> maps = reader.readAll();
        for (Map<String, Object> map : maps) {
            String field = (String) map.get("字段名");
            String desc = (String) map.get("说明");
            String descKey = descKey(desc);
            System.out.println(String.format(template, desc, descKey, field));
        }
    }

    public static String descKey(String desc) {
        if (StrUtil.isEmpty(desc)) {
            return "";
        }
        int index = desc.indexOf(":") > 0 ? desc.indexOf(":") : desc.indexOf("：");
        if (index <= 0) {
            index = desc.length();
        }
        return desc.substring(0, index);
    }
}
