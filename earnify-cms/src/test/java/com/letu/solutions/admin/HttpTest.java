package com.letu.solutions.admin;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
public class HttpTest {

//    public static void main(String[] args) {
//        List<String> list = LldHttpUtil.<String>build("dG9rZW46Y21zOjA6MToxOjE4ZjNkOThlNTdiZDExZWU4OWRlNGUwYzQ1ZTM4MTgx")
//                .getList("cms/category/list", new JSONObject() {{
//            put("type", "nft");
//        }});
//        System.out.println(list);
//        List<String> list1 = LldHttpUtil.<String>build()
//                .getList("cms/category/list", new JSONObject() {{
//                    put("type", "nft");
//                }});
//        System.out.println(list1);
//    }

}
