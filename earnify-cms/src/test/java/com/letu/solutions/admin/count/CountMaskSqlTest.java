package com.letu.solutions.admin.count;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CountMaskSqlTest {
    public static void main(String[] args) {
        mkMsql();
//        mkEntity();
    }
    public static void mkMsql(){
        String sql = "\n" +
                "visit,\n" +
                "visit_uv,\n" +
                "all_login_success_uv,\n" +
                "all_login_success_new_uv,\n" +
                "visit_login,\n" +
                "remain_means1_uv,\n" +
                "remain_success_uv,\n" +
                "referee_product_uv,\n" +
                "referee_api_loan_uv,\n" +
                "register_num,\n" +
                "apply_success,\n" +
                "income,\n" +
                "api_apply_success";
        String[] split = sql.split("\n");
        String reg = "IFNULL(sum(%s), 0) %s,";
        for (String s : split) {
            s = s.replaceAll(",", "");
            String[] split1 = s.split("\\.");
            String key;
            if(split1.length>1){
                key = split1[1];
            }else{
                key = split1[0];
            }
            System.out.println(String.format(reg, s.trim(), key));
        }
    }


    public static void mkEntity(){
        String str = "\n" +
                "visit,\n" +
                "visit_uv,\n" +
                "all_login_success_uv,\n" +
                "all_login_success_new_uv,\n" +
                "visit_login,\n" +
                "remain_means1_uv,\n" +
                "remain_success_uv,\n" +
                "referee_product_uv,\n" +
                "referee_api_loan_uv,\n" +
                "register_num,\n" +
                "apply_success,\n" +
                "income,\n" +
                "api_apply_success";
        String[] split = str.split("\n");
        String reg = "/**\n *\n */\nprivate String %s; \n";
        for (String s : split) {
            String substring = s.substring(s.lastIndexOf(" ") + 1, s.length());
            substring = substring.replaceAll(",","");
            substring = StrUtil.toCamelCase(substring);
            System.out.println(String.format(reg, substring));
        }
    }
}
