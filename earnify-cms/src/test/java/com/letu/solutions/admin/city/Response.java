package com.letu.solutions.admin.city;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
class Response {

    @JsonProperty("status")
    private String status;
    @JsonProperty("info")
    private String info;
    @JsonProperty("infocode")
    private String infocode;
    @JsonProperty("count")
    private String count;
    @JsonProperty("districts")
    private List<DistrictsDTO> districts;

    @NoArgsConstructor
    @Data
    public static class DistrictsDTO {
        @JsonProperty("citycode")
        private List<?> citycode;
        @JsonProperty("adcode")
        private String adcode;
        @JsonProperty("name")
        private String name;
        @JsonProperty("center")
        private String center;
        @JsonProperty("level")
        private String level;
        @JsonProperty("districts")
        private List<DistrictsDTO> districts;
    }
}
