package com.letu.solutions.admin;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ValidTest {

/*    public static void main(String[] args) {
        NoticeAccountDingDingDto noticeAccountDingDingDto = new NoticeAccountDingDingDto();
        noticeAccountDingDingDto.setAccountId(1L);
        noticeAccountDingDingDto.setAccessKeyId("123456");
        noticeAccountDingDingDto.setMsgtype("test");
        noticeAccountDingDingDto.setTitle("test");
        noticeAccountDingDingDto.setText("text");
        noticeAccountDingDingDto.setMessageUrl("http://www.baidu.com");
        ValidUtil.validException(noticeAccountDingDingDto, DingDing.Text.class);

        ValidUtil.validException(noticeAccountDingDingDto, DingDing.DingDing3.class);
        System.out.println(1);
    }*/

}
