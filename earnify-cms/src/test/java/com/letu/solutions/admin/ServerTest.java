package com.letu.solutions.admin;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@SpringBootTest
public class ServerTest {

    public static void main(String[] args) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //初始线程大小
        executor.setCorePoolSize(10);
        //最大线程大小
        executor.setMaxPoolSize(10);
        //设置队列长度
        executor.setQueueCapacity(1000);
        //设置多长时间，线程回收
        executor.setKeepAliveSeconds(60000);
        executor.setThreadNamePrefix("async-service-asyncPool");
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        executor.submit(() -> {
            while (true) {
                test("customer");
            }
        });
        executor.submit(() -> {
            while (true) {
                test("product");
            }
        });
        executor.submit(() -> {
            while (true) {
                test("admin");
            }
        });
    }

    public static void test(String server) {
        try {
            HttpRequest get = HttpUtil.createGet(String.format("https://test.api.qrhjk.com/%s/index.e", server));
            System.out.println(server+ "  "+get.execute().body());
            Thread.sleep(100);
        } catch (Exception e) {
            log.error("发生异常:{}", server, e);
        }
    }
}
