package com.letu.solutions.admin.city;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;

class InitGaode {
    public static void main(String[] args) {
        HttpRequest get = HttpUtil.createGet("https://restapi.amap.com/v3/config/district?key=bedad8a789e477f0afc2a9794029ae64");
        Request request = new Request();
        get.form(BeanUtil.beanToMap(request));
        String body = get.execute().body();
        Response response = JSONObject.parseObject(body, Response.class);
        System.out.println(body);
    }
}
