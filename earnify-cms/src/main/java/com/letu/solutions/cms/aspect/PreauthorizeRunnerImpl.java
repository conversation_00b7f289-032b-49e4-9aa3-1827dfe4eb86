package com.letu.solutions.cms.aspect;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.cms.mapper.sys.SysInterfaceMapper;
import com.letu.solutions.model.entity.sys.SysInterface;
import com.letu.solutions.model.enums.InterfaceTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Component
@RequiredArgsConstructor
public class PreauthorizeRunnerImpl implements ApplicationRunner {
    private final SysInterfaceMapper sysInterfaceMapper;
    public static List<String> allBackInterfaces;

    @Override
    public void run(ApplicationArguments args) {
        List<SysInterface> sysInterfaces = sysInterfaceMapper.selectList(Wrappers.<SysInterface>lambdaQuery().eq(SysInterface::getType, InterfaceTypeEnum.method));
        allBackInterfaces = sysInterfaces.stream().map(SysInterface::getNameAll).collect(Collectors.toList());
    }
}