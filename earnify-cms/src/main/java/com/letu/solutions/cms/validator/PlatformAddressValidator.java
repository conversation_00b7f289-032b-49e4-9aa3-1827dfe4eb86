package com.letu.solutions.cms.validator;

import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressWithConfigRes;
import java.util.List;

public class PlatformAddressValidator {
    public static void validateAddressInConfig(String address, List<String> configList) {
        boolean exists = configList.stream()
            .anyMatch(cfg -> address != null && address.equalsIgnoreCase(cfg));
        if (!exists) {
            throw new RuntimeException("该地址未在平台配置中，禁止操作！");
        }
    }
} 