package com.letu.solutions.cms.dto;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.cms.aspect.ExcelField;

/**
 * 充值记录导出Excel DTO
 */
@Data
public class DepositRecordExcel implements Serializable {
    @ExcelField(title = "充值ID")
    private Long id;
    @ExcelField(title = "用户ID")
    private Long userId;
    @ExcelField(title = "用户名")
    private String username;
    @ExcelField(title = "协议网络")
    private String network;
    @ExcelField(title = "充值数量")
    private BigDecimal amount;
    @ExcelField(title = "币种")
    private String tokenSymbol;
    @ExcelField(title = "付款钱包地址")
    private String fromAddress;
    @ExcelField(title = "收款钱包地址")
    private String toAddress;
    @ExcelField(title = "链上交易哈希")
    private String txHash;
    @ExcelField(title = "凭证图片")
    private String evidenceImage;
    @ExcelField(title = "操作人")
    private String operator;
    @ExcelField(title = "备注")
    private String remark;
    @ExcelField(title = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 