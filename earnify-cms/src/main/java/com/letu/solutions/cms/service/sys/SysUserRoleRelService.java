package com.letu.solutions.cms.service.sys;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysUserRoleRelSaveReq;

import java.util.List;

/**
 * 用户-角色关系 服务类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface SysUserRoleRelService {

    List<Long> selectBasicList(Long userId);

    boolean saveUserRole(SysUserRoleRelSaveReq record, ExtendData extendData);
}
