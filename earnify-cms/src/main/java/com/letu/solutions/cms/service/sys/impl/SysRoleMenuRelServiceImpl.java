package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.mapper.sys.SysMenuMapper;
import com.letu.solutions.cms.mapper.sys.SysRoleMenuRelMapper;
import com.letu.solutions.cms.service.sys.SysRoleMenuRelService;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.model.cms.request.sys.SysRoleMenuRelSaveReq;
import com.letu.solutions.model.cms.response.sys.SysMenuVo;
import com.letu.solutions.model.entity.sys.SysRoleMenuRel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统角色-菜单关系 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysRoleMenuRelServiceImpl extends ServiceImpl<SysRoleMenuRelMapper, SysRoleMenuRel> implements SysRoleMenuRelService {
    private final SysMenuMapper sysMenuMapper;
    private final TransactionalManage transactionalManage;

    @Override
    public List<Long> selectBasicList(Long roleId) {
        if (NumberUtil.equals(roleId, Long.valueOf(1))) {
            return sysMenuMapper.allVoList().stream().map(SysMenuVo::getId).collect(Collectors.toList());
        }
        List<SysRoleMenuRel> resList = baseMapper.selectList(Wrappers.<SysRoleMenuRel>lambdaQuery().eq(SysRoleMenuRel::getRoleId, roleId));
        if (CollectionUtil.isEmpty(resList)) {
            return ListUtil.empty();
        }
        return resList.stream().map(SysRoleMenuRel::getMenuId).collect(Collectors.toList());
    }

    @Override
    public boolean saveRoleMenu(SysRoleMenuRelSaveReq record, ExtendData extendData) {
        if (NumberUtil.equals(record.getRoleId(), Long.valueOf(1))) {
            throw new ThrowException("#超管权限不可修改");
        }
        List<SysRoleMenuRel> list = new ArrayList<>();
        for (Long menuId : record.getMenuIds()) {
            SysRoleMenuRel details = new SysRoleMenuRel();
            details.setRoleId(record.getRoleId());
            details.setMenuId(menuId);
            list.add(details);
        }
        return transactionalManage.execute(() -> {
            baseMapper.delete(Wrappers.<SysRoleMenuRel>lambdaQuery().eq(SysRoleMenuRel::getRoleId, record.getRoleId()));
            if (CollectionUtil.isNotEmpty(list)) {
                this.saveBatch(list);
            }
            return true;
        });
    }
}