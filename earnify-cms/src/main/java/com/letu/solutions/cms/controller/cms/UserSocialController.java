package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.UserSocialService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserSocialListReq;
import com.letu.solutions.model.cms.request.cms.UserSocialSaveReq;
import com.letu.solutions.model.cms.request.cms.UserSocialUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserSocialPageRes;
import com.letu.solutions.model.cms.response.cms.UserSocialDetailRes;

/**
 * 用户/社交媒体绑定信息表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:user_social", valueDesc = "用户:社交媒体绑定信息表")
public class UserSocialController{
    private final UserSocialService basicService;
    /**
    * 社交媒体绑定信息表 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @PostMapping("/userSocial/page")
    public R<Page<UserSocialPageRes>> loadPage(@Validated @RequestBody UserSocialListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 社交媒体绑定信息表 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/userSocial/list")
    public R<List<UserSocialPageRes>> loadList(UserSocialListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 社交媒体绑定信息表 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/userSocial/selectById")
    public R<UserSocialDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 社交媒体绑定信息表 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/userSocial/insert")
    public R<Boolean> insert(@Validated @RequestBody UserSocialSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 社交媒体绑定信息表 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/userSocial/update")
    public R<Boolean> update(@Validated @RequestBody UserSocialUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 社交媒体绑定信息表 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/userSocial/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }
}
