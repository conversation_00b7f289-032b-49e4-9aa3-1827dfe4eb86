package com.letu.solutions.cms.validator;

import com.letu.solutions.cms.service.cms.FinanceConfigService;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@RequiredArgsConstructor
public class FinanceConfigValidator {

    private final FinanceConfigService configService;

    public void validateDeposit(String network,BigDecimal amount) {
        FinanceConfig config = configService.getConfig(network);
        if (amount.compareTo(config.getMinAmount()) < 0) {
            throw new ThrowException("小于最小充值金额");
        }
        if (amount.compareTo(config.getMaxAmount()) > 0) {
            throw new ThrowException("超过最大充值金额");
        }
    }

    public void validateWithdraw(String network,BigDecimal amount, int withdrawTimesToday) {
        FinanceConfig config = configService.getConfig(network);
        if (amount.compareTo(config.getMinWithdraw()) < 0) {
            throw new ThrowException("小于最小提现金额");
        }
        if (amount.compareTo(config.getMaxWithdraw()) > 0) {
            throw new ThrowException("超过最大提现金额");
        }
        if (withdrawTimesToday >= config.getMaxWithdrawTimesPerDay()) {
            throw new ThrowException("超过每日最多提现次数");
        }
    }
}

