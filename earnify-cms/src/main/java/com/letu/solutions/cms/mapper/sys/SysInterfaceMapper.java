package com.letu.solutions.cms.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.letu.solutions.model.entity.sys.SysInterface;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统接口 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Mapper

public interface SysInterfaceMapper extends BaseMapper<SysInterface> {
    List<String> allInterface();
    List<String> userVoList(@Param("userId") Long userId);
}
