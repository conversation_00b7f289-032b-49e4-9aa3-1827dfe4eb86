package com.letu.solutions.cms.controller.sys;

import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.sys.SysInterfaceService;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.cms.response.sys.SysInterfaceResponse;
import com.letu.solutions.model.entity.sys.SysInterface;
import com.letu.solutions.model.request.BaseRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统/系统接口权限
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "sys:interface", valueDesc = "系统:系统接口")
public class SysInterfaceController {
    private final SysInterfaceService basicService;

    /**
     * 系统接口 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/sysInterface/list")
    public R<List<SysInterfaceResponse>> loadList(BaseRequest request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 系统接口 扫描接口
     */
    @Preauthorize(value = "scan", valueDesc = "扫描接口")
    @GetMapping("/sysInterface/scan")
    public R<SysInterface> scan() {
        basicService.scan();
        return R.success(null);
    }


}
