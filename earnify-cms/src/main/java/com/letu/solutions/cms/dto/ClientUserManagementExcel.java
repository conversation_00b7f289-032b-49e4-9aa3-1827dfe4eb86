package com.letu.solutions.cms.dto;

import com.letu.solutions.cms.aspect.ExcelField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 甲方用户管理导出Excel DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class ClientUserManagementExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelField(title = "用户ID", sort = 1)
    private Long userId;

    /**
     * 用户名称
     */
    @ExcelField(title = "用户名称", sort = 2)
    private String userName;

    /**
     * 邮箱
     */
    @ExcelField(title = "邮箱", sort = 3)
    private String email;

    /**
     * 手机号
     */
    @ExcelField(title = "手机号", sort = 4)
    private String phone;

    /**
     * 关联产品数
     */
    @ExcelField(title = "关联产品数", sort = 5)
    private Integer relatedProductCount;

    /**
     * 发布任务次数
     */
    @ExcelField(title = "发布任务次数", sort = 6)
    private Integer publishTaskTimes;

    /**
     * 发布任务个数
     */
    @ExcelField(title = "发布任务个数", sort = 7)
    private Integer publishTaskCount;

    /**
     * 任务完成已领取个数
     */
    @ExcelField(title = "已领取任务数", sort = 8)
    private Integer taskCompletedReceivedCount;

    /**
     * 已完成个数
     */
    @ExcelField(title = "已完成任务数", sort = 9)
    private Integer taskCompletedCount;

    /**
     * 已发放金额
     */
    @ExcelField(title = "已发放金额", sort = 10)
    private BigDecimal totalGrantedAmount;

    /**
     * 发放奖励冻结中金额
     */
    @ExcelField(title = "奖励冻结金额", sort = 11)
    private BigDecimal rewardFrozenAmount;

    /**
     * 累计充值
     */
    @ExcelField(title = "累计充值", sort = 12)
    private BigDecimal totalRechargeAmount;

    /**
     * 当前余额
     */
    @ExcelField(title = "当前余额", sort = 13)
    private BigDecimal currentBalance;

    /**
     * 提现中
     */
    @ExcelField(title = "提现中", sort = 14)
    private BigDecimal withdrawingAmount;

    /**
     * 已提现
     */
    @ExcelField(title = "已提现", sort = 15)
    private BigDecimal totalWithdrawnAmount;

    /**
     * 创建时间
     */
    @ExcelField(title = "创建时间", sort = 16, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
