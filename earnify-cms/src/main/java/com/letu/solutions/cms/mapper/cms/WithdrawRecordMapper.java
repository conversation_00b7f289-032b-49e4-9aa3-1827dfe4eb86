package com.letu.solutions.cms.mapper.cms;

import com.letu.solutions.cms.dto.WithdrawStatistics;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 提现记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper

public interface WithdrawRecordMapper extends BaseMapper<WithdrawRecord> {
    @Select(""" 
            SELECT COUNT(*) FROM withdraw_record WHERE user_id = #{userId} AND network = #{network} AND DATE(create_time) = #{date} AND withdraw_status = 'completed'
            """)
    int countUserWithdrawToday(@Param("userId") Long userId, @Param("network") String network, @Param("date") String date);

    /**
     * 统计提现成功金额
     * @return 提现成功金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM withdraw_record WHERE withdraw_status = 'completed' ")
    BigDecimal sumWithdrawAmountByDate();

    /**
     * 批量获取用户提现统计数据
     * @param userIds 用户ID列表
     * @return 提现统计数据列表
     */
    @Select("""
            <script>
            SELECT
                CAST(user_id AS SIGNED) as userId,
                COALESCE(SUM(CASE WHEN withdraw_status = 'unprocessed' THEN amount ELSE 0 END), 0) as withdrawingAmount,
                COALESCE(SUM(CASE WHEN withdraw_status = 'completed' THEN amount ELSE 0 END), 0) as withdrawnAmount
            FROM withdraw_record
            WHERE user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            GROUP BY user_id
            </script>
            """)
    List<WithdrawStatistics> getWithdrawStatisticsByUserIds(@Param("userIds") List<Long> userIds);
}
