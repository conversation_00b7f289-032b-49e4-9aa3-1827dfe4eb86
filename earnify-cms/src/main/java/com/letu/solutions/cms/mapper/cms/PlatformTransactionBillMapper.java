package com.letu.solutions.cms.mapper.cms;

import com.letu.solutions.model.entity.cms.PlatformTransactionBill;
import com.letu.solutions.model.cms.response.cms.PlatformTransactionBillPageRes;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

/**
 * 平台账户流水表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper

public interface PlatformTransactionBillMapper extends BaseMapper<PlatformTransactionBill> {
    /**
     * 统计发放奖励金总金额
     * @return 奖励金总金额（BigDecimal）
     */
    BigDecimal sumProviderRewardAmount();

    /**
     * 统计解冻返还金额总金额
     * @return 解冻返还金额总金额（BigDecimal）
     */
    BigDecimal sumClientUnfreezeRefundAmount();

    /**
     * 分页查询平台流水（包含协议网络、平台钱包地址、操作人员信息）
     * 通过LEFT JOIN关联deposit_record和withdraw_record表获取额外信息
     */
    Page<PlatformTransactionBillPageRes> selectPageWithDetails(Page<PlatformTransactionBillPageRes> page,
                                                               @Param("request") Object request);
}
