package com.letu.solutions.cms.controller.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.user.UserService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.cms.request.cms.ClientUserManagementReq;
import com.letu.solutions.model.cms.request.user.*;
import com.letu.solutions.model.cms.response.cms.ClientUserManagementRes;
import com.letu.solutions.model.cms.response.user.UserDetailRes;
import com.letu.solutions.model.cms.response.user.UserPageRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户/用户
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "user:user", valueDesc = "用户:用户")
public class UserController{
    private final UserService basicService;
    /**
    * 用户 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/user/page")
    public R<Page<UserPageRes>> loadPage(UserListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 用户 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/user/list")
    public R<List<UserPageRes>> loadList(UserListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 用户 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/user/selectById")
    public R<UserDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 用户 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/user/insert")
    public R<Boolean> insert(@Validated @RequestBody UserSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 用户 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/user/update")
    public R<Boolean> update(@Validated @RequestBody UserUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 用户 启用/禁用
     */
    @Preauthorize(value = "delete", valueDesc = "启用/禁用")
    @PostMapping("/user/delete")
    public R<Boolean> delete(@Validated @RequestBody UserEnableReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateEnable(request, extendData));
    }

    /**
     * 同步用户到中台
     */
    @Preauthorize(value = "sync", valueDesc = "同步用户")
    @PostMapping("/user/sync")
    public R<Boolean> sync(@Validated @RequestBody UserSyncReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.sync(record, extendData));
    }

}
