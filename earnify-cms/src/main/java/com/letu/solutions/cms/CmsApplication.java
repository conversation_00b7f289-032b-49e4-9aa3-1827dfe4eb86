package com.letu.solutions.cms;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * 管理端模块启动类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/26
 */
@EnableAsync
@SpringBootApplication(scanBasePackages = "com.letu")
@EnableDiscoveryClient
@EnableDubbo
public class CmsApplication {

    public static void main(String[] args) {
        SpringApplication.run(CmsApplication.class, args);
    }

}