package com.letu.solutions.cms.controller.cms;

import com.letu.solutions.model.request.BaseRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.DataReportStatisticsService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import com.letu.solutions.core.constant.DateConstants;
import org.springframework.util.StringUtils;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsListReq;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsSaveReq;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsUpdateReq;
import com.letu.solutions.model.cms.response.cms.DataReportStatisticsPageRes;
import com.letu.solutions.model.cms.response.cms.DataReportStatisticsDetailRes;
import com.letu.solutions.model.cms.request.cms.UserGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.UserGrowthTrendRes;
import com.letu.solutions.model.cms.request.cms.TaskGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.TaskGrowthTrendRes;
import com.letu.solutions.model.cms.request.cms.FundGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.FundGrowthTrendRes;
import com.letu.solutions.model.cms.response.cms.TodoStatisticsRes;
import com.letu.solutions.model.cms.response.cms.FinanceStatisticsRes;

/**
 * 管理后台/首页数据统计
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:data_report_statistics", valueDesc = "系统:数据简报统计")
public class DataReportStatisticsController{
    private final DataReportStatisticsService basicService;


    /**
    * 数据简报统计 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/dataReportStatistics/page")
    public R<Page<DataReportStatisticsPageRes>> loadPage(DataReportStatisticsListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

//    /**
//     * 数据简报统计 列表查询
//     * @param date 日期参数，格式：yyyy-MM-dd，可选参数，默认为当天
//     */
//    @Preauthorize(value = "list", valueDesc = "列表查询")
//    @GetMapping("/dataReportStatistics/list")
//    public R<List<DataReportStatisticsPageRes>> loadList(@RequestParam(value = "date", required = false) String date) {
//        DataReportStatisticsListReq request = new DataReportStatisticsListReq();
//
//        // 如果没有传入日期参数，默认使用当天日期
//        if (date == null || date.trim().isEmpty()) {
//            date = LocalDate.now().format(DateConstants.DATE_FORMATTER);
//        }
//        request.setStatDateStr(date);
//
//        return R.success(basicService.selectBasicList(request));
//    }

    /**
     * 数据简报统计 根据日期查询单条记录
     * @param date 日期参数，格式：yyyyMMdd，可选参数，默认为当天
     */
    @Preauthorize(value = "selectByDate", valueDesc = "根据日期查询")
    @GetMapping("/dataReportStatistics/selectByDate")
    public R<DataReportStatisticsPageRes> selectByDate(@RequestParam(value = "date", required = false) Integer date) {
        // 如果没有传入日期参数，默认使用当天日期
        if (date == null) {
            date = Integer.parseInt(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        }
        return R.success(basicService.selectByDate(date));
    }

    /**
     * 用户增长趋势接口
     * @param queryType 查询类型：LAST_7_DAYS(近七天), THIS_MONTH(本月), LAST_MONTH(上月), CUSTOM(自定义)，默认为LAST_7_DAYS
     * @param startDate 自定义开始日期，格式：yyyyMMdd（当queryType为CUSTOM时必填）
     * @param endDate 自定义结束日期，格式：yyyyMMdd（当queryType为CUSTOM时必填）
     */
    @Preauthorize(value = "getUserGrowthTrend", valueDesc = "用户增长趋势")
    @GetMapping("/dataReportStatistics/userGrowthTrend")
    public R<UserGrowthTrendRes> getUserGrowthTrend(
            @RequestParam(value = "queryType", required = false, defaultValue = "LAST_7_DAYS") String queryType,
            @RequestParam(value = "startDate", required = false) Integer startDate,
            @RequestParam(value = "endDate", required = false) Integer endDate) {

        UserGrowthTrendReq request = new UserGrowthTrendReq();
        request.setQueryType(queryType);
        request.setStartDate(startDate);
        request.setEndDate(endDate);

        return R.success(basicService.getUserGrowthTrend(request));
    }

    /**
     * 任务增长趋势接口
     * @param queryType 查询类型：LAST_7_DAYS(近七天), THIS_MONTH(本月), LAST_MONTH(上月), CUSTOM(自定义)，默认为LAST_7_DAYS
     * @param startDate 自定义开始日期，格式：yyyyMMdd（当queryType为CUSTOM时必填）
     * @param endDate 自定义结束日期，格式：yyyyMMdd（当queryType为CUSTOM时必填）
     */
    @Preauthorize(value = "getTaskGrowthTrend", valueDesc = "任务增长趋势")
    @GetMapping("/dataReportStatistics/taskGrowthTrend")
    public R<TaskGrowthTrendRes> getTaskGrowthTrend(
            @RequestParam(value = "queryType", required = false, defaultValue = "LAST_7_DAYS") String queryType,
            @RequestParam(value = "startDate", required = false) Integer startDate,
            @RequestParam(value = "endDate", required = false) Integer endDate) {

        TaskGrowthTrendReq request = new TaskGrowthTrendReq();
        request.setQueryType(queryType);
        request.setStartDate(startDate);
        request.setEndDate(endDate);

        return R.success(basicService.getTaskGrowthTrend(request));
    }


    /**
     * 资金增长趋势接口
     * @param queryType 查询类型：LAST_7_DAYS(近七天), THIS_MONTH(本月), LAST_MONTH(上月), CUSTOM(自定义)，默认为LAST_7_DAYS
     * @param startDate 自定义开始日期，格式：yyyyMMdd（当queryType为CUSTOM时必填）
     * @param endDate 自定义结束日期，格式：yyyyMMdd（当queryType为CUSTOM时必填）
     */
    @Preauthorize(value = "getFundGrowthTrend", valueDesc = "资金增长趋势")
    @GetMapping("/dataReportStatistics/fundGrowthTrend")
    public R<FundGrowthTrendRes> getFundGrowthTrend(
            @RequestParam(value = "queryType", required = false, defaultValue = "LAST_7_DAYS") String queryType,
            @RequestParam(value = "startDate", required = false) Integer startDate,
            @RequestParam(value = "endDate", required = false) Integer endDate) {

        FundGrowthTrendReq request = new FundGrowthTrendReq();
        request.setQueryType(queryType);
        request.setStartDate(startDate);
        request.setEndDate(endDate);

        return R.success(basicService.getFundGrowthTrend(request));
    }

    /**
     * 数据简报统计 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/dataReportStatistics/selectById")
    public R<DataReportStatisticsDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 数据简报统计 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/dataReportStatistics/insert")
    public R<Boolean> insert(@Validated @RequestBody DataReportStatisticsSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 数据简报统计 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/dataReportStatistics/update")
    public R<Boolean> update(@Validated @RequestBody DataReportStatisticsUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 待办事项统计接口
     * 统计乙方用户身份待审核个数，以及提现待处理笔数
     */
    @Preauthorize(value = "getTodoStatistics", valueDesc = "待办事项统计")
    @GetMapping("/dataReportStatistics/todoStatistics")
    public R<TodoStatisticsRes> getTodoStatistics() {
        return R.success(basicService.getTodoStatistics());
    }

    /**
     * 财务统计接口
     * 统计平台账户余额、用户余额、奖励金额、充值提现等财务数据
     */
    @Preauthorize(value = "getFinanceStatistics", valueDesc = "财务统计")
    @GetMapping("/dataReportStatistics/financeStatistics")
    public R<FinanceStatisticsRes> getFinanceStatistics() {
        return R.success(basicService.getFinanceStatistics());
    }
}
