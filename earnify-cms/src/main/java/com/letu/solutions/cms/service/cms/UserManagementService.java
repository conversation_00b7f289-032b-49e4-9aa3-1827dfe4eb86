package com.letu.solutions.cms.service.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.cms.request.cms.ClientUserManagementReq;
import com.letu.solutions.model.cms.request.cms.CreateClientUserReq;
import com.letu.solutions.model.cms.request.cms.WorkerUserManagementReq;
import com.letu.solutions.model.cms.response.cms.ClientUserManagementRes;
import com.letu.solutions.model.cms.response.cms.WorkerUserManagementRes;


/**
 * 用户管理服务接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface UserManagementService {

    /**
     * 分页查询甲方用户管理列表
     * @param request 查询请求参数
     * @return 分页结果
     */
    Page<ClientUserManagementRes> getClientUserManagementList(ClientUserManagementReq request);

    /**
     * 创建甲方用户
     * @param request 创建用户请求参数
     * @return 创建结果，返回用户ID
     */
    Long createClientUser(CreateClientUserReq request);

    /**
     * 导出甲方用户管理数据
     * @param request 查询请求参数
     * @return 导出文件路径
     */
    String exportExcel(ClientUserManagementReq request);

    /**
     * 分页查询乙方用户管理列表
     * @param request 查询请求参数
     * @return 分页结果
     */
    Page<WorkerUserManagementRes> getWorkerUserManagementList(WorkerUserManagementReq request);

    /**
     * 导出乙方用户管理数据
     * @param request 查询请求参数
     * @return 导出文件路径
     */
    String exportWorkerUserExcel(WorkerUserManagementReq request);
}
