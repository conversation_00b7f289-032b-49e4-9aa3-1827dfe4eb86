package com.letu.solutions.cms.controller.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.cms.UserManagementService;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.cms.request.cms.ClientUserManagementReq;
import com.letu.solutions.model.cms.request.cms.CreateClientUserReq;
import com.letu.solutions.model.cms.request.cms.WorkerUserManagementReq;
import com.letu.solutions.model.cms.response.cms.ClientUserManagementRes;
import com.letu.solutions.model.cms.response.cms.WorkerUserManagementRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台/用户管理
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@Preauthorize(value = "user:user", valueDesc = "用户:用户")
public class UserManagementController {

    private final UserManagementService clientUserManagementService;

    /**
     * 甲方用户管理列表查询
     * 支持按用户名称、ID、邮箱、手机号、创建时间范围等条件查询
     * 
     * @param request 查询请求参数
     * @return 分页查询结果
     */
    @Preauthorize(value = "getClientUserManagementList", valueDesc = "甲方用户管理列表查询")
    @GetMapping("/clientUserManagement/list")
    public R<Page<ClientUserManagementRes>> getClientUserManagementList(ClientUserManagementReq request) {
           
            Page<ClientUserManagementRes> result = clientUserManagementService.getClientUserManagementList(request);
            return R.success(result);
    }

    /**
     * 创建甲方用户
     * 支持创建用户基本信息和社交媒体绑定信息
     *
     * @param request 创建用户请求参数
     * @return 创建结果，返回用户ID
     */
    @Preauthorize(value = "createClientUser", valueDesc = "创建甲方用户")
    @PostMapping("/clientUser/create")
    public R<Long> createClientUser(@Validated @RequestBody CreateClientUserReq request) {

            Long userId = clientUserManagementService.createClientUser(request);
            return R.success(userId);
    }

    /**
     * 甲方用户管理数据 Excel导出
     */
    @Preauthorize(value = "exportClientUserManagementList", valueDesc = "甲方用户管理数据导出")
    @GetMapping("/clientUserManagement/exportExcel")
    public R<String> exportExcel(ClientUserManagementReq request) {
        return R.success(clientUserManagementService.exportExcel(request));
    }

    /**
     * 乙方用户管理列表查询
     * 支持按用户名称、ID、邮箱、手机号、身份审核状态、创建时间范围等条件查询
     *
     * @param request 查询请求参数
     * @return 分页查询结果
     */
    @Preauthorize(value = "getWorkerUserManagementList", valueDesc = "乙方用户管理列表查询")
    @GetMapping("/workerUserManagement/list")
    public R<Page<WorkerUserManagementRes>> getWorkerUserManagementList(WorkerUserManagementReq request) {
        return R.success(clientUserManagementService.getWorkerUserManagementList(request));
    }

    /**
     * 乙方用户管理数据 Excel导出
     */
    @Preauthorize(value = "exportWorkerUserManagementList", valueDesc = "乙方用户管理数据导出")
    @GetMapping("/workerUserManagement/exportExcel")
    public R<String> exportWorkerUserExcel(WorkerUserManagementReq request) {
        return R.success(clientUserManagementService.exportWorkerUserExcel(request));
    }
}
