package com.letu.solutions.cms.controller.sys;

import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.sys.SysMenuService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import com.letu.solutions.model.cms.request.sys.SysMenuSaveReq;
import com.letu.solutions.model.cms.request.sys.SysMenuUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysMenuDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统/菜单
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "sys:menu", valueDesc = "系统:菜单")
public class SysMenuController {
    private final SysMenuService basicService;

    /**
     * 菜单保存
     */
    @Preauthorize(value = "save", valueDesc = "保存")
    @PostMapping("/sys/menu/insert")
    public R<Boolean> saveRole(@Validated @RequestBody SysMenuSaveReq sysMenuSaveReq) {
        return R.success(basicService.saveMenu(sysMenuSaveReq));
    }

    /**
     * 菜单修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/sys/menu/update")
    public R<Boolean> updateRole(@Validated @RequestBody SysMenuUpdateReq sysMenuUpdateReq) {
        return R.success(basicService.updateMenu(sysMenuUpdateReq));
    }

    /**
     * 查询菜单详情
     */
    @Preauthorize(value = "detail", valueDesc = "详情")
    @GetMapping("/sys/menu/detail")
    public R<SysMenuDetail> detail(@RequestParam(value = "id") Long id) {
        return R.success(basicService.detail(id));
    }

    /**
     * 查询菜单树
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/sys/menu/list")
    public R<List<SysMenuDetail>> getMenuList() {
        return R.success(basicService.getMenuList());
    }

    /**
     * 菜单删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/sys/menu/delete")
    public R<Boolean> deleteRole(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.basicDelete(request.getId().toString(), extendData));
    }
}
