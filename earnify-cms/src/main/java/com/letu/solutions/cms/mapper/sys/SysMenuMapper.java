package com.letu.solutions.cms.mapper.sys;

import com.letu.solutions.model.cms.response.sys.SysMenuDetail;
import com.letu.solutions.model.cms.response.sys.SysMenuVo;
import com.letu.solutions.model.entity.sys.SysMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统权限（菜单） Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Mapper

public interface SysMenuMapper extends BaseMapper<SysMenu> {
    List<SysMenuDetail> detailList(@Param("parentId") Long parentId);
    List<SysMenuVo> allVoList();
    List<SysMenuVo> userVoList(@Param("userId") Long userId);
}
