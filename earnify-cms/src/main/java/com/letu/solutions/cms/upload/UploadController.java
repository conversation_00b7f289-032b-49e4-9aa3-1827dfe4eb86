package com.letu.solutions.cms.upload;

import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.enums.OssEnum;
import com.letu.solutions.util.util.upload.UploadUtil;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * 文件上传相关
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Preauthorize(value = "upload", valueDesc = "文件上传")
@Slf4j
@Validated
public class UploadController {
    private final UploadUtil uploadUtil;


    /**
     * 图片上传
     *
     * @param file 文件
     */
    @Preauthorize(value = "imageUpload", valueDesc = "图片上传")
    @PostMapping("/upload/image")
    public R<String> imageUpload(@NotNull(message = "文件不能为空") @RequestParam(name = "file") MultipartFile file,
                                 @RequestParam(value = "ossEnum", required = false, defaultValue = "BUSINESS") OssEnum ossEnum) throws Exception {
        return R.success(uploadUtil.uploadImage(file, ossEnum.getPath()));
    }

    /**
     * apk上传
     *
     * @param files      文件
     * @param needRename 是否需要重命名文件[true:是;false:否]
     */
    @Preauthorize(value = "apkUpload", valueDesc = "apk上传")
    @PostMapping("/upload/apk")
    public R<String> apkUpload(@NotNull(message = "文件不能为空") @RequestParam(name = "file") MultipartFile files, @NotNull(message = "上传文件目录不存在") @RequestParam("ossEnum") OssEnum ossEnum, boolean needRename) throws Exception {
        return R.success(uploadUtil.uploadApk(files, ossEnum.getPath(), needRename));
    }

    /**
     * excel文件上传
     *
     * @param file 文件
     */
    @Preauthorize(value = "excelUpload", valueDesc = "excel文件上传")
    @PostMapping("/upload/excel")
    public R<String> excelUpload(@NotNull(message = "文件不能为空") @RequestParam(name = "file") MultipartFile file, @NotNull(message = "上传文件目录不存在") @RequestParam("ossEnum") OssEnum ossEnum) throws Exception {
        return R.success(uploadUtil.uploadExcel(file, ossEnum.getPath()));
    }


    /**
     * 视频上传
     *
     * @param file 文件
     */
    @Preauthorize(value = "videoUpload", valueDesc = "视频上传")
    @PostMapping("/upload/video")
    public R<String> videoUpload(@NotNull(message = "文件不能为空") @RequestParam(name = "file") MultipartFile file, @NotNull(message = "上传文件目录不存在") @RequestParam("ossEnum") OssEnum ossEnum) throws Exception {
        return R.success(uploadUtil.uploadVideo(file, ossEnum.getPath()));
    }

    /**
     * 批量图片上传
     * @param files 文件数组
     * @param ossEnum 上传目录
     */
    @Preauthorize(value = "batchUpload", valueDesc = "批量图片上传")
    @PostMapping("/upload/batch")
    public R<java.util.List<String>> batchUpload(@NotNull(message = "文件不能为空") @RequestParam(name = "files") MultipartFile[] files,
                                                @RequestParam(value = "ossEnum", required = false, defaultValue = "BUSINESS") OssEnum ossEnum) throws Exception {
        var result = new ArrayList<String>();
        Arrays.stream(files).forEach(file -> {
            try {
                result.add(uploadUtil.uploadImage(file, ossEnum.getPath()));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });


        return R.success(result);
    }
}
