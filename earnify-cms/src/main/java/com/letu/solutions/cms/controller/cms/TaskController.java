package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.TaskService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import com.letu.solutions.model.cms.request.cms.TaskListReq;
import com.letu.solutions.model.cms.request.cms.TaskSaveReq;
import com.letu.solutions.model.cms.response.cms.TaskPageRes;
import com.letu.solutions.model.cms.response.cms.TaskDetailRes;

/**
 * 任务/发布任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:task", valueDesc = "发布:任务")
public class TaskController{
    private final TaskService basicService;
    /**
    * 任务 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/task/page")
    public R<Page<TaskPageRes>> loadPage(TaskListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 导出
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/task/list")
    public R<String> loadList(TaskListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 任务详情
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/task/selectById")
    public R<TaskDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 任务 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/task/insert")
    public R<Boolean> insert(@Validated @RequestBody TaskSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }


    /**
     * 任务撤销
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/task/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }
}
