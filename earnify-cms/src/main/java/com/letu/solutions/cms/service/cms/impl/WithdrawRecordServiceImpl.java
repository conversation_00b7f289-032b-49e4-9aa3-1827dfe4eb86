package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.cms.service.cms.*;
import com.letu.solutions.cms.validator.FinanceConfigValidator;
import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;
import com.letu.solutions.model.cms.response.user.UserDetailRes;
import com.letu.solutions.model.dto.PlatformTransactionBillDto;
import com.letu.solutions.model.dto.UserTransactionBillDto;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import com.letu.solutions.cms.mapper.cms.WithdrawRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.ActionTypeEnum;
import com.letu.solutions.model.enums.cms.WithdrawStatusEnum;
import com.letu.solutions.util.constants.CurrencyConstants;
import com.letu.solutions.util.util.TimeUtil;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.RoundingMode;
import java.util.List;
import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordListReq;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordSaveReq;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordUpdateReq;
import com.letu.solutions.model.cms.response.cms.WithdrawRecordPageRes;
import com.letu.solutions.model.cms.response.cms.WithdrawRecordDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import com.letu.solutions.cms.service.user.UserService;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceUpdateReq;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import org.springframework.transaction.annotation.Transactional;
import com.letu.solutions.core.configuration.AccountConfiguration;
import org.springframework.transaction.support.TransactionTemplate;
import java.math.BigDecimal;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.WithdrawRecordExcel;
import com.letu.solutions.cms.validator.PlatformAddressValidator;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressWithConfigRes;

/**
 * 提现记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WithdrawRecordServiceImpl extends ServiceImpl<WithdrawRecordMapper, WithdrawRecord> implements WithdrawRecordService {
    private final UserService userService;
    private final PlatformTransactionBillService platformTransactionBillService;
    private final UserAccountBalanceService userAccountBalanceService;
    private final UserTransactionBillService userTransactionBillService;
    private final AccountConfiguration accountConfiguration;
    private final FinanceConfigService financeConfigService;
    private final TransactionTemplate transactionTemplate;
    private final FinanceConfigValidator financeConfigValidator;
    private final WithdrawRecordMapper withdrawRecordMapper;

    @Override
    public Page<WithdrawRecordPageRes> selectBasicPage(Page<WithdrawRecord> page, WithdrawRecordListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<WithdrawRecord> queryWrapper = Wrappers.<WithdrawRecord>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),WithdrawRecord::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getUserId()),WithdrawRecord::getUserId, request.getUserId())
                .like(ObjectUtil.isNotEmpty(request.getUsername()),WithdrawRecord::getUsername, request.getUsername())
                .eq(ObjectUtil.isNotEmpty(request.getWithdrawStatus()),WithdrawRecord::getWithdrawStatus, request.getWithdrawStatus())
                .eq(ObjectUtil.isNotEmpty(request.getAccountType()),WithdrawRecord::getAccountType, request.getAccountType())
                .eq(ObjectUtil.isNotEmpty(request.getActionType()),WithdrawRecord::getActionType, request.getActionType())
                .eq(ObjectUtil.isNotEmpty(request.getNetwork()),WithdrawRecord::getNetwork, request.getNetwork())
                .ge(ObjectUtil.isNotEmpty(request.getBeginTime()), WithdrawRecord::getDay, request.getBeginTime())
                .le(ObjectUtil.isNotEmpty(request.getEndDate()), WithdrawRecord::getDay, request.getEndDate())
                .ge(ObjectUtil.isNotEmpty(request.getBeginDate()), WithdrawRecord::getFinishDay, request.getBeginDate())
                .le(ObjectUtil.isNotEmpty(request.getEndDate()), WithdrawRecord::getFinishDay, request.getEndDate());

        Page<WithdrawRecord> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, WithdrawRecordPageRes.class);
        }



    @Override
    public List<WithdrawRecordPageRes> selectBasicList(WithdrawRecordListReq request) {
        List<WithdrawRecord> basicList = baseMapper.selectList(Wrappers.lambdaQuery());
        return BeanUtil.copyToList(basicList, WithdrawRecordPageRes.class);
    }

    @Override
    public WithdrawRecordDetailRes selectByIdBasic(Long id) {
        WithdrawRecord record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, WithdrawRecordDetailRes.class);
    }

    @Override
    public boolean saveBasic(WithdrawRecordSaveReq record, ExtendData extendData) {
        // 统计用户当日提现次数
        String today = DateUtil.today();
        int withdrawTimesToday = withdrawRecordMapper.countUserWithdrawToday(record.getUserId(), record.getNetwork(), today);
        financeConfigValidator.validateWithdraw(record.getNetwork(), record.getAmount(), withdrawTimesToday);
        FinanceConfigDetailRes financeConfigDetailRes = financeConfigService.selectByNetwork(record.getNetwork());
        Assert.isTrue(ObjectUtil.isNotNull(financeConfigDetailRes), "当前链不存在");
        // 校验提现地址
        List<String> addressList = financeConfigDetailRes.getAddressList();
        PlatformAddressValidator.validateAddressInConfig(record.getFromAddress(), addressList);
        // 只允许甲方用户提现
        UserDetailRes user = userService.selectByIdBasic(record.getUserId());
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户不存在");
        Assert.isTrue(user != null && AccountTypeEnum.client == user.getAccountRole(), "只有甲方用户可以提现");

        return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            // 计算手续费
            BigDecimal percent = financeConfigDetailRes.getFeeRate()
                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            BigDecimal fee = record.getAmount().multiply(percent).add(financeConfigDetailRes.getFixedFee());
            // 1. 冻结用户账户余额（可用资金减少，冻结资金增加）
            freezeUserAccountBalance(record, extendData);
            // 2. 生成用户流水记录
            writeUserWithdrawTransactionBill(record, user, extendData);
            // 3. 保存提现记录
            WithdrawRecord saveRecord = BeanUtil.copyProperties(record, WithdrawRecord.class);
            saveRecord.setUsername(user.getNickName());
            saveRecord.setWithdrawStatus(WithdrawStatusEnum.unprocessed);
            saveRecord.setAccountType(AccountTypeEnum.client);
            saveRecord.setActionType(ActionTypeEnum.platform);
            saveRecord.setCurrency(CurrencyConstants.COIN_USDT);
            saveRecord.setFeeRate(financeConfigDetailRes.getFeeRate());
            saveRecord.setFixedFee(financeConfigDetailRes.getFixedFee());
            saveRecord.setDay(TimeUtil.today(new Date()));
            saveRecord.setFee(fee);
            return baseMapper.insert(saveRecord) > 0;
        }));
    }

    /**
     * 提现申请时的资金处理：可用资金减少，冻结资金增加
     */
    private void freezeUserAccountBalance(WithdrawRecordSaveReq record, ExtendData extendData) {
        var userAccount = userAccountBalanceService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, record.getUserId())
                .eq(UserAccountBalance::getCurrency, CurrencyConstants.COIN_USDT)
        );
        Assert.isTrue(userAccount != null, "用户账户不存在");
        Assert.isTrue(userAccount.getAvailableAmount().compareTo(record.getAmount()) >= 0, "账户余额不足");

        var updateReq = new UserAccountBalanceUpdateReq();
        updateReq.setId(userAccount.getId());
        updateReq.setUserId(userAccount.getUserId());
        // 可用资金减少
        updateReq.setAvailableAmount(userAccount.getAvailableAmount().subtract(record.getAmount()));
        // 冻结资金增加
        updateReq.setFrozenAmount(userAccount.getFrozenAmount().add(record.getAmount()));
        updateReq.setCurrency(CurrencyConstants.COIN_USDT);
        userAccountBalanceService.updateBasic(updateReq, extendData);
    }

    /**
     * 生成用户提现申请流水（资金冻结）
     */
    private void writeUserWithdrawTransactionBill(WithdrawRecordSaveReq record, UserDetailRes user, ExtendData extendData) {
        var userAccount = userAccountBalanceService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, record.getUserId())
                .eq(UserAccountBalance::getCurrency, CurrencyConstants.COIN_USDT)
        );

        // 记录可用资金的变化（减少）
        BigDecimal availableBefore = userAccount.getAvailableAmount().add(record.getAmount());
        BigDecimal availableAfter = userAccount.getAvailableAmount();

        // 记录冻结资金的变化（增加）
        BigDecimal frozenBefore = userAccount.getFrozenAmount();
        BigDecimal frozenAfter = userAccount.getFrozenAmount().add(record.getAmount());

        UserTransactionBillDto userBill = UserTransactionBillDto.builder()
            .userId(record.getUserId())
            .userName(user.getNickName())
            .accountType(AccountTypeEnum.client)
            .fundType(FundTypeEnum.withdraw_freeze)  // 使用冻结类型
            .side(FundSideTypeEnum.out)  // 可用资金减少
            .amount(record.getAmount())
            .currency(CurrencyConstants.COIN_USDT)
            .before(availableBefore)   // 可用资金变化前
            .after(availableAfter)     // 可用资金变化后
            .frozen(record.getAmount()) // 冻结资金增加（正数表示增加）
            .remark("提现申请-资金冻结")
            .build();
        userTransactionBillService.createUserTransactionBill(userBill, extendData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBasic(WithdrawRecordUpdateReq record, ExtendData extendData) {
        WithdrawRecord dbRecord = baseMapper.selectById(record.getId());
        Assert.isTrue(dbRecord != null, "提现记录不存在");
        Assert.isTrue(dbRecord.getWithdrawStatus() != WithdrawStatusEnum.completed, "已打款状态不能再修改");
        Assert.isTrue(record.getWithdrawStatus() == WithdrawStatusEnum.completed || record.getWithdrawStatus() == WithdrawStatusEnum.rejected, "只允许审核通过或不通过");
        if (record.getWithdrawStatus() == WithdrawStatusEnum.rejected) {
            Assert.isTrue(ObjectUtil.isNotEmpty(record.getRemark()), "不通过原因必填");
        }
        WithdrawRecord updateRecord = BeanUtil.copyProperties(record, WithdrawRecord.class);
        // 审核通过时设置完成时间
        if (record.getWithdrawStatus() == WithdrawStatusEnum.completed) {
            updateRecord.setFinishTime(new Date());
            updateRecord.setFinishDay(TimeUtil.today(new Date()));
            updateRecord.setOperator(extendData.getAuthentication().getUserName());
            // 审核通过：减少用户冻结资金，扣减平台账户
            unfreezeUserAccountAndDeductPlatform(dbRecord, extendData);
        }

        // 审核失败时恢复用户资金
        if (record.getWithdrawStatus() == WithdrawStatusEnum.rejected) {
            updateRecord.setFinishDay(TimeUtil.today(new Date()));
            updateRecord.setOperator(extendData.getAuthentication().getUserName());
            // 审核失败：减少冻结资金，恢复可用资金
            restoreUserAccountBalance(dbRecord, extendData);
        }
        return baseMapper.updateById(updateRecord) > 0;
    }

    /**
     * 审核通过：解冻用户资金并扣减平台账户
     */
    private void unfreezeUserAccountAndDeductPlatform(WithdrawRecord withdrawRecord, ExtendData extendData) {
        // 1. 解冻用户资金（减少冻结资金）
        unfreezeUserFunds(withdrawRecord, extendData);

        // 2. 扣减平台账户并生成流水
        deductPlatformAccountAndWriteBill(withdrawRecord, extendData);
    }

    /**
     * 解冻用户资金（减少冻结资金）
     */
    private void unfreezeUserFunds(WithdrawRecord withdrawRecord, ExtendData extendData) {
        var userAccount = userAccountBalanceService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, withdrawRecord.getUserId())
                .eq(UserAccountBalance::getCurrency, CurrencyConstants.COIN_USDT)
        );
        Assert.isTrue(userAccount != null, "用户账户不存在");
        Assert.isTrue(userAccount.getFrozenAmount().compareTo(withdrawRecord.getAmount()) >= 0, "冻结资金不足");

        var updateReq = new UserAccountBalanceUpdateReq();
        updateReq.setId(userAccount.getId());
        updateReq.setUserId(userAccount.getUserId());
        updateReq.setAvailableAmount(userAccount.getAvailableAmount()); // 可用资金不变
        // 冻结资金减少
        updateReq.setFrozenAmount(userAccount.getFrozenAmount().subtract(withdrawRecord.getAmount()));
        updateReq.setCurrency(CurrencyConstants.COIN_USDT);
        userAccountBalanceService.updateBasic(updateReq, extendData);

        // 生成提现成功流水
        writeUserWithdrawSuccessTransactionBill(withdrawRecord, extendData);
    }

    /**
     * 审核失败：恢复用户资金（减少冻结资金，增加可用资金）
     * 注意：这个逻辑是正确的，冻结资金减少多少，可用资金就增加多少，保证总资金不变
     */
    private void restoreUserAccountBalance(WithdrawRecord withdrawRecord, ExtendData extendData) {
        var userAccount = userAccountBalanceService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, withdrawRecord.getUserId())
                .eq(UserAccountBalance::getCurrency, CurrencyConstants.COIN_USDT)
        );
        Assert.isTrue(userAccount != null, "用户账户不存在");
        Assert.isTrue(userAccount.getFrozenAmount().compareTo(withdrawRecord.getAmount()) >= 0, "冻结资金不足");

        // 记录操作前的金额，用于流水记录
        BigDecimal availableBefore = userAccount.getAvailableAmount();
        BigDecimal frozenBefore = userAccount.getFrozenAmount();

        var updateReq = new UserAccountBalanceUpdateReq();
        updateReq.setId(userAccount.getId());
        updateReq.setUserId(userAccount.getUserId());
        // 可用资金增加（恢复）
        updateReq.setAvailableAmount(userAccount.getAvailableAmount().add(withdrawRecord.getAmount()));
        // 冻结资金减少
        updateReq.setFrozenAmount(userAccount.getFrozenAmount().subtract(withdrawRecord.getAmount()));
        updateReq.setCurrency(CurrencyConstants.COIN_USDT);
        userAccountBalanceService.updateBasic(updateReq, extendData);

        // 生成恢复资金流水
        writeUserRestoreTransactionBill(withdrawRecord, availableBefore, frozenBefore, extendData);
    }

    /**
     * 审核通过：平台账户扣减余额并写平台流水
     */
    private void deductPlatformAccountAndWriteBill(WithdrawRecord withdraw, ExtendData extendData) {
        Long platformUserId = accountConfiguration.getPlatformUserId();
        String currency = withdraw.getCurrency();
        var platformAccount = userAccountBalanceService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, platformUserId)
                .eq(UserAccountBalance::getCurrency, currency)
        );
        Assert.isTrue(platformAccount != null, "平台账户不存在");
        Assert.isTrue(platformAccount.getAvailableAmount().compareTo(withdraw.getAmount()) >= 0, "平台账户余额不足");
        // 扣减余额
        var updateReq = new UserAccountBalanceUpdateReq();
        updateReq.setId(platformAccount.getId());
        updateReq.setUserId(platformUserId);
        updateReq.setAvailableAmount(platformAccount.getAvailableAmount().subtract(withdraw.getAmount()));
        updateReq.setFrozenAmount(platformAccount.getFrozenAmount());
        updateReq.setCurrency(currency);
        userAccountBalanceService.updateBasic(updateReq, extendData);
        // 写平台流水
        PlatformTransactionBillDto platformBill = PlatformTransactionBillDto.builder()
            .userId(withdraw.getUserId())
            .userName(withdraw.getUsername())
            .accountType(AccountTypeEnum.client)
            .fundType(FundTypeEnum.withdraw)
            .side(FundSideTypeEnum.out)
            .amount(withdraw.getAmount())
            .currency(currency)
            .fee(withdraw.getFee())
            .before(platformAccount.getAvailableAmount())
            .after(platformAccount.getAvailableAmount().subtract(withdraw.getAmount()))
            .fundId(withdraw.getId())
            .remark("提现打款")
            .platformUserId(platformUserId)
            .build();
        platformTransactionBillService.createPlatformTransactionBill(platformBill, extendData);
    }



    /**
     * 生成用户提现成功流水（审核通过时）
     */
    private void writeUserWithdrawSuccessTransactionBill(WithdrawRecord withdrawRecord, ExtendData extendData) {
        var userAccount = userAccountBalanceService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, withdrawRecord.getUserId())
                .eq(UserAccountBalance::getCurrency, CurrencyConstants.COIN_USDT)
        );

        // 审核通过时：可用资金不变，冻结资金减少
        BigDecimal availableBalance = userAccount.getAvailableAmount(); // 可用资金不变

        UserTransactionBillDto userBill = UserTransactionBillDto.builder()
            .userId(withdrawRecord.getUserId())
            .userName(withdrawRecord.getUsername())
            .accountType(AccountTypeEnum.client)
            .fundType(FundTypeEnum.withdraw)  // 直接使用提现类型
            .side(FundSideTypeEnum.out)  // 资金流出（提现成功）
            .amount(withdrawRecord.getAmount())
            .currency(withdrawRecord.getCurrency())
            .before(availableBalance)  // 可用资金不变
            .after(availableBalance)   // 可用资金不变
            .frozen(withdrawRecord.getAmount())  // 冻结资金减少的金额（正数）
            .remark("提现成功")
            .build();
        userTransactionBillService.createUserTransactionBill(userBill, extendData);
    }

    /**
     * 生成用户资金恢复流水（审核失败时）
     */
    private void writeUserRestoreTransactionBill(WithdrawRecord withdrawRecord,
                                                BigDecimal availableBefore,
                                                BigDecimal frozenBefore,
                                                ExtendData extendData) {

        // 计算操作后的金额
        BigDecimal availableAfter = availableBefore.add(withdrawRecord.getAmount());
        BigDecimal frozenAfter = frozenBefore.subtract(withdrawRecord.getAmount());

        UserTransactionBillDto userBill = UserTransactionBillDto.builder()
            .userId(withdrawRecord.getUserId())
            .userName(withdrawRecord.getUsername())
            .accountType(AccountTypeEnum.client)
            .fundType(FundTypeEnum.withdraw_rejected)  // 使用审核驳回类型
            .side(FundSideTypeEnum.in)  // 可用资金增加
            .amount(withdrawRecord.getAmount())
            .currency(withdrawRecord.getCurrency())
            .before(availableBefore)   // 可用资金恢复前
            .after(availableAfter)     // 可用资金恢复后
            .frozen(withdrawRecord.getAmount()) // 冻结资金减少的金额（正数）
            .remark("提现审核失败-资金恢复")
            .build();
        userTransactionBillService.createUserTransactionBill(userBill, extendData);
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public String exportExcel(WithdrawRecordListReq request) {
        List<WithdrawRecordPageRes> data = selectBasicList(request);
        // 转为Excel DTO
        List<WithdrawRecordExcel> excelList = BeanUtil.copyToList(data, WithdrawRecordExcel.class);
        return ExcelUtil.getExcel("提现记录导出", WithdrawRecordExcel.class, excelList, false);
    }
}