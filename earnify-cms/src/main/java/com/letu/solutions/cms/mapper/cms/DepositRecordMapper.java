package com.letu.solutions.cms.mapper.cms;

import com.letu.solutions.model.entity.cms.DepositRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 用户充值记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper
public interface DepositRecordMapper extends BaseMapper<DepositRecord> {

    /**
     * 统计充值成功金额
     * @return 充值成功金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM deposit_record ")
    BigDecimal sumDepositAmountByDate();

    /**
     * 批量获取用户充值金额
     * @param userIds 用户ID列表
     * @return 用户充值金额映射
     */
    @Select("""
            <script>
            SELECT
                CAST(user_id AS SIGNED) as userId,
                COALESCE(SUM(amount), 0) as totalAmount
            FROM deposit_record
            WHERE user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            GROUP BY user_id
            </script>
            """)
    List<Map<String, Object>> getDepositAmountByUserIds(@Param("userIds") List<Long> userIds);
}
