package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.UserSocial;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserSocialListReq;
import com.letu.solutions.model.cms.request.cms.UserSocialSaveReq;
import com.letu.solutions.model.cms.request.cms.UserSocialUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserSocialPageRes;
import com.letu.solutions.model.cms.response.cms.UserSocialDetailRes;

/**
 * 社交媒体绑定信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface UserSocialService {
    Page<UserSocialPageRes> selectBasicPage(Page<UserSocial> page, UserSocialListReq request);

    List<UserSocialPageRes> selectBasicList(UserSocialListReq request);

    UserSocialDetailRes selectByIdBasic(Long id);

    boolean saveBasic(UserSocialSaveReq record, ExtendData extendData);

    boolean updateBasic(UserSocialUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);
}
