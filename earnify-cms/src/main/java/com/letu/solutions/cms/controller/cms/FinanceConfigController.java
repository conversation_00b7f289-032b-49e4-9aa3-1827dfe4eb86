package com.letu.solutions.cms.controller.cms;

import com.letu.solutions.cms.service.cms.PlatformWalletAddressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.FinanceConfigService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.FinanceConfigListReq;
import com.letu.solutions.model.cms.request.cms.FinanceConfigSaveReq;
import com.letu.solutions.model.cms.request.cms.FinanceConfigUpdateReq;
import com.letu.solutions.model.cms.response.cms.FinanceConfigPageRes;
import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;

/**
 * 管理后台/财务管理/财务相关配置
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:finance_config", valueDesc = "财务相关配置")
public class FinanceConfigController{
    private final FinanceConfigService basicService;
    private final PlatformWalletAddressService walletAddressService;

    /**
    * 财务配置 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/financeConfig/page")
    public R<Page<FinanceConfigPageRes>> loadPage(@Validated FinanceConfigListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 财务配置 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/financeConfig/list")
    public R<List<FinanceConfigPageRes>> loadList(FinanceConfigListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 财务配置 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/financeConfig/selectById")
    public R<FinanceConfigDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 财务配置 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/financeConfig/insert")
    public R<Boolean> insert(@Validated @RequestBody FinanceConfigSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 财务配置 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/financeConfig/update")
    public R<Boolean> update(@Validated @RequestBody FinanceConfigUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 财务配置 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/financeConfig/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }

    /**
     * 财务配置 根据network查询
     */
    @Preauthorize(value = "selectByNetwork", valueDesc = "根据network查询")
    @GetMapping("/financeConfig/selectByNetwork")
    public R<FinanceConfigDetailRes> selectByNetwork(@RequestParam("network") String network) {
        FinanceConfigDetailRes res = basicService.selectByNetwork(network);
        return R.success(res);
    }

    /**
     * 根据协议网络查询钱包地址列表
     * 返回该网络下所有可用的钱包地址
     *
     * @param network 协议网络（如 ERC20, TRC20, BEP20）
     * @return 钱包地址列表
     */
    @Preauthorize(value = "getAddressesByNetwork", valueDesc = "根据网络查询钱包地址列表")
    @GetMapping("/financeConfig/getAddressesByNetwork")
    public R<List<String>> getAddressesByNetwork(@RequestParam("network") String network) {
        return R.success(walletAddressService.getAddressesByNetwork(network));
    }
}
