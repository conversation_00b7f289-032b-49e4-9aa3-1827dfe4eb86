package com.letu.solutions.cms.dto;

import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 乙方用户统计数据聚合类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkerUserStatistics implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户上级信息映射
     */
    private Map<Long, UserParentInfoDto> parentInfoMap;

    /**
     * 邀请统计信息映射
     */
    private Map<Long, InviteStatisticsDto> inviteStatisticsMap;

    /**
     * 任务统计信息映射
     */
    private Map<Long, UserTaskStatisticsDto> taskStatisticsMap;

    /**
     * 提现统计信息映射
     */
    private Map<Long, WithdrawStatistics> withdrawStatisticsMap;

    /**
     * 当前余额映射
     */
    private Map<Long, BigDecimal> currentBalanceMap;

    /**
     * KYC认证状态映射
     */
    private Map<Long, UserVerifyStatusEnum> verifyStatusMap;
}
