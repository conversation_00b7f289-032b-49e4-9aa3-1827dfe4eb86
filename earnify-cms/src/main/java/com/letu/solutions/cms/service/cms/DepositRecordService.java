package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.DepositRecord;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.DepositRecordListReq;
import com.letu.solutions.model.cms.request.cms.DepositRecordSaveReq;
import com.letu.solutions.model.cms.request.cms.DepositRecordUpdateReq;
import com.letu.solutions.model.cms.response.cms.DepositRecordPageRes;
import com.letu.solutions.model.cms.response.cms.DepositRecordDetailRes;

/**
 * 用户充值记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface DepositRecordService {
    Page<DepositRecordPageRes> selectBasicPage(Page<DepositRecord> page, DepositRecordListReq request);

    List<DepositRecordPageRes> selectBasicList(DepositRecordListReq request);

    DepositRecordDetailRes selectByIdBasic(Long id);

    boolean saveBasic(DepositRecordSaveReq record, ExtendData extendData);

    boolean updateBasic(DepositRecordUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    String exportExcel(DepositRecordListReq request);
}
