package com.letu.solutions.cms.service.sys;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.CmsLoginRequest;
import com.letu.solutions.model.cms.request.sys.CmsSmsRequest;
import com.letu.solutions.model.cms.response.sys.LoginResponse;
import com.letu.solutions.share.model.request.cms.ClientTokenReq;

public interface LoginService {
    void sms(CmsSmsRequest request, ExtendData extendData);

    LoginResponse detail(Long userId);

    void refresh(Long userId);

    String clientToken(ClientTokenReq request);

    LoginResponse login(CmsLoginRequest request,ExtendData extendData);


}
