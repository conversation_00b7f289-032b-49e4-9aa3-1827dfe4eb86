package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.PlatformTransactionBillService;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.cms.PlatformTransactionBillListReq;
import com.letu.solutions.model.cms.response.cms.PlatformTransactionBillPageRes;

/**
 * 管理后台/财务管理/平台账户流水对账
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:platform_transaction_bill", valueDesc = "系统:平台流水对账")
public class PlatformTransactionBillController{
    private final PlatformTransactionBillService basicService;
    /**
    * 平台流水对账 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/platformTransactionBill/page")
    public R<Page<PlatformTransactionBillPageRes>> loadPage(PlatformTransactionBillListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 平台流水对账 Excel导出
     */
    @Preauthorize(value = "list", valueDesc = "平台流水导出")
    @GetMapping("/platformTransactionBill/exportExcel")
    public R<String> exportExcel(PlatformTransactionBillListReq request) {
        return R.success(basicService.exportExcel(request));
    }
}
