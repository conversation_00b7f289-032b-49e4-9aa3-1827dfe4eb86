package com.letu.solutions.cms.controller.cms;

import com.letu.solutions.model.entity.cms.Product;
import com.letu.solutions.model.request.BaseRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.ProductService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.ProductListReq;
import com.letu.solutions.model.cms.request.cms.ProductSaveReq;
import com.letu.solutions.model.cms.request.cms.ProductUpdateReq;
import com.letu.solutions.model.cms.response.cms.ProductPageRes;
import com.letu.solutions.model.cms.response.cms.ProductDetailRes;

/**
 * 任务/产品表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:product", valueDesc = "配置:产品表")
public class ProductController{
    private final ProductService basicService;
    /**
    * 产品表 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/product/page")
    public R<Page<ProductPageRes>> loadPage(ProductListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }
    /**
     * 查询未上架和已上架产品分页查询
     */
    @Preauthorize(value = "notPage", valueDesc = "查询未上架产品分页查询")
    @GetMapping("/product/notPage")
    public R<Page<Product>> notPage(BaseRequest request) {
        return R.success(basicService.selectNotPage(request.getPage()));
    }

    /**
     * 产品表 导出
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/product/list")
    public R<String> loadList(ProductListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 产品表 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/product/selectById")
    public R<ProductDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 产品表 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/product/insert")
    public R<Boolean> insert(@Validated @RequestBody ProductSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 产品表 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/product/update")
    public R<Boolean> update(@Validated @RequestBody ProductUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

}
