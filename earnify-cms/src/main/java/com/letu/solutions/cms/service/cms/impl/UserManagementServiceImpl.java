package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.dto.TaskStatistics;
import com.letu.solutions.cms.dto.WithdrawStatistics;
import com.letu.solutions.cms.mapper.user.UserMapper;
import com.letu.solutions.cms.service.cms.UserManagementService;
import com.letu.solutions.model.cms.request.cms.ClientUserManagementReq;
import com.letu.solutions.model.cms.request.cms.CreateClientUserReq;
import com.letu.solutions.model.cms.request.cms.WorkerUserManagementReq;
import com.letu.solutions.model.cms.response.cms.ClientUserManagementRes;
import com.letu.solutions.model.cms.response.cms.WorkerUserManagementRes;
import com.letu.solutions.cms.dto.ClientUserManagementExcel;
import com.letu.solutions.cms.mapper.cms.ProductMapper;
import com.letu.solutions.cms.mapper.cms.TaskMapper;
import com.letu.solutions.cms.mapper.cms.DepositRecordMapper;
import com.letu.solutions.cms.mapper.cms.UserAccountBalanceMapper;
import com.letu.solutions.cms.mapper.cms.WithdrawRecordMapper;
import com.letu.solutions.cms.mapper.cms.UserSocialMapper;
import com.letu.solutions.cms.mapper.cms.InviteRecordMapper;
import com.letu.solutions.cms.mapper.cms.UserTaskMapper;
import com.letu.solutions.cms.mapper.cms.UserKycAuthMapper;
import com.letu.solutions.model.entity.cms.UserSocial;
import com.letu.solutions.model.entity.user.User;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.entity.cms.InviteRecord;
import com.letu.solutions.cms.dto.InviteStatisticsDto;
import com.letu.solutions.cms.dto.UserParentInfoDto;
import com.letu.solutions.cms.dto.UserTaskStatisticsDto;
import com.letu.solutions.cms.dto.WorkerUserStatistics;
import com.letu.solutions.cms.dto.WorkerUserManagementExcel;
import com.letu.solutions.model.enums.cms.SocialTypeEnum;
import com.letu.solutions.model.enums.cms.SocialBindStatusEnum;
import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import com.letu.solutions.util.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.support.TransactionTemplate;
import com.letu.solutions.cms.aspect.ExcelUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.letu.solutions.util.util.PageUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 甲方用户管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserManagementServiceImpl implements UserManagementService {

    private final UserMapper userMapper;
    private final ProductMapper productMapper;
    private final TaskMapper taskMapper;
    private final DepositRecordMapper depositRecordMapper;
    private final UserAccountBalanceMapper userAccountBalanceMapper;
    private final WithdrawRecordMapper withdrawRecordMapper;
    private final UserSocialMapper userSocialMapper;
    private final InviteRecordMapper inviteRecordMapper;
    private final UserTaskMapper userTaskMapper;
    private final UserKycAuthMapper userKycAuthMapper;
    private final PasswordEncoder passwordEncoder;
    private final TransactionTemplate transactionTemplate;

    @Override
    public Page<ClientUserManagementRes> getClientUserManagementList(ClientUserManagementReq request) {
        // 1. 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = buildQueryWrapper(request);

        // 2. 分页查询甲方用户基础信息
        Page<User> userPage = userMapper.selectPage(request.getPage(), queryWrapper);

        // 3. 批量获取用户ID列表
        List<Long> userIds = userPage.getRecords().stream()
                .map(User::getId)
                .collect(Collectors.toList());

        // 4. 批量查询统计数据（避免N+1查询问题）
        Map<Long, TaskStatistics> taskStatsMap = getTaskStatisticsBatch(userIds);
        Map<Long, WithdrawStatistics> withdrawStatsMap = getWithdrawStatisticsBatch(userIds);
        Map<Long, Integer> productCountMap = getProductCountBatch(userIds);
        Map<Long, BigDecimal> depositAmountMap = getDepositAmountBatch(userIds);
        Map<Long, BigDecimal> currentBalanceMap = getCurrentBalanceBatch(userIds);

        // 5. 转换为响应对象
        List<ClientUserManagementRes> resultList = userPage.getRecords().stream()
                .map(user -> convertToClientUserManagementRes(user, taskStatsMap, withdrawStatsMap,
                        productCountMap, depositAmountMap, currentBalanceMap))
                .collect(Collectors.toList());

        // 6. 构建分页响应
        Page<ClientUserManagementRes> resultPage = new Page<>();
        resultPage.setCurrent(userPage.getCurrent());
        resultPage.setSize(userPage.getSize());
        resultPage.setTotal(userPage.getTotal());
        resultPage.setRecords(resultList);

        return resultPage;
    }

    /**
     * 构建查询条件（使用链式调用，不使用if判断）
     */
    private LambdaQueryWrapper<User> buildQueryWrapper(ClientUserManagementReq request) {
        return Wrappers.<User>lambdaQuery()
                // 只查询甲方用户
                .eq(User::getAccountRole, AccountTypeEnum.client)
                // 用户ID精确查询
                .eq(ObjectUtil.isNotEmpty(request.getUserId()), User::getId, request.getUserId())
                // 用户名称模糊查询
                .like(ObjectUtil.isNotEmpty(request.getUserName()), User::getNickName, request.getUserName())
                // 邮箱模糊查询
                .like(ObjectUtil.isNotEmpty(request.getEmail()), User::getEmail, request.getEmail())
                // 手机号模糊查询
                .like(ObjectUtil.isNotEmpty(request.getPhone()), User::getUserPhone, request.getPhone())
                // 日期范围查询（使用BaseRequest中的beginDate和endDate）
                .ge(ObjectUtil.isNotEmpty(request.getBeginDate()), User::getDay, request.getBeginDate())
                .le(ObjectUtil.isNotEmpty(request.getEndDate()), User::getDay, request.getEndDate())
                // 按创建时间倒序排列
                .orderByDesc(User::getCreateTime);
    }

    /**
     * 批量获取任务统计数据
     */
    private Map<Long, TaskStatistics> getTaskStatisticsBatch(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Map.of();
        }

        // 使用自定义SQL批量查询任务统计数据
        List<TaskStatistics> taskStatsList = taskMapper.getTaskStatisticsByUserIds(userIds);

        return taskStatsList.stream()
                .collect(Collectors.toMap(TaskStatistics::getUserId, stats -> stats));
    }

    /**
     * 批量获取提现统计数据
     */
    private Map<Long, WithdrawStatistics> getWithdrawStatisticsBatch(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Map.of();
        }

        // 使用自定义SQL批量查询提现统计数据
        List<WithdrawStatistics> withdrawStatsList = withdrawRecordMapper.getWithdrawStatisticsByUserIds(userIds);

        return withdrawStatsList.stream()
                .collect(Collectors.toMap(WithdrawStatistics::getUserId, stats -> stats));
    }

    /**
     * 批量获取产品数量
     */
    private Map<Long, Integer> getProductCountBatch(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Map.of();
        }

        // 使用自定义SQL批量查询产品数量（SQL中已使用CAST确保返回Long类型）
        List<Map<String, Object>> productCounts = productMapper.getProductCountByUserIds(userIds);

        return productCounts.stream()
                .collect(Collectors.toMap(
                    map -> (Long) map.get("userId"),  // 直接转换为Long，SQL已确保类型
                    map -> ((Number) map.get("productCount")).intValue()
                ));
    }

    /**
     * 批量获取充值金额
     */
    private Map<Long, BigDecimal> getDepositAmountBatch(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Map.of();
        }

        // 使用自定义SQL批量查询充值金额（SQL中已使用CAST确保返回Long类型）
        List<Map<String, Object>> depositAmounts = depositRecordMapper.getDepositAmountByUserIds(userIds);

        return depositAmounts.stream()
                .collect(Collectors.toMap(
                    map -> (Long) map.get("userId"),  // 直接转换为Long，SQL已确保类型
                    map -> (BigDecimal) map.getOrDefault("totalAmount", BigDecimal.ZERO)
                ));
    }

    /**
     * 批量获取当前余额
     */
    private Map<Long, BigDecimal> getCurrentBalanceBatch(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Map.of();
        }

        // 使用自定义SQL批量查询当前余额（SQL中已使用CAST确保返回Long类型）
        List<Map<String, Object>> currentBalances = userAccountBalanceMapper.getCurrentBalanceByUserIds(userIds);

        return currentBalances.stream()
                .collect(Collectors.toMap(
                    map -> (Long) map.get("userId"),  // 直接转换为Long，SQL已确保类型
                    map -> (BigDecimal) map.getOrDefault("availableAmount", BigDecimal.ZERO)
                ));
    }

    /**
     * 转换为甲方用户管理响应对象
     */
    private ClientUserManagementRes convertToClientUserManagementRes(
            User user,
            Map<Long, TaskStatistics> taskStatsMap,
            Map<Long, WithdrawStatistics> withdrawStatsMap,
            Map<Long, Integer> productCountMap,
            Map<Long, BigDecimal> depositAmountMap,
            Map<Long, BigDecimal> currentBalanceMap) {

        Long userId = user.getId();
        TaskStatistics taskStats = taskStatsMap.getOrDefault(userId, new TaskStatistics());
        WithdrawStatistics withdrawStats = withdrawStatsMap.getOrDefault(userId, new WithdrawStatistics());

        return ClientUserManagementRes.builder()
                .userId(userId)
                .userName(user.getNickName())
                .email(user.getEmail())
                .phone(user.getUserPhone())
                .relatedProductCount(productCountMap.getOrDefault(userId, 0))
                .publishTaskTimes(taskStats.getTaskCount())
                .publishTaskCount(taskStats.getTotalTaskNumber())
                .taskCompletedReceivedCount(taskStats.getTotalReceiveNum())
                .taskCompletedCount(taskStats.getTotalFinishNum())
                .totalGrantedAmount(taskStats.getTotalGrantedAmount())
                .rewardFrozenAmount(taskStats.getTotalFrozenAmount())
                .totalRechargeAmount(depositAmountMap.getOrDefault(userId, BigDecimal.ZERO))
                .currentBalance(currentBalanceMap.getOrDefault(userId, BigDecimal.ZERO))
                .withdrawingAmount(withdrawStats.getWithdrawingAmount())
                .totalWithdrawnAmount(withdrawStats.getWithdrawnAmount())
                .createTime(user.getCreateTime())
                .build();
    }

    @Override
    public Long createClientUser(CreateClientUserReq request) {
        log.info("开始创建甲方用户，邮箱：{}", request.getEmail());

        return transactionTemplate.execute(status -> {
            try {
                // 1. 检查邮箱是否已存在
                checkEmailUniqueness(request.getEmail());

                // 2. 创建并保存用户基本信息
                User user = buildUser(request);
                int insertResult = userMapper.insert(user);
                Assert.isTrue(insertResult > 0, "创建用户失败");

                Long userId = user.getId();
                log.info("用户创建成功，用户ID：{}", userId);

                // 3. 批量创建社交媒体绑定信息
                List<UserSocial> socialList = buildUserSocialList(userId, request);
                batchInsertSocialMedia(socialList);

                log.info("甲方用户创建完成，用户ID：{}", userId);
                return userId;

            } catch (Exception e) {
                log.error("创建甲方用户失败，邮箱：{}", request.getEmail(), e);
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 检查邮箱唯一性
     */
    private void checkEmailUniqueness(String email) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.<User>lambdaQuery()
                .eq(User::getEmail, email);
        User existingUser = userMapper.selectOne(queryWrapper);
        Assert.isNull(existingUser, "邮箱已存在，无法创建用户");
    }

    /**
     * 构建用户对象
     */
    private User buildUser(CreateClientUserReq request) {
        User user = new User();
        user.setEmail(request.getEmail());
        user.setPwd(passwordEncoder.encode(request.getPassword()));
        user.setNickName(Optional.ofNullable(request.getUserName())
                .filter(StringUtils::hasText)
                .orElse(request.getEmail()));
        user.setUserImage(request.getAvatar());
        user.setUserDescription(request.getUserDescription());
        user.setAccountRole(AccountTypeEnum.client);
        user.setDay(TimeUtil.today(new Date()));
        user.setUserPhone(request.getPhone());
        user.setCountryName(request.getCountry());
        return user;
    }

    /**
     * 批量插入社交媒体绑定信息
     */
    private void batchInsertSocialMedia(List<UserSocial> socialList) {
        Optional.of(socialList)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    list.forEach(userSocialMapper::insert);
                    log.info("用户社交媒体信息创建成功，共{}条记录", list.size());
                });
    }

    /**
     * 构建用户社交媒体绑定信息列表（优化版本，使用Lambda表达式）
     */
    private List<UserSocial> buildUserSocialList(Long userId, CreateClientUserReq request) {

        // 定义社交媒体类型和URL的映射关系
        Map<SocialTypeEnum, String> socialUrlMap = Map.of(
                SocialTypeEnum.TWITTER, request.getTwitterUrl(),
                SocialTypeEnum.TELEGRAM, request.getTelegramUrl(),
                SocialTypeEnum.DISCORD, request.getDiscordUrl(),
                SocialTypeEnum.REDDIT, request.getRedditUrl(),
                SocialTypeEnum.TIKTOK, request.getTiktokUrl(),
                SocialTypeEnum.MEDIUM, request.getMediumUrl()
        );

        // 使用Lambda表达式和Stream API构建社交媒体列表
        return socialUrlMap.entrySet().stream()
                .filter(entry -> StringUtils.hasText(entry.getValue()))
                .map(entry -> buildUserSocial(userId, entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个用户社交媒体绑定信息
     */
    private UserSocial buildUserSocial(Long userId, SocialTypeEnum socialType, String socialUrl) {
        return UserSocial.builder()
                .userId(userId)
                .socialType(socialType)
                .socialUrl(socialUrl)
                .status(SocialBindStatusEnum.VERIFIED)
                .build();
    }

    @Override
    public String exportExcel(ClientUserManagementReq request) {
        List<ClientUserManagementRes> data = getClientUserManagementListData(request);
        // 转为Excel DTO
        List<ClientUserManagementExcel> excelList = BeanUtil.copyToList(data, ClientUserManagementExcel.class);
        return ExcelUtil.getExcel("甲方用户管理数据导出", ClientUserManagementExcel.class, excelList, false);
    }

    /**
     * 获取甲方用户管理列表数据（不分页）
     */
    private List<ClientUserManagementRes> getClientUserManagementListData(ClientUserManagementReq request) {
        // 1. 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = buildQueryWrapper(request);

        // 2. 查询所有符合条件的甲方用户基础信息
        List<User> userList = userMapper.selectList(queryWrapper);

        if (userList.isEmpty()) {
            log.info("没有找到符合条件的甲方用户数据");
            return Collections.emptyList();
        }

        // 3. 批量获取用户ID列表
        List<Long> userIds = userList.stream()
                .map(User::getId)
                .collect(Collectors.toList());

        // 4. 批量查询统计数据
        Map<Long, TaskStatistics> taskStatsMap = getTaskStatisticsBatch(userIds);
        Map<Long, WithdrawStatistics> withdrawStatsMap = getWithdrawStatisticsBatch(userIds);
        Map<Long, Integer> productCountMap = getProductCountBatch(userIds);
        Map<Long, BigDecimal> depositAmountMap = getDepositAmountBatch(userIds);
        Map<Long, BigDecimal> currentBalanceMap = getCurrentBalanceBatch(userIds);

        // 5. 转换为响应对象
        return userList.stream()
                .map(user -> convertToClientUserManagementRes(user, taskStatsMap, withdrawStatsMap,
                        productCountMap, depositAmountMap, currentBalanceMap))
                .collect(Collectors.toList());
    }

    @Override
    public Page<WorkerUserManagementRes> getWorkerUserManagementList(WorkerUserManagementReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<User> queryWrapper = buildWorkerQueryWrapper(request);

        // 分页查询乙方用户基础信息
        Page<User> userPage = userMapper.selectPage(request.getPage(), queryWrapper);

        // 如果没有数据，直接返回空分页
        if (userPage.getRecords().isEmpty()) {
            return PageUtil.builderPage(userPage, WorkerUserManagementRes.class);
        }

        // 批量获取用户ID列表
        List<Long> userIds = userPage.getRecords().stream()
                .map(User::getId)
                .collect(Collectors.toList());

        // 批量查询统计数据
        WorkerUserStatistics statistics = getWorkerUserStatisticsBatch(userIds);

        // 转换为响应对象
        List<WorkerUserManagementRes> resultList = userPage.getRecords().stream()
                .map(user -> convertToWorkerUserManagementRes(user, statistics))
                .filter(res -> filterByVerifyStatus(res, request.getVerifyStatus()))  // 根据认证状态过滤
                .collect(Collectors.toList());

        // 构建分页响应
        Page<WorkerUserManagementRes> resultPage = new Page<>();
        resultPage.setCurrent(userPage.getCurrent());
        resultPage.setSize(userPage.getSize());
        resultPage.setTotal(userPage.getTotal());
        resultPage.setRecords(resultList);

        return resultPage;
    }

    /**
     * 构建乙方用户查询条件
     */
    private LambdaQueryWrapper<User> buildWorkerQueryWrapper(WorkerUserManagementReq request) {
        // 动态构建查询条件，参考WithdrawRecordServiceImpl的模式
        return Wrappers.<User>lambdaQuery()
                // 只查询乙方用户
                .eq(User::getAccountRole, AccountTypeEnum.provider)
                // 用户ID精确查询
                .eq(ObjectUtil.isNotEmpty(request.getUserId()), User::getId, request.getUserId())
                // 用户名称模糊查询
                .like(ObjectUtil.isNotEmpty(request.getUserName()), User::getNickName, request.getUserName())
                // 邮箱模糊查询
                .like(ObjectUtil.isNotEmpty(request.getEmail()), User::getEmail, request.getEmail())
                // 手机号模糊查询
                .like(ObjectUtil.isNotEmpty(request.getPhone()), User::getUserPhone, request.getPhone())
                // 创建时间范围查询
                .ge(ObjectUtil.isNotEmpty(request.getBeginDate()), User::getDay, request.getBeginDate())
                .le(ObjectUtil.isNotEmpty(request.getEndDate()), User::getDay, request.getEndDate())
                // 按创建时间倒序排列
                .orderByDesc(User::getCreateTime);
    }

    /**
     * 批量获取乙方用户统计数据（优化版本，一次性获取所有统计信息）
     */
    private WorkerUserStatistics getWorkerUserStatisticsBatch(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return WorkerUserStatistics.builder()
                    .parentInfoMap(Map.of())
                    .inviteStatisticsMap(Map.of())
                    .taskStatisticsMap(Map.of())
                    .withdrawStatisticsMap(Map.of())
                    .currentBalanceMap(Map.of())
                    .build();
        }

        String userIdList = userIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        // 并行查询各种统计数据
        Map<Long, UserParentInfoDto> parentInfoMap = getParentInfoBatch(userIdList);
        Map<Long, InviteStatisticsDto> inviteStatisticsMap = getInviteStatisticsBatch(userIdList);
        Map<Long, UserTaskStatisticsDto> taskStatisticsMap = getUserTaskStatisticsBatch(userIdList);
        Map<Long, WithdrawStatistics> withdrawStatisticsMap = getWithdrawStatisticsBatch(userIds);
        Map<Long, BigDecimal> currentBalanceMap = getCurrentBalanceBatch(userIds);
        Map<Long, UserVerifyStatusEnum> verifyStatusMap = getVerifyStatusBatch(userIdList);

        return WorkerUserStatistics.builder()
                .parentInfoMap(parentInfoMap)
                .inviteStatisticsMap(inviteStatisticsMap)
                .taskStatisticsMap(taskStatisticsMap)
                .withdrawStatisticsMap(withdrawStatisticsMap)
                .currentBalanceMap(currentBalanceMap)
                .verifyStatusMap(verifyStatusMap)
                .build();
    }

    /**
     * 批量获取用户上级信息
     */
    private Map<Long, UserParentInfoDto> getParentInfoBatch(String userIdList) {
        List<UserParentInfoDto> parentInfoList = inviteRecordMapper.getParentInfoByUserIds(userIdList);
        return parentInfoList.stream()
                .collect(Collectors.toMap(UserParentInfoDto::getUserId, dto -> dto));
    }

    /**
     * 批量获取邀请统计数据（直推和总推广）
     */
    private Map<Long, InviteStatisticsDto> getInviteStatisticsBatch(String userIdList) {
        List<InviteStatisticsDto> inviteStatistics = inviteRecordMapper.getInviteStatisticsByUserIds(userIdList);
        return inviteStatistics.stream()
                .collect(Collectors.toMap(InviteStatisticsDto::getUserId, dto -> dto));
    }

    /**
     * 批量获取用户任务统计数据
     */
    private Map<Long, UserTaskStatisticsDto> getUserTaskStatisticsBatch(String userIdList) {
        List<UserTaskStatisticsDto> taskStatistics = userTaskMapper.getUserTaskStatsByUserIds(userIdList);
        return taskStatistics.stream()
                .collect(Collectors.toMap(UserTaskStatisticsDto::getUserId, dto -> dto));
    }

    /**
     * 批量获取用户KYC认证状态
     */
    private Map<Long, UserVerifyStatusEnum> getVerifyStatusBatch(String userIdList) {
        List<Map<String, Object>> verifyStatusList = userKycAuthMapper.getVerifyStatusByUserIds(userIdList, AccountTypeEnum.provider.name());

        return verifyStatusList.stream()
                .collect(Collectors.toMap(
                    map -> ((Number) map.get("userId")).longValue(),
                    map -> {
                        String statusStr = (String) map.get("verifyStatus");
                        try {
                            return UserVerifyStatusEnum.valueOf(statusStr);
                        } catch (Exception e) {
                            log.warn("无效的认证状态值: {}", statusStr);
                            return null;
                        }
                    },
                    (existing, replacement) -> existing,
                    HashMap::new
                ));
    }



    /**
     * 转换为乙方用户管理响应对象（优化版本，使用DTO对象）
     */
    private WorkerUserManagementRes convertToWorkerUserManagementRes(User user, WorkerUserStatistics statistics) {
        Long userId = user.getId();

        // 获取各种统计数据，使用DTO对象避免硬编码
        UserParentInfoDto parentInfo = statistics.getParentInfoMap().get(userId);
        InviteStatisticsDto inviteStats = statistics.getInviteStatisticsMap().get(userId);
        UserTaskStatisticsDto taskStats = statistics.getTaskStatisticsMap().get(userId);
        WithdrawStatistics withdrawStats = statistics.getWithdrawStatisticsMap().getOrDefault(userId, new WithdrawStatistics());
        BigDecimal currentBalance = statistics.getCurrentBalanceMap().getOrDefault(userId, BigDecimal.ZERO);
        UserVerifyStatusEnum verifyStatus = statistics.getVerifyStatusMap().get(userId);

        return WorkerUserManagementRes.builder()
                .userId(userId)
                .userName(user.getNickName())
                .email(user.getEmail())
                .phone(user.getUserPhone())
                .verifyStatus(verifyStatus)  // 使用从user_kyc_auth表查询的真实认证状态
                .parentUserName(parentInfo != null ? parentInfo.getParentUserName() : null)
                .parentUserId(parentInfo != null ? parentInfo.getParentUserId() : null)
                .directInviteCount(inviteStats != null ? inviteStats.getDirectInviteCount() : 0)
                .totalInviteCount(inviteStats != null ? inviteStats.getTotalInviteCount() : 0)
                .receivedTaskCount(taskStats != null ? taskStats.getReceivedTaskCount() : 0)
                .completedTaskCount(taskStats != null ? taskStats.getCompletedTaskCount() : 0)
                .pendingReward(taskStats != null ? taskStats.getPendingReward() : BigDecimal.ZERO)
                .settledReward(taskStats != null ? taskStats.getSettledReward() : BigDecimal.ZERO)
                .withdrawingAmount(withdrawStats.getWithdrawingAmount())
                .withdrawnAmount(withdrawStats.getWithdrawnAmount())
                .accountBalance(currentBalance)
                .createTime(user.getCreateTime())
                .build();
    }

    /**
     * 根据认证状态过滤结果
     */
    private boolean filterByVerifyStatus(WorkerUserManagementRes res, UserVerifyStatusEnum requestVerifyStatus) {
        if (requestVerifyStatus == null) {
            return true;  // 没有指定认证状态，不过滤
        }
        return requestVerifyStatus.equals(res.getVerifyStatus());
    }

    @Override
    public String exportWorkerUserExcel(WorkerUserManagementReq request) {
        // 查询数据（不分页，获取所有符合条件的数据）
        List<WorkerUserManagementRes> data = getWorkerUserManagementListData(request);

        // 转换为Excel DTO
        List<WorkerUserManagementExcel> excelList = data.stream()
                .map(this::convertToWorkerUserManagementExcel)
                .collect(Collectors.toList());

        // 生成Excel文件
        return ExcelUtil.getExcel("乙方用户管理数据导出", WorkerUserManagementExcel.class, excelList, false);
    }

    /**
     * 获取乙方用户管理列表数据（不分页）
     */
    private List<WorkerUserManagementRes> getWorkerUserManagementListData(WorkerUserManagementReq request) {
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = buildWorkerQueryWrapper(request);

        // 查询所有符合条件的乙方用户基础信息
        List<User> userList = userMapper.selectList(queryWrapper);

        if (userList.isEmpty()) {
            return Collections.emptyList();
        }

        // 批量获取用户ID列表
        List<Long> userIds = userList.stream()
                .map(User::getId)
                .collect(Collectors.toList());

        // 批量查询统计数据
        WorkerUserStatistics statistics = getWorkerUserStatisticsBatch(userIds);

        // 转换为响应对象并过滤
        return userList.stream()
                .map(user -> convertToWorkerUserManagementRes(user, statistics))
                .filter(res -> filterByVerifyStatus(res, request.getVerifyStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 转换为乙方用户管理导出Excel对象
     */
    private WorkerUserManagementExcel convertToWorkerUserManagementExcel(WorkerUserManagementRes res) {
        WorkerUserManagementExcel excel = new WorkerUserManagementExcel();
        excel.setUserId(res.getUserId());
        excel.setUserName(res.getUserName());
        excel.setEmail(res.getEmail());
        excel.setPhone(res.getPhone());
        excel.setVerifyStatus(res.getVerifyStatus() != null ? res.getVerifyStatus().getDesc() : "未认证");
        excel.setParentUserName(res.getParentUserName());
        excel.setParentUserId(res.getParentUserId());
        excel.setDirectInviteCount(res.getDirectInviteCount());
        excel.setTotalInviteCount(res.getTotalInviteCount());
        excel.setReceivedTaskCount(res.getReceivedTaskCount());
        excel.setCompletedTaskCount(res.getCompletedTaskCount());
        excel.setPendingReward(res.getPendingReward());
        excel.setSettledReward(res.getSettledReward());
        excel.setWithdrawingAmount(res.getWithdrawingAmount());
        excel.setWithdrawnAmount(res.getWithdrawnAmount());
        excel.setAccountBalance(res.getAccountBalance());
        excel.setCreateTime(res.getCreateTime());
        return excel;
    }
}
