package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.UserKycAuthService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserKycAuthListReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthSaveReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthUpdateReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthVerifyReq;
import com.letu.solutions.model.cms.response.cms.UserKycAuthPageRes;
import com.letu.solutions.model.cms.response.cms.UserKycAuthDetailRes;
import com.letu.solutions.model.cms.response.cms.UserKycAuthInfoRes;

/**
 * 用户/KYC身份认证表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:user_kyc_auth", valueDesc = "用户:KYC身份认证表")
public class UserKycAuthController{
    private final UserKycAuthService basicService;
    /**
    * KYC身份认证表 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @PostMapping("/userKycAuth/page")
    public R<Page<UserKycAuthPageRes>> loadPage(@Validated @RequestBody UserKycAuthListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * KYC身份认证表 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/userKycAuth/list")
    public R<List<UserKycAuthPageRes>> loadList(UserKycAuthListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * KYC身份认证表 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/userKycAuth/selectById")
    public R<UserKycAuthDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * KYC身份认证表 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/userKycAuth/insert")
    public R<Boolean> insert(@Validated @RequestBody UserKycAuthSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * KYC身份认证表 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/userKycAuth/update")
    public R<Boolean> update(@Validated @RequestBody UserKycAuthUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * KYC身份认证表 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/userKycAuth/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }

    /**
     * 乙方用户身份审核
     * 支持审核通过和审核不通过，不通过时需要填写拒绝原因
     */
    @Preauthorize(value = "verifyUserKyc", valueDesc = "乙方用户身份审核")
    @PostMapping("/userKycAuth/verify")
    public R<Boolean> verifyUserKyc(@Validated @RequestBody UserKycAuthVerifyReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.verifyUserKyc(request, extendData));
    }

    /**
     * 根据用户ID查询KYC认证信息
     * 返回证件类型、证件号、上传证件数量、证件图片等信息
     */
    @Preauthorize(value = "getUserKycAuthInfo", valueDesc = "查询用户KYC认证信息")
    @GetMapping("/userKycAuth/info/{userId}")
    public R<UserKycAuthInfoRes> getUserKycAuthInfo(@PathVariable("userId") Long userId) {
        return R.success(basicService.getUserKycAuthInfo(userId));
    }
}
