package com.letu.solutions.cms.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户任务统计数据DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserTaskStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 领取任务数
     */
    private Integer receivedTaskCount;

    /**
     * 完成任务数
     */
    private Integer completedTaskCount;

    /**
     * 未结奖励
     */
    private BigDecimal pendingReward;

    /**
     * 已结奖励
     */
    private BigDecimal settledReward;
}
