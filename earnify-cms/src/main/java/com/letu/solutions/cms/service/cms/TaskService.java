package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.Task;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.TaskListReq;
import com.letu.solutions.model.cms.request.cms.TaskSaveReq;
import com.letu.solutions.model.cms.response.cms.TaskPageRes;
import com.letu.solutions.model.cms.response.cms.TaskDetailRes;

/**
 * 任务 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface TaskService {
    Page<TaskPageRes> selectBasicPage(Page<Task> page, TaskListReq request);

    String selectBasicList(TaskListReq request);

    TaskDetailRes selectByIdBasic(Long id);

    boolean saveBasic(TaskSaveReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);
}
