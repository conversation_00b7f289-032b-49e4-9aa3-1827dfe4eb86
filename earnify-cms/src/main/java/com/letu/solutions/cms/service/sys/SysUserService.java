package com.letu.solutions.cms.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.dubbo.middle.cms.dto.SysUserDto;
import com.letu.solutions.dubbo.middle.cms.req.SysUserQueryReq;
import com.letu.solutions.model.cms.response.sys.SysUserResponse;
import com.letu.solutions.model.entity.sys.SysUser;

import java.util.List;

/**
 * 后台系统用户表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface SysUserService {
    Page<SysUserDto> selectBasicPage(Page<SysUser> page, SysUserQueryReq request);

    List<SysUserDto> selectBasicList(SysUserQueryReq request);

    SysUserResponse selectByIdBasic(Long id);
}
