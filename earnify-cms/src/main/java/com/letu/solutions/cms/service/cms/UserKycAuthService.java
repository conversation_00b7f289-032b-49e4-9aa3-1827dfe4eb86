package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.UserKycAuth;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserKycAuthListReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthSaveReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthUpdateReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthVerifyReq;
import com.letu.solutions.model.cms.response.cms.UserKycAuthPageRes;
import com.letu.solutions.model.cms.response.cms.UserKycAuthDetailRes;
import com.letu.solutions.model.cms.response.cms.UserKycAuthInfoRes;

/**
 * KYC身份认证表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface UserKycAuthService {
    Page<UserKycAuthPageRes> selectBasicPage(Page<UserKycAuth> page, UserKycAuthListReq request);

    List<UserKycAuthPageRes> selectBasicList(UserKycAuthListReq request);

    UserKycAuthDetailRes selectByIdBasic(Long id);

    boolean saveBasic(UserKycAuthSaveReq record, ExtendData extendData);

    boolean updateBasic(UserKycAuthUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    /**
     * 乙方用户身份审核
     * @param request 审核请求参数
     * @param extendData 扩展数据
     * @return 审核结果
     */
    boolean verifyUserKyc(UserKycAuthVerifyReq request, ExtendData extendData);

    /**
     * 根据用户ID查询KYC认证信息
     * @param userId 用户ID
     * @return KYC认证信息
     */
    UserKycAuthInfoRes getUserKycAuthInfo(Long userId);
}
