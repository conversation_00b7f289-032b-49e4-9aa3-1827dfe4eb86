package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.FinanceConfigListReq;
import com.letu.solutions.model.cms.request.cms.FinanceConfigSaveReq;
import com.letu.solutions.model.cms.request.cms.FinanceConfigUpdateReq;
import com.letu.solutions.model.cms.response.cms.FinanceConfigPageRes;
import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;

/**
 * 财务配置表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface FinanceConfigService {
    Page<FinanceConfigPageRes> selectBasicPage(Page<FinanceConfig> page, FinanceConfigListReq request);

    List<FinanceConfigPageRes> selectBasicList(FinanceConfigListReq request);

    FinanceConfigDetailRes selectByIdBasic(Long id);

    boolean saveBasic(FinanceConfigSaveReq record, ExtendData extendData);

    boolean updateBasic(FinanceConfigUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    FinanceConfigDetailRes selectByNetwork(String network);

    FinanceConfig getConfig(String network);
}
