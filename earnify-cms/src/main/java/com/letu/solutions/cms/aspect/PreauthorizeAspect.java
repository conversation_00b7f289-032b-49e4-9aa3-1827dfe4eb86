package com.letu.solutions.cms.aspect;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.letu.solutions.cms.service.sys.SysOperateService;
import com.letu.solutions.core.config.ExtendDataConverter;
import com.letu.solutions.core.model.ExtendData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class PreauthorizeAspect {
    private final ExtendDataConverter extendDataConverter;
    private final SysOperateService sysOperateService;

    /**
     * ..表示包及子包 该方法代表controller层的所有方法
     *
     * @date 2020/9/21
     */

    @Pointcut("execution(* com.letu.solutions.cms.controller..*.*(..))")
    public void controllerMethod() {
    }


    @Before("controllerMethod()")
    public void doBefore(JoinPoint joinPoint) {
        Pair<String, String> authPair = sysOperateService.loadPreAuth(joinPoint, true);
        if (ObjectUtil.isNull(authPair)) {
            return;
        }
        if (CollectionUtil.isNotEmpty(PreauthorizeRunnerImpl.allBackInterfaces) && !PreauthorizeRunnerImpl.allBackInterfaces.contains(authPair.getKey())) {
            return;
        }
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            String extendDataStr = request.getHeader("extendData");
            if (StrUtil.isNotEmpty(extendDataStr)) {
                ExtendData extendData = extendDataConverter.convert(extendDataStr);
                List<String> nameAlls = extendData.getAuthentication().getCmsUserInfo().getNameAlls();
                Assert.isTrue(nameAlls.contains(authPair.getKey()), String.format(String.format("用户缺乏接口权限《%s》", authPair.getValue())));
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception ignored) {
        }
    }
}