package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.ProductExcel;
import com.letu.solutions.model.entity.cms.Product;
import com.letu.solutions.cms.mapper.cms.ProductMapper;
import com.letu.solutions.cms.service.cms.ProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.enums.cms.ProductStatusEnum;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Date;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.ProductListReq;
import com.letu.solutions.model.cms.request.cms.ProductSaveReq;
import com.letu.solutions.model.cms.request.cms.ProductUpdateReq;
import com.letu.solutions.model.cms.response.cms.ProductPageRes;
import com.letu.solutions.model.cms.response.cms.ProductDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;

/**
 * 产品表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {
    @Override
    public Page<ProductPageRes> selectBasicPage(Page<Product> page, ProductListReq request) {
        return baseMapper.selectProductPage(page, request);
    }

    @Override
    public Page<Product> selectNotPage(Page page) {
        return baseMapper.selectPage(page,Wrappers.<Product>lambdaQuery().ne(Product::getProductStatus,ProductStatusEnum.stopShelf));
    }


    @Override
    public String selectBasicList(ProductListReq request) {
        List<ProductExcel> list = baseMapper.selectProductList(request);
        return ExcelUtil.getExcel("产品管理列表", ProductExcel.class, list, false);
    }

    @Override
    public ProductDetailRes selectByIdBasic(Long id) {
        Product record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, ProductDetailRes.class);
    }

    @Override
    public boolean saveBasic(ProductSaveReq record, ExtendData extendData) {
        Product saveRecord = BeanUtil.copyProperties(record, Product.class);
        saveRecord.setProductStatus(ProductStatusEnum.notList);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(ProductUpdateReq record, ExtendData extendData) {
        Product updateRecord = BeanUtil.copyProperties(record, Product.class);
        Product product = baseMapper.selectById(record.getId());
        Assert.notNull(product, "产品id错误");
        if (null != record.getPartyUserId()) {
            Assert.isTrue(product.getProductStatus() == ProductStatusEnum.notList, "产品已经上架不能修改甲方信息");
        }
        return baseMapper.updateById(updateRecord) > 0;
    }
}