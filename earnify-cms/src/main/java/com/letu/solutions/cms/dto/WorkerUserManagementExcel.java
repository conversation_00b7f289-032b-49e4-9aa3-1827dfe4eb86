package com.letu.solutions.cms.dto;

import com.letu.solutions.cms.aspect.ExcelField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 乙方用户管理导出Excel DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class WorkerUserManagementExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelField(title = "用户ID", sort = 1)
    private Long userId;

    /**
     * 用户名称
     */
    @ExcelField(title = "用户名称", sort = 2)
    private String userName;

    /**
     * 邮箱
     */
    @ExcelField(title = "邮箱", sort = 3)
    private String email;

    /**
     * 手机号
     */
    @ExcelField(title = "手机号", sort = 4)
    private String phone;

    /**
     * 身份审核状态
     */
    @ExcelField(title = "身份审核状态", sort = 5)
    private String verifyStatus;

    /**
     * 关联上级名称
     */
    @ExcelField(title = "关联上级名称", sort = 6)
    private String parentUserName;

    /**
     * 关联上级ID
     */
    @ExcelField(title = "关联上级ID", sort = 7)
    private Long parentUserId;

    /**
     * 直推人数
     */
    @ExcelField(title = "直推人数", sort = 8)
    private Integer directInviteCount;

    /**
     * 总人数（包含间推）
     */
    @ExcelField(title = "总人数", sort = 9)
    private Integer totalInviteCount;

    /**
     * 领取任务数
     */
    @ExcelField(title = "领取任务数", sort = 10)
    private Integer receivedTaskCount;

    /**
     * 完成任务数
     */
    @ExcelField(title = "完成任务数", sort = 11)
    private Integer completedTaskCount;

    /**
     * 未结奖励
     */
    @ExcelField(title = "未结奖励", sort = 12)
    private BigDecimal pendingReward;

    /**
     * 已结奖励
     */
    @ExcelField(title = "已结奖励", sort = 13)
    private BigDecimal settledReward;

    /**
     * 提现中
     */
    @ExcelField(title = "提现中", sort = 14)
    private BigDecimal withdrawingAmount;

    /**
     * 已提现
     */
    @ExcelField(title = "已提现", sort = 15)
    private BigDecimal withdrawnAmount;

    /**
     * 账户余额
     */
    @ExcelField(title = "账户余额", sort = 16)
    private BigDecimal accountBalance;

    /**
     * 创建时间
     */
    @ExcelField(title = "创建时间", sort = 17, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
