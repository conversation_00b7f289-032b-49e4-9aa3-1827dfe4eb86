package com.letu.solutions.cms.mapper.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.dto.TaskExcel;
import com.letu.solutions.cms.dto.TaskStatistics;
import com.letu.solutions.model.entity.cms.Task;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import com.letu.solutions.model.cms.request.cms.TaskListReq;
import com.letu.solutions.model.cms.response.cms.TaskPageRes;

/**
 * 任务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper
public interface TaskMapper extends BaseMapper<Task> {
    /**
     * 多条件分页查询任务及关联信息
     */
    Page<TaskPageRes> selectTaskPage(
        @Param("page") Page page,
        @Param("req") TaskListReq req
    );

    List<TaskExcel> selectTaskList(@Param("req") TaskListReq req);

    /**
     * 批量获取用户任务统计数据
     * @param userIds 用户ID列表
     * @return 任务统计数据列表
     */
    @Select("""
            <script>
            SELECT
                CAST(user_id AS SIGNED) as userId,
                CAST(COUNT(*) AS SIGNED) as taskCount,
                CAST(COALESCE(SUM(number), 0) AS SIGNED) as totalTaskNumber,
                CAST(COALESCE(SUM(task_receive_num), 0) AS SIGNED) as totalReceiveNum,
                CAST(COALESCE(SUM(task_finish_num), 0) AS SIGNED) as totalFinishNum,
                COALESCE(SUM(task_finish_num * price), 0) as totalGrantedAmount,
                COALESCE(SUM((number - task_finish_num) * price), 0) as totalFrozenAmount
            FROM task
            WHERE user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            GROUP BY user_id
            </script>
            """)
    List<TaskStatistics> getTaskStatisticsByUserIds(@Param("userIds") List<Long> userIds);
}
