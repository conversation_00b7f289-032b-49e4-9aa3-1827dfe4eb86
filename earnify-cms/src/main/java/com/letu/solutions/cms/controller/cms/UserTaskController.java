package com.letu.solutions.cms.controller.cms;

import com.letu.solutions.model.cms.request.cms.AppealRecordUpdateReq;
import com.letu.solutions.model.entity.cms.UserTaskStep;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.UserTaskService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserTaskListReq;
import com.letu.solutions.model.cms.request.cms.UserTaskSaveReq;
import com.letu.solutions.model.cms.request.cms.UserTaskUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserTaskPageRes;
import com.letu.solutions.model.cms.response.cms.UserTaskDetailRes;

/**
 * 任务/任务记录
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:user_task", valueDesc = "用户接受:任务")
public class UserTaskController{
    private final UserTaskService basicService;
    /**
    * 任务记录分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/userTask/page")
    public R<Page<UserTaskPageRes>> loadPage(UserTaskListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 导出
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/userTask/list")
    public R<String> loadList(UserTaskListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 任务详情
     * @param taskId 任务id
     * @param partyBUserId  乙方用户id
     * @return
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/userTask/selectById")
    public R<UserTaskDetailRes> selectById(@RequestParam("taskId") Long taskId,@RequestParam("partyBUserId") Long partyBUserId) {
        return R.success(basicService.selectByIdBasic(taskId,partyBUserId));
    }
    /**
     * 任务审核
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/userTask/update")
    public R<Boolean> update(@Validated @RequestBody UserTaskUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record));
    }

    /**
     * 查询乙方任务提交凭证
     * @param taskId 任务id
     * @param userId 乙方用户
     * @param extendData
     * @return
     */
    @Preauthorize(value = "selectUserTask", valueDesc = "查询乙方任务提交凭证")
    @GetMapping("/userTask/selectUserTask")
    public R<List<UserTaskStep>> selectUserTask(@RequestParam Long taskId,
                                                @RequestParam Long userId, @RequestHeader ExtendData extendData) {
        return R.success(basicService.selectUserTask(taskId,userId));
    }
}
