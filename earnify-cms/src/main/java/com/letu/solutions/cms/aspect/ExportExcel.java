package com.letu.solutions.cms.aspect;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * 导出Excel文件（导出“XLSX”格式，支持大数据量导出 @see org.apache.poi.ss.SpreadsheetVersion）
 *
 * <AUTHOR>
 * @version 20191227
 */
public class ExportExcel {

    private static final String redWordArry = "*";

    private static Logger log = LoggerFactory.getLogger(ExportExcel.class);

    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";


    private XSSFWorkbook hsw;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 当前行号
     */
    private int rownum;

    /**
     * 下拉选下标
     */
    private int realSheetIndex = 1;
    /**
     * 注解列表（Object[]{ ExcelField, Field/Method }）
     */
    List<Object[]> annotationList = Lists.newArrayList();

    /**
     * 构造函数
     *
     * @param title  表格标题，传“空值”，表示无标题
     * @param cls    实体对象，通过annotation.ExportField获取标题
     * @param type   导出类型（1:导出数据；2：导出模板）
     * @param groups 导入分组
     */
    public ExportExcel(String title, Class<?> cls, int type, int... groups) {
        // Get annotation field
        Field[] fs = cls.getDeclaredFields();
        for (Field f : fs) {
            ExcelField ef = f.getAnnotation(ExcelField.class);
            if (ef != null && (ef.type() == 0 || ef.type() == type)) {
                if (groups != null && groups.length > 0) {
                    boolean inGroup = false;
                    for (int g : groups) {
                        if (inGroup) {
                            break;
                        }
                        for (int efg : ef.groups()) {
                            if (g == efg) {
                                inGroup = true;
                                annotationList.add(new Object[]{ef, f});
                                break;
                            }
                        }
                    }
                } else {
                    annotationList.add(new Object[]{ef, f});
                }
            }
        }
        // Field sorting
        Collections.sort(annotationList, new Comparator<Object[]>() {
            @Override
            public int compare(Object[] o1, Object[] o2) {
                return new Integer(((ExcelField) o1[0]).sort()).compareTo(new Integer(((ExcelField) o2[0]).sort()));
            }
        });
        // Initialize
        List<String> headerList = Lists.newArrayList();
        for (Object[] os : annotationList) {
            String t = ((ExcelField) os[0]).title();
            // 如果是导出，则去掉注释
            if (type == 1) {
                String[] ss = StringUtils.split(t, "**", 2);
                if (ss.length == 2) {
                    t = ss[0];
                }
            }
            headerList.add(t);
        }
        initialize(title, headerList);
    }

    /**
     * 构造函数
     *
     * @param title           表格标题，传“空值”，表示无标题
     * @param cls             实体对象，通过annotation.ExportField获取标题
     * @param type            导出类型（1:导出数据；2：导出模板）
     * @param isGetSuperClass true:包括父类的属性,false:不包括父类的属性
     * @param groups          导入分组
     */
    public ExportExcel(String title, Class<?> cls, boolean isGetSuperClass, int type, int... groups) {
        // Get annotation field
        List<Field> fs = getClazzAndParentClassField(cls);
        for (Field f : fs) {
            ExcelField ef = f.getAnnotation(ExcelField.class);
            if (ef != null && (ef.type() == 0 || ef.type() == type)) {
                if (groups != null && groups.length > 0) {
                    boolean inGroup = false;
                    for (int g : groups) {
                        if (inGroup) {
                            break;
                        }
                        for (int efg : ef.groups()) {
                            if (g == efg) {
                                inGroup = true;
                                annotationList.add(new Object[]{ef, f});
                                break;
                            }
                        }
                    }
                } else {
                    annotationList.add(new Object[]{ef, f});
                }
            }
        }
        // Field sorting
        Collections.sort(annotationList, new Comparator<Object[]>() {
            @Override
            public int compare(Object[] o1, Object[] o2) {
                return new Integer(((ExcelField) o1[0]).sort()).compareTo(new Integer(((ExcelField) o2[0]).sort()));
            }
        });
        // Initialize
        List<String> headerList = Lists.newArrayList();
        for (Object[] os : annotationList) {
            String t = ((ExcelField) os[0]).title();
            // 如果是导出，则去掉注释
            if (type == 1) {
                String[] ss = StringUtils.split(t, "**", 2);
                if (ss.length == 2) {
                    t = ss[0];
                }
            }
            headerList.add(t);
        }
        initialize(title, headerList);
    }

    /**
     * 取得类以及父类的属性
     *
     * @param clazz 类文件
     * @return
     * <AUTHOR>
     */
    private static List<Field> getClazzAndParentClassField(Class<?> clazz) {

        List<Field> datas = new ArrayList<Field>();
        // 取得当前类的属性
        Field[] fields = clazz.getDeclaredFields();
        datas.addAll(Arrays.asList(fields));
        // 取得当前类的父类
        Class<?> superClazz = clazz.getSuperclass();
        if (superClazz != null) {
            // 取得父类的属性
            Field[] superFields = superClazz.getDeclaredFields();
            datas.addAll(Arrays.asList(superFields));
        }
        // 返回数据
        return datas;
    }

    private void initailDiySheet(List<String> titleList, List<List<String>> dataList) {
        this.hsw = new XSSFWorkbook();
        this.sheet = hsw.createSheet("Export");
        this.styles = createStyles(hsw);
        Row headerRow = sheet.createRow(rownum++);
        // 表头添加数据
        for (int i = 0; i < titleList.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellStyle(styles.get("header"));
            cell.setCellValue((String) titleList.get(i));
        }
        for (List<String> row : dataList) {
            Row dataRow = sheet.createRow(rownum++);
            for (int j = 0; j < row.size(); j++) {
                Cell cell = dataRow.createCell(j);
                cell.setCellStyle(styles.get("data"));
                cell.setCellValue(row.get(j));
            }
        }
    }

    /**
     * 生成工作薄
     *
     * @param title
     * @param headerList
     * @return void
     * <AUTHOR>
     * @date 2018/1/11 10:19
     */
    private void initialize(String title, List<String> headerList) {
        this.hsw = new XSSFWorkbook();
        this.sheet = hsw.createSheet("Export");
        this.styles = createStyles(hsw);
        // Create title
        if (StrUtil.isNotBlank(title)) {
            Row titleRow = sheet.createRow(rownum++);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(),
                    headerList.size() - 1));
        }
        // Create header
        if (headerList == null) {
            throw new RuntimeException("headerList not null!");
        }
        Row headerRow = sheet.createRow(rownum++);
        headerRow.setHeightInPoints(16);
        for (int i = 0; i < headerList.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellStyle(styles.get("header"));
            String[] ss = StringUtils.split(headerList.get(i), "**", 2);
            if (ss.length == 2) {
                cell.setCellValue(ss[0]);
            } else {
                cell.setCellValue(headerList.get(i));
            }
            sheet.autoSizeColumn(i);
        }
        for (int i = 0; i < headerList.size(); i++) {
            int colWidth = sheet.getColumnWidth(i) * 2;
            sheet.setColumnWidth(i, colWidth < 3000 ? 3000 : colWidth);
        }
        log.debug("Initialize success.");
    }

    /**
     * @param wb
     * @param sheet
     * @param startRow
     * @param endRow
     * @param startCol
     * @return void
     * <AUTHOR>
     * @date 2018/1/19 17:38
     */
    public void createDateCell(XSSFWorkbook wb, Sheet sheet, int startRow, int endRow, int startCol) {

        XSSFCellStyle cellStyle = wb.createCellStyle();
        XSSFDataFormat format = wb.createDataFormat();
        cellStyle.setDataFormat(format.getFormat("yyyy/MM/dd"));
        XSSFRow row = null;
        XSSFCell cell = null;
        for (int i = startRow; i < endRow; i++) {
            row = (XSSFRow) sheet.createRow(i);
            cell = row.createCell(startCol);
            cell.setCellStyle(cellStyle);
        }
    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderRight(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.LEFT);
        styles.put("data1", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        styles.put("data2", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setAlignment(HorizontalAlignment.RIGHT);
        styles.put("data3", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setWrapText(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);

        return styles;
    }

    /**
     * 添加一行
     *
     * @return 行对象
     */
    public Row addRow() {
        return sheet.createRow(rownum++);
    }

    /**
     * 添加一个单元格
     *
     * @param row    添加的行
     * @param column 添加列号
     * @param val    添加值
     * @return 单元格对象
     */
    public Cell addCell(Row row, int column, Object val) {
        return this.addCell(row, column, val, 0, Class.class, DEFAULT_DATE_FORMAT);
    }

    /**
     * 添加一个单元格
     *
     * @param row        添加的行
     * @param column     添加列号
     * @param val        添加值
     * @param align      对齐方式（1：靠左；2：居中；3：靠右）
     * @param dateFormat 日期格式
     * @return 单元格对象
     */
    public Cell addCell(Row row, int column, Object val, int align, Class<?> fieldType, String dateFormat) {
        Cell cell = row.createCell(column);
        CellStyle style = styles.get("data" + (align >= 1 && align <= 3 ? align : ""));
        try {
            if (val == null) {
                cell.setCellValue("");
            } else if (val instanceof String) {
                cell.setCellValue((String) val);
            } else if (val instanceof Integer) {
                cell.setCellValue(String.valueOf(val));
            } else if (val instanceof Long) {
                if (val != null) {
                    cell.setCellValue(val.toString());
                } else {
                    cell.setCellValue((Long) val);
                }
            } else if (val instanceof Double) {
                cell.setCellValue(String.valueOf(val));
                // cell.setCellValue((Double) val);
            } else if (val instanceof Float) {
                cell.setCellValue((Float) val);
            } else if (val instanceof Date) {
                DataFormat format = hsw.createDataFormat();
                style.setDataFormat(format.getFormat(dateFormat));
                cell.setCellValue((Date) val);
            } else if (val instanceof BigDecimal) {
                cell.setCellValue(String.valueOf(((BigDecimal) val).doubleValue()));
            } else {
                if (fieldType != Class.class) {
                    cell.setCellValue((String) fieldType.getMethod("setValue", Object.class).invoke(null, val));
                } else {
                    cell.setCellValue((String) Class
                            .forName(this.getClass().getName().replaceAll(this.getClass().getSimpleName(),
                                    "fieldtype." + val.getClass().getSimpleName() + "Type"))
                            .getMethod("setValue", Object.class).invoke(null, val));
                }
            }
        } catch (Exception ex) {
            log.info("Set cell value [" + row.getRowNum() + "," + column + "] error: " + ex.toString());
            cell.setCellValue(val.toString());
        }
        cell.setCellStyle(style);
        return cell;
    }

    /**
     * 添加数据（通过annotation.ExportField添加数据）
     *
     * @return list 数据列表
     */
    public <E> ExportExcel setDataList(List<E> list) {
        Map<ExcelField, List> keysMap = new HashMap<>();
        for (E e : list) {
            int colunm = 0;
            Row row = this.addRow();
            for (Object[] os : annotationList) {
                ExcelField ef = (ExcelField) os[0];
                Object val = null;
                // Get entity value
                try {
                    if (os[1] instanceof Field) {
                        val = ReflectUtil.getFieldValue(e, ((Field) os[1]).getName());
                    } else if (os[1] instanceof Method) {
                        val = ReflectUtil.invoke(e, ((Method) os[1]));
                    }
                    if (StrUtil.isNotBlank(ef.value())) {
                        val = ReflectUtil.getFieldValue(val, ef.value());
                    }
                    // If is dict, get dict label
                    if (ef.dictType().length > 0 && ef.dictValue().length > 0) {
                        List<String> keys = keysMap.get(ef);
                        if (null == keys) {
                            keys = ListUtil.toList(ef.dictType());
                            keysMap.put(ef, keys);
                        }
                        int keyIndex = keys.indexOf(val.toString());
                        if (keyIndex < 0) {
                            val = "";
                        } else {
                            val = ef.dictValue()[keyIndex];
                        }
                    }
                } catch (Exception ex) {
                    // Failure to ignore
                    log.info(ex.toString());
                    val = "";
                }
                this.addCell(row, colunm++, val, ef.align(), ef.fieldType(), ef.dateFormat());
            }
        }
        return this;
    }

    /**
     * 输出数据流
     *
     * @param os 输出数据流
     */
    public ExportExcel write(OutputStream os) throws IOException {
        hsw.write(os);
        return this;
    }

    /**
     * 输出到客户端
     *
     * @param fileName 输出文件名
     */
    public ExportExcel write(HttpServletResponse response, String fileName) throws Exception {
        response.reset();
        response.setContentType("application/force-download");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "utf-8"));
        write(response.getOutputStream());
        return this;
    }

    /**
     * 输出到文件
     *
     * @param name 输出文件名
     */
    public ExportExcel writeFile(String name) throws FileNotFoundException, IOException {
        FileOutputStream os = new FileOutputStream(name);
        this.write(os);
        os.close();
        return this;
    }

    /**
     * 清理临时文件
     */
    public ExportExcel dispose() {
        return this;
    }

}
