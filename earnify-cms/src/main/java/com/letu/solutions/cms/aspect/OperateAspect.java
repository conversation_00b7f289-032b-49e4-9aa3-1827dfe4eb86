package com.letu.solutions.cms.aspect;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.letu.solutions.cms.service.sys.SysOperateService;
import com.letu.solutions.core.config.ExtendDataConverter;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;

@Aspect
@RequiredArgsConstructor
@Component
@Slf4j
public class OperateAspect {
    private final ExtendDataConverter extendDataConverter;
    @Resource
    @Lazy
    private SysOperateService sysOperateService;

    /**
     * ..表示包及子包 该方法代表controller层的所有方法
     *
     * @date 2020/9/21
     */

    @Pointcut("execution(* com.letu.solutions.cms.controller..*.*(..))")
    public void controllerMethod() {
    }


    @AfterReturning(value = "controllerMethod()", returning = "r")
    public void doAfter(JoinPoint joinPoint, R r) throws Throwable {
        try {
            String result = loadResult(r);
            sysOperateService.saveLog(sysOperateService.loadDate(loadExtendData(), joinPoint, result, r.isOk(), null, false));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(pointcut = "controllerMethod()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
        try {
            String error = loadError(e);
            sysOperateService.saveLog(sysOperateService.loadDate(loadExtendData(), joinPoint, null, false, error, true));
        } catch (Exception exception) {
            log.error("接口throw日志记录发生异常", e);
        }
    }

    /**
     * 拼接出参
     */
    private String loadResult(R r) {
        return StrUtil.sub(JSONObject.toJSONString(r.getData(), JSONWriter.Feature.MapSortField), 0, 2000);
    }

    private ExtendData loadExtendData() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            String extendDataStr = request.getHeader("extendData");
            if (StrUtil.isNotEmpty(extendDataStr)) {
                return extendDataConverter.convert(extendDataStr);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }

    }

    private String loadError(Exception e) {
        return StrUtil.sub(e.getMessage(), 0, 2000);

    }
}