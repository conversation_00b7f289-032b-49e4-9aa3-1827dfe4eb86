package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.PlatformTransactionBillListReq;
import com.letu.solutions.model.cms.request.cms.PlatformTransactionBillSaveReq;
import com.letu.solutions.model.cms.request.cms.PlatformTransactionBillUpdateReq;
import com.letu.solutions.model.cms.response.cms.PlatformTransactionBillPageRes;
import com.letu.solutions.model.cms.response.cms.PlatformTransactionBillDetailRes;
import com.letu.solutions.model.dto.PlatformTransactionBillDto;
import com.letu.solutions.model.entity.cms.PlatformTransactionBill;

/**
 * 平台账户流水表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface PlatformTransactionBillService {
    Page<PlatformTransactionBillPageRes> selectBasicPage(Page<PlatformTransactionBill> page, PlatformTransactionBillListReq request);

    List<PlatformTransactionBillPageRes> selectBasicList(PlatformTransactionBillListReq request);

    PlatformTransactionBillDetailRes selectByIdBasic(Long id);

    boolean saveBasic(PlatformTransactionBillSaveReq record, ExtendData extendData);

    boolean updateBasic(PlatformTransactionBillUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    void createPlatformTransactionBill(PlatformTransactionBillDto bill, ExtendData extendData);

    String exportExcel(PlatformTransactionBillListReq request);
}
