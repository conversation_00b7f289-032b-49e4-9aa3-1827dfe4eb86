package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.TaskStep;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.TaskStepListReq;
import com.letu.solutions.model.cms.request.cms.TaskStepSaveReq;
import com.letu.solutions.model.cms.request.cms.TaskStepUpdateReq;
import com.letu.solutions.model.cms.response.cms.TaskStepPageRes;
import com.letu.solutions.model.cms.response.cms.TaskStepDetailRes;

/**
 * 步骤 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface TaskStepService {
    Page<TaskStepPageRes> selectBasicPage(Page<TaskStep> page, TaskStepListReq request);

    List<TaskStepPageRes> selectBasicList(TaskStepListReq request);

    TaskStepDetailRes selectByIdBasic(Long id);

    boolean saveBasic(TaskStepSaveReq record, ExtendData extendData);

    boolean updateBasic(TaskStepUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);
}
