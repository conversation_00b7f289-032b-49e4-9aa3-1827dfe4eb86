package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.mapper.sys.SysMenuMapper;
import com.letu.solutions.cms.mapper.sys.SysUserRoleRelMapper;
import com.letu.solutions.cms.service.sys.SysMenuService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysMenuSaveReq;
import com.letu.solutions.model.cms.request.sys.SysMenuUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysMenuDetail;
import com.letu.solutions.model.cms.response.sys.SysMenuVo;
import com.letu.solutions.model.entity.sys.SysMenu;
import com.letu.solutions.model.entity.sys.SysUserRoleRel;
import com.letu.solutions.util.util.TreeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 系统权限（菜单） 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {
    private final SysUserRoleRelMapper sysUserRoleRelMapper;

    @Override
    public boolean saveMenu(SysMenuSaveReq request) throws RuntimeException {
        if (request.getParentId() == null) {
            request.setParentId(0L);
        }
        SysMenu sysMenu = BeanUtil.copyProperties(request, SysMenu.class);
        if (sysMenu.getSort() == null || sysMenu.getSort() == 0) {
            SysMenu maxMenu = baseMapper.selectOne(Wrappers.<SysMenu>lambdaQuery()
                    .eq(SysMenu::getParentId, request.getParentId())
                    .orderByDesc(SysMenu::getSort)
                    .last("limit 1")
            );
            sysMenu.setSort(ObjectUtil.isNotNull(maxMenu) ? maxMenu.getSort() + 1 : 1);
        }
        return baseMapper.insert(sysMenu) > 0;
    }

    @Override
    public boolean updateMenu(SysMenuUpdateReq request) throws RuntimeException {
        SysMenu sysMenu = BeanUtil.copyProperties(request, SysMenu.class);
        return baseMapper.updateById(sysMenu) > 0;
    }

    @Override
    public SysMenuDetail detail(Long menuId) {
        SysMenu sysMenu = baseMapper.selectById(menuId);
        Assert.notNull(sysMenu, "菜单不存在");
        return BeanUtil.copyProperties(sysMenu, SysMenuDetail.class);
    }

    @Override
    public List<SysMenuDetail> getMenuList() {
        List<SysMenuDetail> detailList = baseMapper.detailList(null);
        return TreeUtil.listToTree(detailList);
    }

    @Override
    public List<Long> getRoleMenuList(Long roleId) {
        return Collections.emptyList();
    }

    @Override
    public List<SysMenuVo> selectUserMenus(Long userId) {
        // 超管拥有所有权限
        SysUserRoleRel sysUserRoleRel = sysUserRoleRelMapper.selectOne(
                Wrappers.<SysUserRoleRel>lambdaQuery()
                        .eq(SysUserRoleRel::getUserId, userId)
                        .eq(SysUserRoleRel::getRoleId, 1L)
                        .last("limit 1"));
        if (null != sysUserRoleRel) {
            return TreeUtil.listToTree(baseMapper.allVoList());
        }
        // 普通用户正常查询
        List<SysMenuVo> userVoList = baseMapper.userVoList(userId);
        return TreeUtil.listToTree(userVoList);
    }

    @Override
    public Boolean basicDelete(String menuId, ExtendData extendData) {
        baseMapper.deleteById(menuId);
        return Boolean.TRUE;
    }
}