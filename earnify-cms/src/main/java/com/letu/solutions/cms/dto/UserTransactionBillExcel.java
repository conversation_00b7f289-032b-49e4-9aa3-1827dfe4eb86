package com.letu.solutions.cms.dto;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.cms.aspect.ExcelField;

@Data
public class UserTransactionBillExcel implements Serializable {
    @ExcelField(title = "流水ID")
    private Long id;
    @ExcelField(title = "用户ID")
    private Long userId;
    @ExcelField(title = "用户名")
    private String userName;
    @ExcelField(title = "账号类型")
    private String accountType;
    @ExcelField(title = "动账类型")
    private String fundType;
    @ExcelField(title = "方向")
    private String side;
    @ExcelField(title = "冻结方向")
    private String frozenSide;
    @ExcelField(title = "动账数量")
    private BigDecimal amount;
    @ExcelField(title = "变动前金额")
    private BigDecimal balanceBefore;
    @ExcelField(title = "变动后金额")
    private BigDecimal balanceAfter;
    @ExcelField(title = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 