package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.mapper.sys.SysUserMapper;
import com.letu.solutions.cms.service.sys.SysUserService;
import com.letu.solutions.dubbo.middle.cms.CmsFacade;
import com.letu.solutions.dubbo.middle.cms.dto.SysUserDto;
import com.letu.solutions.dubbo.middle.cms.req.SysUserQueryReq;
import com.letu.solutions.model.cms.response.sys.SysUserResponse;
import com.letu.solutions.model.entity.sys.SysUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 后台系统用户表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    @DubboReference(check = false)
    private CmsFacade cmsFacade;

    @Override
    public Page<SysUserDto> selectBasicPage(Page<SysUser> page, SysUserQueryReq request) {
        return cmsFacade.sysUserPage(request);
    }

    @Override
    public List<SysUserDto> selectBasicList(SysUserQueryReq request) {
        return cmsFacade.sysUserList(request);
    }

    @Override
    public SysUserResponse selectByIdBasic(Long id) {
        return BeanUtil.copyProperties(cmsFacade.sysUserDetail(id), SysUserResponse.class);
    }

}