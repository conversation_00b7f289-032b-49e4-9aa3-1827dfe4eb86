package com.letu.solutions.cms.service.sys;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysRoleInterfaceRelSaveReq;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 系统角色-接口关系 服务类
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface SysRoleInterfaceRelService {

    List<String> selectRoleInterface(Long roleId);

    boolean saveRoleInterface(SysRoleInterfaceRelSaveReq record, ExtendData extendData);

    void refreshAllRoleUser(Long roleId);
}
