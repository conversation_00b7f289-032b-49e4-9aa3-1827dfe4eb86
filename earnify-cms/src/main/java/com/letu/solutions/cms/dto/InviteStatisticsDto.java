package com.letu.solutions.cms.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 邀请统计数据DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InviteStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 直推人数
     */
    private Integer directInviteCount;

    /**
     * 总推广人数（包含间推）
     */
    private Integer totalInviteCount;
}
