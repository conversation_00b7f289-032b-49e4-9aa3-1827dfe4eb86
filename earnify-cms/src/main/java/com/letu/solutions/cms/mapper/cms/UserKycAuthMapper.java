package com.letu.solutions.cms.mapper.cms;

import com.letu.solutions.model.entity.cms.UserKycAuth;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;

import java.util.List;
import java.util.Map;

/**
 * KYC身份认证表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper
public interface UserKycAuthMapper extends BaseMapper<UserKycAuth> {

    /**
     * 批量查询用户的KYC认证状态
     * @param userIdList 用户ID列表字符串
     * @param accountRole 账户角色（乙方用户）
     * @return 用户KYC认证状态映射
     */
    @Select("""
            SELECT
                user_id as userId,
                verify_status as verifyStatus
            FROM user_kyc_auth
            WHERE user_id IN (${userIdList})
            AND account_role = #{accountRole}
            """)
    List<Map<String, Object>> getVerifyStatusByUserIds(@Param("userIdList") String userIdList,
                                                        @Param("accountRole") String accountRole);
}
