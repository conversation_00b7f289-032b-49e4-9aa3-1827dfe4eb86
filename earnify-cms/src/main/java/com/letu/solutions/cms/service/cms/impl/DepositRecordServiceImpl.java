package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.cms.service.user.UserService;
import com.letu.solutions.cms.validator.FinanceConfigValidator;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.model.cms.request.cms.*;
import com.letu.solutions.model.cms.response.user.UserDetailRes;
import com.letu.solutions.model.dto.PlatformTransactionBillDto;
import com.letu.solutions.model.dto.UserTransactionBillDto;
import com.letu.solutions.model.entity.cms.DepositRecord;
import com.letu.solutions.cms.mapper.cms.DepositRecordMapper;
import com.letu.solutions.cms.service.cms.DepositRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.util.constants.CurrencyConstants;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.response.cms.DepositRecordPageRes;
import com.letu.solutions.model.cms.response.cms.DepositRecordDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import com.letu.solutions.core.configuration.AccountConfiguration;
import org.springframework.transaction.support.TransactionTemplate;
import com.letu.solutions.cms.dto.DepositRecordExcel;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.service.cms.FinanceConfigService;
import com.letu.solutions.cms.validator.PlatformAddressValidator;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressWithConfigRes;

/**
 * 用户充值记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DepositRecordServiceImpl extends ServiceImpl<DepositRecordMapper, DepositRecord> implements DepositRecordService {
    private final UserService userService;
    private final com.letu.solutions.cms.service.cms.UserAccountBalanceService userAccountBalanceService;
    private final com.letu.solutions.cms.service.cms.UserTransactionBillService userTransactionBillService;
    private final com.letu.solutions.cms.service.cms.PlatformTransactionBillService platformTransactionBillService;
    private final AccountConfiguration accountConfiguration;
    private final TransactionTemplate transactionTemplate;
    private final FinanceConfigValidator financeConfigValidator;
    private final FinanceConfigService financeConfigService;

    @Override
    public Page<DepositRecordPageRes> selectBasicPage(Page<DepositRecord> page, DepositRecordListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<DepositRecord> queryWrapper = Wrappers.<DepositRecord>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),DepositRecord::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getUserId()),DepositRecord::getUserId, request.getUserId())
                .eq(ObjectUtil.isNotEmpty(request.getUsername()),DepositRecord::getUsername, request.getUsername())
                .eq(ObjectUtil.isNotEmpty(request.getNetwork()),DepositRecord::getNetwork, request.getNetwork())
                .ge(ObjectUtil.isNotEmpty(request.getBeginDate()), DepositRecord::getDay, request.getBeginDate())
                .le(ObjectUtil.isNotEmpty(request.getEndDate()), DepositRecord::getDay, request.getEndDate());
        Page<DepositRecord> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, DepositRecordPageRes.class);
        }



    @Override
    public List<DepositRecordPageRes> selectBasicList(DepositRecordListReq request) {
        LambdaQueryWrapper<DepositRecord> queryWrapper = Wrappers.<DepositRecord>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()), DepositRecord::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getUserId()), DepositRecord::getUserId, request.getUserId())
                .eq(ObjectUtil.isNotEmpty(request.getUsername()), DepositRecord::getUsername, request.getUsername())
                .eq(ObjectUtil.isNotEmpty(request.getNetwork()), DepositRecord::getNetwork, request.getNetwork())
                .ge(ObjectUtil.isNotEmpty(request.getStartDateTime()), DepositRecord::getCreateTime, request.getStartDateTime())
                .le(ObjectUtil.isNotEmpty(request.getEndDateTime()), DepositRecord::getCreateTime, request.getEndDateTime());
        List<DepositRecord> basicList = baseMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(basicList, DepositRecordPageRes.class);
    }

    @Override
    public DepositRecordDetailRes selectByIdBasic(Long id) {
        DepositRecord record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, DepositRecordDetailRes.class);
    }

    @Override
    public boolean saveBasic(DepositRecordSaveReq record, ExtendData extendData) {
        checkPlatformUser(record.getUserId());
        // 校验充值金额
        financeConfigValidator.validateDeposit(record.getNetwork(), record.getAmount());
        UserDetailRes user = userService.selectByIdBasic(record.getUserId());
        // 校验充值地址
        List<String> addressList = financeConfigService.selectByNetwork(record.getNetwork()).getAddressList();
        PlatformAddressValidator.validateAddressInConfig(record.getToAddress(), addressList);
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户不存在");

        log.info("[充值] 开始插入充值记录, userId={}, amount={}", record.getUserId(), record.getAmount());

        // 事务内执行：插入充值记录、资金账户变动、流水写入
        return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            DepositRecord saveRecord = BeanUtil.copyProperties(record, DepositRecord.class);
            saveRecord.setUsername(user.getNickName());
            saveRecord.setCurrency(CurrencyConstants.COIN_USDT);
            saveRecord.setOperator(extendData.getAuthentication().getCmsUserInfo().getUserName());
            boolean insertResult = baseMapper.insert(saveRecord) > 0;
            Assert.isTrue(insertResult, "充值记录插入失败");
            log.info("[充值] 充值记录插入成功, depositId={}, userId={}, amount={}", saveRecord.getId(), record.getUserId(), record.getAmount());

            // 处理用户资金账户，返回前后余额
            BalanceChangeResult balanceResult = handleUserAccountBalance(record, extendData);

            // 写入用户资金流水
            writeUserTransactionBillUser(record, user, saveRecord.getId(), balanceResult, extendData);
            // 写入平台流水
            writePlatformTransactionBill(record, user, saveRecord.getId(), balanceResult, extendData);
            // 平台账户余额变动
            writePlatformAccountBalance(record.getAmount(), extendData);

            return true;
        }));
    }

    /**
     * 校验是否为平台账户
     */
    private void checkPlatformUser(Long userId) {
        if (userId != null && userId.equals(accountConfiguration.getPlatformUserId())) {
            throw new ThrowException("平台账户不允许操作");
        }
    }

    /**
     * 处理用户资金账户，返回前后余额
     */
    private BalanceChangeResult handleUserAccountBalance(DepositRecordSaveReq record, ExtendData extendData) {
        var userAccountBalance = userAccountBalanceService
            .getBaseMapper().selectOne(new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, record.getUserId())
                .eq(UserAccountBalance::getCurrency, CurrencyConstants.COIN_USDT));
        var beforeAmount = java.math.BigDecimal.ZERO;
        BigDecimal afterAmount;
        if (userAccountBalance == null) {
            var saveReq = new UserAccountBalanceSaveReq();
            saveReq.setUserId(record.getUserId());
            saveReq.setAvailableAmount(record.getAmount());
            saveReq.setFrozenAmount(java.math.BigDecimal.ZERO);
            saveReq.setCurrency(CurrencyConstants.COIN_USDT);
            userAccountBalanceService.saveBasic(saveReq, extendData);
            afterAmount = record.getAmount();
            log.info("[充值] 新建用户资金账户, userId={}, amount={}", record.getUserId(), record.getAmount());
        } else {
            beforeAmount = userAccountBalance.getAvailableAmount();
            afterAmount = beforeAmount.add(record.getAmount());
            var updateReq = new UserAccountBalanceUpdateReq();
            updateReq.setId(userAccountBalance.getId());
            updateReq.setUserId(userAccountBalance.getUserId());
            updateReq.setAvailableAmount(afterAmount);
            updateReq.setFrozenAmount(userAccountBalance.getFrozenAmount());
            updateReq.setCurrency(CurrencyConstants.COIN_USDT);
            userAccountBalanceService.updateBasic(updateReq, extendData);
            log.info("[充值] 更新用户资金账户, userId={}, beforeAmount={}, afterAmount={}", record.getUserId(), beforeAmount, afterAmount);
        }
        return new BalanceChangeResult(beforeAmount, afterAmount);
    }

    /**
     * 写入用户资金流水
     */
    private void writeUserTransactionBillUser(DepositRecordSaveReq record, UserDetailRes user, Long depositId, BalanceChangeResult balanceResult, ExtendData extendData) {
        // 用户流水
        UserTransactionBillDto userBill = UserTransactionBillDto.builder()
                .userId(record.getUserId())
                .userName(user.getNickName())
                .accountType(AccountTypeEnum.client)
                .fundType(FundTypeEnum.deposit)
                .side(FundSideTypeEnum.in)
                .amount(record.getAmount())
                .currency(CurrencyConstants.COIN_USDT)
                .before(balanceResult.getBeforeAmount())
                .after(balanceResult.getAfterAmount())
                .remark("充值入账")
                .build();
        userTransactionBillService.createUserTransactionBill(userBill, extendData);
        log.info("[充值] 资金流水写入成功, userId={}, amount={}, beforeAmount={}, afterAmount={}, billType=充值, billId={}", record.getUserId(), record.getAmount(), balanceResult.getBeforeAmount(), balanceResult.getAfterAmount(), depositId);
    }

    /**
     * 平台账户余额变动：如无则新建，有则累加
     */
    private void writePlatformAccountBalance(BigDecimal amount, ExtendData extendData) {
        var platformUserId = accountConfiguration.getPlatformUserId();
        var currency = CurrencyConstants.COIN_USDT;
        var platformAccount = userAccountBalanceService.getBaseMapper().selectOne(
            new LambdaQueryWrapper<UserAccountBalance>()
                .eq(UserAccountBalance::getUserId, platformUserId)
                .eq(UserAccountBalance::getCurrency, currency)
        );
        if (platformAccount == null) {
            var saveReq = new UserAccountBalanceSaveReq();
            saveReq.setUserId(platformUserId);
            saveReq.setAvailableAmount(amount);
            saveReq.setFrozenAmount(java.math.BigDecimal.ZERO);
            saveReq.setCurrency(currency);
            userAccountBalanceService.saveBasic(saveReq, extendData);
            log.info("[充值] 新建平台账户余额, platformUserId={}, amount={}", platformUserId, amount);
        } else {
            var updateReq = new UserAccountBalanceUpdateReq();
            updateReq.setId(platformAccount.getId());
            updateReq.setUserId(platformUserId);
            updateReq.setAvailableAmount(platformAccount.getAvailableAmount().add(amount));
            updateReq.setFrozenAmount(platformAccount.getFrozenAmount());
            updateReq.setCurrency(currency);
            userAccountBalanceService.updateBasic(updateReq, extendData);
            log.info("[充值] 更新平台账户余额, platformUserId={}, beforeAmount={}, afterAmount={}", platformUserId, platformAccount.getAvailableAmount(), platformAccount.getAvailableAmount().add(amount));
        }
    }

    /**
     * 写入平台资金流水
     */
    private void writePlatformTransactionBill(DepositRecordSaveReq record, UserDetailRes user, Long depositId, BalanceChangeResult balanceResult, ExtendData extendData) {

        PlatformTransactionBillDto platformBill = PlatformTransactionBillDto.builder()
                .userId(record.getUserId())
                .userName(user.getNickName())
                .accountType(AccountTypeEnum.client)
                .fundType(FundTypeEnum.deposit)
                .side(FundSideTypeEnum.in)
                .amount(record.getAmount())
                .currency(CurrencyConstants.COIN_USDT)
                .fee(BigDecimal.ZERO)
                .before(balanceResult.getBeforeAmount())
                .after(balanceResult.getAfterAmount())
                .fundId(depositId)
                .remark("充值入账")
                .platformUserId(accountConfiguration.getPlatformUserId())
                .build();
        platformTransactionBillService.createPlatformTransactionBill(platformBill, extendData);
        log.info("[充值] 平台流水写入成功, userId={}, amount={}, beforeAmount={}, afterAmount={}, billType=充值, billId={}", record.getUserId(), record.getAmount(), balanceResult.getBeforeAmount(), balanceResult.getAfterAmount(), depositId);
    }

    /**
     * 资金变动结果结构体
     */
    private static class BalanceChangeResult {
        private final java.math.BigDecimal beforeAmount;
        private final java.math.BigDecimal afterAmount;
        public BalanceChangeResult(java.math.BigDecimal beforeAmount, java.math.BigDecimal afterAmount) {
            this.beforeAmount = beforeAmount;
            this.afterAmount = afterAmount;
        }
        public java.math.BigDecimal getBeforeAmount() { return beforeAmount; }
        public java.math.BigDecimal getAfterAmount() { return afterAmount; }
    }

    @Override
    public boolean updateBasic(DepositRecordUpdateReq record, ExtendData extendData) {
        DepositRecord updateRecord = BeanUtil.copyProperties(record, DepositRecord.class);
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public String exportExcel(DepositRecordListReq request) {
        List<DepositRecordPageRes> data = selectBasicList(request);
        List<DepositRecordExcel> excelList = BeanUtil.copyToList(data, DepositRecordExcel.class);
        // 拼接文件名
        StringBuilder fileName = new StringBuilder("充值记录");
        if (request.getStartDateTime() != null || request.getEndDateTime() != null) {
            fileName.append("_");
            if (request.getStartDateTime() != null && !request.getStartDateTime().isEmpty()) {
                String start = request.getStartDateTime().replaceAll("[:\\s-]", "");
                fileName.append(start.length() >= 8 ? start.substring(0, 8) : start);
            }
            fileName.append("-");
            if (request.getEndDateTime() != null && !request.getEndDateTime().isEmpty()) {
                String end = request.getEndDateTime().replaceAll("[:\\s-]", "");
                fileName.append(end.length() >= 8 ? end.substring(0, 8) : end);
            }
        }
        if (ObjectUtil.isNotEmpty(request.getUsername())) {
            fileName.append("_").append(request.getUsername());
        }
        if (ObjectUtil.isNotEmpty(request.getUserId())) {
            fileName.append("_UID").append(request.getUserId());
        }
        if (ObjectUtil.isNotEmpty(request.getId())) {
            fileName.append("_ID").append(request.getId());
        }
        if (ObjectUtil.isNotEmpty(request.getNetwork())) {
            fileName.append("_").append(request.getNetwork());
        }
        fileName.append(".xlsx");
        return ExcelUtil.getExcel(fileName.toString(), DepositRecordExcel.class, excelList, false);
    }
}