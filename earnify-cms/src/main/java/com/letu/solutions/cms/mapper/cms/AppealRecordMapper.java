package com.letu.solutions.cms.mapper.cms;

import com.letu.solutions.cms.dto.AppealRecordExcel;
import com.letu.solutions.model.entity.cms.AppealRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.cms.request.cms.AppealRecordListReq;
import com.letu.solutions.model.cms.response.cms.AppealRecordPageRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 申述记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper

public interface AppealRecordMapper extends BaseMapper<AppealRecord> {
    Page<AppealRecordPageRes> selectBasicPage(@Param("page") Page page, @Param("req") AppealRecordListReq request);

    List<AppealRecordExcel> selectBasicList(@Param("req")AppealRecordListReq request);
}
