package com.letu.solutions.cms.service.sys;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysOperateListReq;
import com.letu.solutions.model.cms.response.sys.SysOperatePageRes;
import com.letu.solutions.model.entity.sys.SysOperate;
import com.letu.solutions.model.request.BaseRequest;
import org.aspectj.lang.JoinPoint;

/**
 * 系统-操作日志 服务类
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface SysOperateService {
    Page<SysOperatePageRes> selectBasicPage(Page<SysOperate> page, SysOperateListReq request);

    SysOperate loadDate(ExtendData extendData, JoinPoint joinPoint, String result, boolean isOk, String error, boolean isError);

    void saveLog(SysOperate record);

    Pair<String, String> loadPreAuth(JoinPoint joinPoint, boolean check);
}
