package com.letu.solutions.cms.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.sys.SysOperateService;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.cms.request.sys.SysOperateListReq;
import com.letu.solutions.model.cms.response.sys.SysOperatePageRes;
import com.letu.solutions.model.entity.sys.SysOperate;
import com.letu.solutions.model.request.BaseRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统/系统-操作日志
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "sys:operate", valueDesc = "系统:操作日志")
public class SysOperateController {
    private final SysOperateService basicService;

    /**
     * 系统-操作日志 分页查询
     */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/sysOperate/page")
    public R<Page<SysOperatePageRes>> loadPage(SysOperateListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

}
