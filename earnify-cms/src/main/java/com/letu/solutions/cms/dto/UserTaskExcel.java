package com.letu.solutions.cms.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import com.letu.solutions.cms.aspect.ExcelField;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import lombok.Data;

/**
 * 任务记录
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class UserTaskExcel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务id
     */
    @ExcelField(title = "任务ID")
    private Long taskId;
    /**
     * 任务状态 枚举
     */
    @ExcelField(title = "任务状态",
            dictType = {"ended", "incomplete", "completed", "appealInProgress", "pendingApproval", "appealNotPass"},
            dictValue = {"已结束", "未完成", "已完成", "申诉中","待审核","申诉不通过"}
    )
    private UserTaskStatusEnum state;
    /** 任务名称 */
    @ExcelField(title = "任务名称")
    private String taskName;
    /** 甲方账号昵称 */
    @ExcelField(title = "甲方账号昵称")
    private String partyANickName;
    /** 甲方id */
    @ExcelField(title = "甲方id")
    private Long partyAId;
    /** 乙方账号昵称 */
    @ExcelField(title = "乙方账号昵称")
    private String partyBNickName;
    /** 乙方id */
    @ExcelField(title = "乙方id")
    private Long partyBId;
    /** 关联产品id */
    @ExcelField(title = "关联产品id")
    private Long productId;
    /** 产品名称 */
    @ExcelField(title = "产品名称")
    private String productName;
    /** 任务属性 */
    @ExcelField(title = "任务属性")
    private Integer attribute;
    /** 任务类型 */
    @ExcelField(title = "任务类型",
            dictType = {"task", "nft"},
            dictValue = {"任务", "链游"}
    )
    private TaskTypeEnum taskType;
    /** 是否排重 */
    @ExcelField(title = "是否排重")
    private Integer weightSorting;
    /** 完成进度 */
    @ExcelField(title = "完成进度")
    private String progress;
    /** 任务奖励 */
    @ExcelField(title = "任务奖励")
    private BigDecimal price;
    /** 任务完成时限 */
    @ExcelField(title = "任务完成时限")
    private Integer time;
    @ExcelField(title = "任务接手时间")
    private String createTime;
    @ExcelField(title = "任务审核时间")
    private String examineTime;
}
