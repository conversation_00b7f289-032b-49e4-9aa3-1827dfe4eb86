package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import com.letu.solutions.cms.mapper.cms.FinanceConfigMapper;
import com.letu.solutions.cms.service.cms.FinanceConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.util.constants.CurrencyConstants;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.FinanceConfigListReq;
import com.letu.solutions.model.cms.request.cms.FinanceConfigSaveReq;
import com.letu.solutions.model.cms.request.cms.FinanceConfigUpdateReq;
import com.letu.solutions.model.cms.response.cms.FinanceConfigPageRes;
import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Set;
import java.util.stream.Collectors;
import com.letu.solutions.cms.service.cms.PlatformWalletAddressService;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressSaveReq;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressDetailRes;
import com.letu.solutions.cms.mapper.cms.PlatformWalletAddressMapper;
import com.letu.solutions.model.entity.cms.PlatformWalletAddress;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressWithConfigRes;
import com.letu.solutions.cms.util.FinanceConfigCacheUtil;
import com.letu.solutions.cms.util.FinanceConfigCacheUtil.FinanceConfigCacheDTO;

/**
 * 财务配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FinanceConfigServiceImpl extends ServiceImpl<FinanceConfigMapper, FinanceConfig> implements FinanceConfigService {
    private final PlatformWalletAddressService platformWalletAddressService;
    private final PlatformWalletAddressMapper platformWalletAddressMapper;
    private final FinanceConfigCacheUtil financeConfigCacheUtil;
    private final TransactionTemplate transactionTemplate;

    @Override
    public Page<FinanceConfigPageRes> selectBasicPage(Page<FinanceConfig> page, FinanceConfigListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<FinanceConfig> queryWrapper = Wrappers.<FinanceConfig>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),FinanceConfig::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getNetwork()),FinanceConfig::getNetwork, request.getNetwork())
                .eq(ObjectUtil.isNotEmpty(request.getCurrency()),FinanceConfig::getCurrency, request.getCurrency())
                .eq(ObjectUtil.isNotEmpty(request.getMinAmount()),FinanceConfig::getMinAmount, request.getMinAmount())
                .eq(ObjectUtil.isNotEmpty(request.getMaxAmount()),FinanceConfig::getMaxAmount, request.getMaxAmount())
                .eq(ObjectUtil.isNotEmpty(request.getMinWithdraw()),FinanceConfig::getMinWithdraw, request.getMinWithdraw())
                .eq(ObjectUtil.isNotEmpty(request.getMaxWithdraw()),FinanceConfig::getMaxWithdraw, request.getMaxWithdraw())
                .eq(ObjectUtil.isNotEmpty(request.getMaxWithdrawTimesPerDay()),FinanceConfig::getMaxWithdrawTimesPerDay, request.getMaxWithdrawTimesPerDay())
                .eq(ObjectUtil.isNotEmpty(request.getFeeRate()),FinanceConfig::getFeeRate, request.getFeeRate())
                .eq(ObjectUtil.isNotEmpty(request.getFixedFee()),FinanceConfig::getFixedFee, request.getFixedFee())
                .eq(ObjectUtil.isNotEmpty(request.getRemark()),FinanceConfig::getRemark, request.getRemark());

        Page<FinanceConfig> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, FinanceConfigPageRes.class);
        }



    @Override
    public List<FinanceConfigPageRes> selectBasicList(FinanceConfigListReq request) {
        List<FinanceConfig> basicList = baseMapper.selectList(Wrappers.<FinanceConfig>lambdaQuery());
        return BeanUtil.copyToList(basicList, FinanceConfigPageRes.class);
    }

    @Override
    public FinanceConfigDetailRes selectByIdBasic(Long id) {
        FinanceConfig record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, FinanceConfigDetailRes.class);
    }

    @Override
    public FinanceConfigDetailRes selectByNetwork(String network) {
        FinanceConfigCacheDTO cacheDTO = financeConfigCacheUtil.getConfig(network);
        if (cacheDTO != null && cacheDTO.getConfig() != null) {
            FinanceConfigDetailRes res = BeanUtil.copyProperties(cacheDTO.getConfig(), FinanceConfigDetailRes.class);
            // 直接使用缓存中的地址字符串列表
            res.setAddressList(cacheDTO.getAddressList());
            return res;
        }
        LambdaQueryWrapper<FinanceConfig> query = new LambdaQueryWrapper<>();
        query.eq(FinanceConfig::getNetwork, network);
        FinanceConfig record = baseMapper.selectOne(query);
        FinanceConfigDetailRes res = BeanUtil.copyProperties(record, FinanceConfigDetailRes.class);
        if (record != null) {
            List<PlatformWalletAddressWithConfigRes> addressWithConfigList = platformWalletAddressService.selectByNetwork(network);
            // 转换为地址字符串列表
            List<String> addressList = addressWithConfigList.stream()
                    .map(PlatformWalletAddressWithConfigRes::getAddress)
                    .collect(Collectors.toList());
            res.setAddressList(addressList);
            // 缓存地址字符串列表
            FinanceConfigCacheDTO dto = new FinanceConfigCacheDTO();
            dto.setConfig(record);
            dto.setAddressList(addressList);
            financeConfigCacheUtil.setConfig(network, dto);
        }
        return res;
    }

    @Override
    public FinanceConfig getConfig(String network) {
        FinanceConfigCacheUtil.FinanceConfigCacheDTO cacheDTO = financeConfigCacheUtil.getConfig(network);
        if (cacheDTO != null && cacheDTO.getConfig() != null) {
            return cacheDTO.getConfig();
        }
        LambdaQueryWrapper<FinanceConfig> query = new LambdaQueryWrapper<>();
        query.eq(FinanceConfig::getNetwork, network);
        FinanceConfig record = baseMapper.selectOne(query);
        Assert.isTrue(ObjectUtil.isNotNull(record),"未找到财务配置：" + network );
        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBasic(FinanceConfigSaveReq record, ExtendData extendData) {
        financeConfigCacheUtil.deleteConfig(record.getNetwork());
        FinanceConfig saveRecord = BeanUtil.copyProperties(record, FinanceConfig.class);
        saveRecord.setCurrency(CurrencyConstants.COIN_USDT);
        boolean configResult = baseMapper.insert(saveRecord) > 0;
        boolean addressResult = true;
        if (record.getAddressList() != null && !record.getAddressList().isEmpty()) {
            addressResult = record.getAddressList().stream()
                .peek(address -> address.setNetwork(record.getNetwork()))
                .map(address -> platformWalletAddressService.saveBasic(address, extendData))
                .reduce(true, (a, b) -> a & b);
        }
        // 组装缓存对象
        List<PlatformWalletAddressWithConfigRes> addressWithConfigList = platformWalletAddressService.selectByNetwork(record.getNetwork());
        List<String> addressList = addressWithConfigList.stream()
                .map(PlatformWalletAddressWithConfigRes::getAddress)
                .collect(Collectors.toList());
        FinanceConfigCacheUtil.FinanceConfigCacheDTO dto = new FinanceConfigCacheUtil.FinanceConfigCacheDTO();
        dto.setConfig(saveRecord);
        dto.setAddressList(addressList);
        financeConfigCacheUtil.setConfig(record.getNetwork(), dto);
        return configResult && addressResult;
    }

    @Override
    public boolean updateBasic(FinanceConfigUpdateReq record, ExtendData extendData) {
        // 检查记录是否存在
        FinanceConfig existingRecord = baseMapper.selectById(record.getId());
        Assert.isTrue(ObjectUtil.isNotEmpty(existingRecord), "ID为 " + record.getId() + " 的记录不存在，无法更新");
        // Redis操作：先删除缓存，再重新设置
        financeConfigCacheUtil.deleteConfig(record.getNetwork());

        // 更新金融配置
        // 处理平台钱包地址列表
        // 重新组装缓存对象
        return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            try {
                // 更新金融配置
                FinanceConfig updateRecord = BeanUtil.copyProperties(record, FinanceConfig.class);
                updateRecord.setUpdateTime(DateUtil.date());
                boolean configResult = baseMapper.updateById(updateRecord) > 0;
                Assert.isTrue(configResult, "更新金融配置失败");

                // 处理平台钱包地址列表
                if (!CollectionUtils.isEmpty(record.getAddressList())) {
                    updatePlatformWalletAddresses(record.getNetwork(), record.getAddressList(), extendData);
                }

                // 重新组装缓存对象
                List<PlatformWalletAddressWithConfigRes> addressWithConfigList = platformWalletAddressService.selectByNetwork(record.getNetwork());
                List<String> addressList = addressWithConfigList.stream()
                        .map(PlatformWalletAddressWithConfigRes::getAddress)
                        .collect(Collectors.toList());
                FinanceConfigCacheDTO dto = new FinanceConfigCacheDTO();
                dto.setConfig(updateRecord);
                dto.setAddressList(addressList);
                financeConfigCacheUtil.setConfig(record.getNetwork(), dto);

                return true;
            } catch (Exception e) {
                log.error("更新金融配置失败，ID：{}，错误：{}", record.getId(), e.getMessage(), e);
                status.setRollbackOnly();
                throw e;
            }
        }));
    }

    /**
     * 更新平台钱包地址列表
     * 查询现有地址，对比新地址列表，进行增删改操作
     */
    private void updatePlatformWalletAddresses(String network, List<PlatformWalletAddressSaveReq> addressList, ExtendData extendData) {
        // 1. 查询该网络下的所有现有地址
        List<PlatformWalletAddress> existingAddresses = platformWalletAddressMapper.selectList(
                Wrappers.<PlatformWalletAddress>lambdaQuery()
                        .eq(PlatformWalletAddress::getNetwork, network)
        );

        // 2. 获取现有地址集合
        Set<String> existingAddressSet = existingAddresses.stream()
                .map(PlatformWalletAddress::getAddress)
                .collect(Collectors.toSet());

        // 3. 获取新地址集合
        Set<String> newAddressSet = addressList.stream()
                .map(PlatformWalletAddressSaveReq::getAddress)
                .collect(Collectors.toSet());

        // 4. 删除不在新列表中的地址
        existingAddresses.stream()
                .filter(existing -> !newAddressSet.contains(existing.getAddress()))
                .forEach(address -> {
                    int deleteResult = platformWalletAddressMapper.deleteById(address.getId());
                    Assert.isTrue(deleteResult > 0, "删除钱包地址失败：" + address.getAddress());
                });

        // 5. 添加新地址（不在现有列表中的）
        addressList.stream()
                .filter(newAddress -> !existingAddressSet.contains(newAddress.getAddress()))
                .forEach(addressReq -> {
                    // 确保网络名称一致
                    addressReq.setNetwork(network);

                    // 保存地址
                    boolean saveResult = platformWalletAddressService.saveBasic(addressReq, extendData);
                    Assert.isTrue(saveResult, "保存平台钱包地址失败：" + addressReq.getAddress());
                });

        log.info("更新平台钱包地址列表完成，网络：{}，新地址数量：{}", network, addressList.size());
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }
}