package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.mapper.sys.SysInterfaceMapper;
import com.letu.solutions.cms.mapper.sys.SysUserRoleRelMapper;
import com.letu.solutions.cms.service.sys.SysInterfaceService;
import com.letu.solutions.core.annotation.CacheRedis;
import com.letu.solutions.model.cms.response.sys.SysInterfaceResponse;
import com.letu.solutions.model.cms.response.sys.SysMenuVo;
import com.letu.solutions.model.entity.sys.SysInterface;
import com.letu.solutions.model.entity.sys.SysUserRoleRel;
import com.letu.solutions.model.enums.InterfaceTypeEnum;
import com.letu.solutions.model.request.BaseRequest;
import com.letu.solutions.util.util.TreeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 系统接口 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysInterfaceServiceImpl extends ServiceImpl<SysInterfaceMapper, SysInterface> implements SysInterfaceService {
    private final SysUserRoleRelMapper sysUserRoleRelMapper;

    @Override
    public List<SysInterfaceResponse> selectBasicList(BaseRequest request) {
        List<SysInterface> sysInterfaces = baseMapper.selectList(Wrappers.<SysInterface>lambdaQuery().orderByAsc(SysInterface::getNameAll));
        List<SysInterfaceResponse> sysInterfaceResponses = BeanUtil.copyToList(sysInterfaces, SysInterfaceResponse.class);
        Map<String, SysInterfaceResponse> map = new HashMap<>();
        List<SysInterfaceResponse> res = new ArrayList<>();
        for (SysInterfaceResponse sysInterfaceRespons : sysInterfaceResponses) {
            sysInterfaceRespons.setChildren(new ArrayList<>());
            String parentName = parentName(sysInterfaceRespons.getNameAll());
            if (map.containsKey(parentName)) {
                map.get(parentName).getChildren().add(sysInterfaceRespons);
            } else {
                res.add(sysInterfaceRespons);
            }
            map.put(sysInterfaceRespons.getNameAll(), sysInterfaceRespons);
        }
        return res;
    }

    public String parentName(String nameAll) {
        List<String> list = Arrays.asList(nameAll.split(":"));
        if (list.size() > 1) {
            list = list.subList(0, list.size() - 1);
        }
        if (list.size() == 1) {
            return list.get(0);
        }
        return ArrayUtil.join(list.toArray(), ":");
    }

    @Override
    @CacheRedis(expireTime = 60, lockTime = 20)
    public void scan() {
        List<SysInterface> sysInterfaces = scanTree();
        log.info("扫描接口总数:{}", sysInterfaces.size());
        Date now = new Date();
        for (SysInterface record : sysInterfaces) {
            saveOne(record, now);
        }
        baseMapper.delete(Wrappers.<SysInterface>lambdaQuery().lt(SysInterface::getUpdateTime, DateUtil.offset(now, DateField.SECOND, -3)));
    }

    @Override
    public List<String> selectUserInterfaces(Long userId) {
        // 超管拥有所有权限
        SysUserRoleRel sysUserRoleRel = sysUserRoleRelMapper.selectOne(
                Wrappers.<SysUserRoleRel>lambdaQuery()
                        .eq(SysUserRoleRel::getUserId, userId)
                        .eq(SysUserRoleRel::getRoleId, 1L)
                        .last("limit 1"));
        if (null != sysUserRoleRel) {
            return baseMapper.allInterface();
        }
        // 普通用户正常查询
        return baseMapper.userVoList(userId);
    }

    public void saveOne(SysInterface record, Date now) {
        LambdaUpdateWrapper<SysInterface> updateWrapper = Wrappers.<SysInterface>lambdaUpdate()
                .set(SysInterface::getType, record.getType())
                .set(SysInterface::getInterfaceDesc, record.getInterfaceDesc())
                .set(SysInterface::getInterfaceAllDesc, record.getInterfaceAllDesc())
                .set(SysInterface::getUpdateTime, now)
                .eq(SysInterface::getNameAll, record.getNameAll());
        boolean updateRes = baseMapper.update(null, updateWrapper) > 0;
        if (!updateRes) {
            try {
                record.setCreateTime(now);
                record.setUpdateTime(now);
                baseMapper.insert(record);
            } catch (Exception e) {
                baseMapper.update(null, updateWrapper);
            }
        }
    }

    public List<SysInterface> scanTree() {
        ClassPathScanningCandidateComponentProvider provider =
                new ClassPathScanningCandidateComponentProvider(false);
        provider.addIncludeFilter(new AnnotationTypeFilter(Preauthorize.class));
        Set<BeanDefinition> candidateComponents = provider.findCandidateComponents("com.letu.solutions.cms.controller");
        List<SysInterface> treeList = new ArrayList<>();
        Map<String, SysInterface> treeMap = new HashMap<>();
        try {
            for (BeanDefinition candidateComponent : candidateComponents) {
                Class<?> aClass = ClassUtil.getClassLoader().loadClass(candidateComponent.getBeanClassName());
                Preauthorize parentPreAuth = AnnotationUtil.getAnnotationAlias(aClass, Preauthorize.class);
                if (ObjectUtil.isNull(parentPreAuth) || parentPreAuth.disCheck()) {
                    continue;
                }
                String parentValue = parentPreAuth.value();
                String[] split = parentValue.split(splitKey);
                String[] splitDesc = parentPreAuth.valueDesc().split(splitKey);
                int length = split.length;
                Assert.isTrue(length <= 2, "类的接口注释分段长度必须为两段");
                Assert.isTrue(splitDesc.length <= 2, "类的接口注释分段长度必须为两段");
                if (!treeMap.containsKey(split[0])) {
                    SysInterface oneTree = SysInterface.builder()
                            .type(InterfaceTypeEnum.aClass)
                            .name(split[0])
                            .interfaceDesc(splitDesc[0])
                            .nameAll(split[0])
                            .interfaceAllDesc(splitDesc[0])
                            .build();
                    treeMap.put(split[0], oneTree);
                    treeList.add(oneTree);
                }
                if (length == 2) {
                    if (!treeMap.containsKey(parentValue)) {
                        SysInterface twoTree = SysInterface.builder()
                                .type(InterfaceTypeEnum.aClass)
                                .name(split[1])
                                .interfaceDesc(splitDesc[1])
                                .nameAll(parentValue)
                                .interfaceAllDesc(parentPreAuth.valueDesc())
                                .build();
                        treeList.add(twoTree);
                        treeMap.put(parentValue, twoTree);
                    }
                }
                Method[] methods = ClassUtil.getPublicMethods(aClass);
                for (Method method : methods) {
                    Preauthorize annotation = AnnotationUtil.getAnnotation(method, Preauthorize.class);
                    if (ObjectUtil.isNull(annotation) || annotation.disCheck()) {
                        continue;
                    }
                    String nameAll = StrUtil.join(splitKey, parentValue, annotation.value());
                    if (!treeMap.containsKey(nameAll)) {
                        SysInterface threeTree = SysInterface.builder()
                                .type(InterfaceTypeEnum.method)
                                .name(annotation.value())
                                .interfaceDesc(annotation.valueDesc())
                                .nameAll(nameAll)
                                .interfaceAllDesc(StrUtil.join(splitKey, parentPreAuth.valueDesc(), annotation.valueDesc()))
                                .build();
                        treeList.add(threeTree);
                        treeMap.put(nameAll, threeTree);
                    }
                }
            }
        } catch (Exception e) {
            log.error("扫描发生异常", e);
        }
        return treeList;
    }
}