package com.letu.solutions.cms.util;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import java.util.List;
import java.io.Serializable;

@Component
public class FinanceConfigCacheUtil {
    private static final String KEY_PREFIX = "finance:config:";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    public static class FinanceConfigCacheDTO implements Serializable {
        private FinanceConfig config;
        private List<String> addressList;
        public FinanceConfig getConfig() { return config; }
        public void setConfig(FinanceConfig config) { this.config = config; }
        public List<String> getAddressList() { return addressList; }
        public void setAddressList(List<String> addressList) { this.addressList = addressList; }
    }

    public void setConfig(String network, FinanceConfigCacheDTO dto) {
        redisTemplate.opsForValue().set(KEY_PREFIX + network, dto);
    }

    public FinanceConfigCacheDTO getConfig(String network) {
        Object obj = redisTemplate.opsForValue().get(KEY_PREFIX + network);
        if (obj instanceof FinanceConfigCacheDTO) {
            return (FinanceConfigCacheDTO) obj;
        }
        return null;
    }

    public void deleteConfig(String network) {
        redisTemplate.delete(KEY_PREFIX + network);
    }
} 