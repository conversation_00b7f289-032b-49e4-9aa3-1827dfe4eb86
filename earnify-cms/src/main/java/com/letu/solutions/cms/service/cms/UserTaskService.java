package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.cms.request.cms.AppealRecordUpdateReq;
import com.letu.solutions.model.entity.cms.UserTask;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserTaskListReq;
import com.letu.solutions.model.cms.request.cms.UserTaskSaveReq;
import com.letu.solutions.model.cms.request.cms.UserTaskUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserTaskPageRes;
import com.letu.solutions.model.cms.response.cms.UserTaskDetailRes;
import com.letu.solutions.model.entity.cms.UserTaskStep;

/**
 * 任务 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface UserTaskService {
    Page<UserTaskPageRes> selectBasicPage(Page<UserTask> page, UserTaskListReq request);

    String selectBasicList(UserTaskListReq request);

    UserTaskDetailRes selectByIdBasic(Long id,Long partyBUserId);

    boolean updateBasic(UserTaskUpdateReq record);

    List<UserTaskStep> selectUserTask(Long userId,Long taskId);
}
