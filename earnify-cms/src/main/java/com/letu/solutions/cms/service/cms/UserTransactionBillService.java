package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.dto.UserTransactionBillDto;
import com.letu.solutions.model.entity.cms.UserTransactionBill;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserTransactionBillListReq;
import com.letu.solutions.model.cms.request.cms.UserTransactionBillSaveReq;
import com.letu.solutions.model.cms.request.cms.UserTransactionBillUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserTransactionBillPageRes;
import com.letu.solutions.model.cms.response.cms.UserTransactionBillDetailRes;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;

import java.math.BigDecimal;

/**
 * 账户流水表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface UserTransactionBillService {
    Page<UserTransactionBillPageRes> selectBasicPage(Page<UserTransactionBill> page, UserTransactionBillListReq request);

    List<UserTransactionBillPageRes> selectBasicList(UserTransactionBillListReq request);

    UserTransactionBillDetailRes selectByIdBasic(Long id);

    boolean saveBasic(UserTransactionBillSaveReq record, ExtendData extendData);

    boolean updateBasic(UserTransactionBillUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    void createUserTransactionBill(UserTransactionBillDto dto, ExtendData extendData);

    String exportExcel(UserTransactionBillListReq request);
}
