package com.letu.solutions.cms.controller.cms;

import com.letu.solutions.cms.dto.AppealRecordExcel;
import com.letu.solutions.model.cms.response.cms.ProductDetailRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.AppealRecordService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.AppealRecordListReq;
import com.letu.solutions.model.cms.request.cms.AppealRecordSaveReq;
import com.letu.solutions.model.cms.request.cms.AppealRecordUpdateReq;
import com.letu.solutions.model.cms.response.cms.AppealRecordPageRes;
import com.letu.solutions.model.cms.response.cms.AppealRecordDetailRes;

/**
 * 任务/申述记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:appeal_record", valueDesc = "任务:申述记录表")
public class AppealRecordController{
    private final AppealRecordService basicService;
    /**
    * 申述记录表 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/appealRecord/page")
    public R<Page<AppealRecordPageRes>> loadPage(AppealRecordListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }
    /**
     * 申诉详情 通过主键id
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/appealRecord/selectById")
    public R<AppealRecordDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }
    /**
     * 导出
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/appealRecord/list")
    public R<String> loadList(AppealRecordListReq request) {
        return R.success(basicService.selectBasicList(request));
    }
    /**
     * 申述审核
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/appealRecord/update")
    public R<Boolean> update(@Validated @RequestBody AppealRecordUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }


}
