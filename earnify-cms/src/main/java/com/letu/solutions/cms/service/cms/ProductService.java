package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.Product;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.ProductListReq;
import com.letu.solutions.model.cms.request.cms.ProductSaveReq;
import com.letu.solutions.model.cms.request.cms.ProductUpdateReq;
import com.letu.solutions.model.cms.response.cms.ProductPageRes;
import com.letu.solutions.model.cms.response.cms.ProductDetailRes;

/**
 * 产品表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface ProductService {
    Page<ProductPageRes> selectBasicPage(Page<Product> page, ProductListReq request);

    Page<Product> selectNotPage(Page page);

    String selectBasicList(ProductListReq request);

    ProductDetailRes selectByIdBasic(Long id);

    boolean saveBasic(ProductSaveReq record, ExtendData extendData);

    boolean updateBasic(ProductUpdateReq record, ExtendData extendData);

}
