package com.letu.solutions.cms.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.letu.solutions.cms.aspect.ExcelField;
import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import lombok.Data;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class TaskExcel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    @ExcelField(title = "任务ID")
    private Long id;
    /**
     * 任务名称
     */
    @ExcelField(title = "任务名称")
    private String name;
    /**
     * 任务属性 1人工 2自动
     */
    @ExcelField(title = "任务属性",
            dictType = {"1", "2"},
            dictValue = {"人工", "自动"}
    )
    private Integer attribute;
    /**
     * 发布者id
     */
    @ExcelField(title = "发布者id")
    private Long userId;
    /**
     * 发布者名称
     */
    @ExcelField(title = "发布者名称")
    private String nickName;
    /**
     * 产品id
     */
    @ExcelField(title = "产品id")
    private Long productId;
    /**
     * 产品名称
     */
    @ExcelField(title = "产品名称")
    private String productName;
    /**
     * 是否排重 1排重 2不排重
     */
    @ExcelField(title = "是否排重",
            dictType = {"1", "2"},
            dictValue = {"排重", "不排重"}
    )
    private Integer weightSorting;
    /**
     * 任务类型 枚举
     */
    @ExcelField(title = "任务类型",
            dictType = {"task", "nft"},
            dictValue = {"任务", "链游"}
    )
    private TaskTypeEnum taskType;
    /**
     * 任务有效期 天数
     */
    @ExcelField(title = "任务有效期天数")
    private Integer time;
    /**
     * 任务状态 枚举
     */
    @ExcelField(title = "任务状态",
            dictType = {"pendingOrders", "inProgress", "completed", "revoked"},
            dictValue = {"待接单", "进行中", "已完成", "已撤销"}
    )
    private TaskStatusEnum state;
    /**
     * 保证金
     */
    @ExcelField(title = "保证金")
    private Integer bond;
    /**
     * 任务数量
     */
    @ExcelField(title = "任务数量")
    private Integer number;
    /**
     * 任务领取个数
     */
    @ExcelField(title = "任务领取个数")
    private Integer taskReceiveNum;
    /**
     * 任务完成个数
     */
    @ExcelField(title = "任务完成个数")
    private Integer taskFinishNum;

    /**
     * 单价格
     */
    @ExcelField(title = "单价格")
    private BigDecimal price;
    /**
     * 已发放金额
     */
    @ExcelField(title = "已发放金额")
    private BigDecimal grantedAmount;
    /**
     * 冻结中金额
     */
    @ExcelField(title = "冻结中金额")
    private BigDecimal frozenAmount;
    /**
     * 创建时间
     */
    @ExcelField(title = "创建时间")
    private String createTime;
}
