package com.letu.solutions.cms.mapper.cms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.letu.solutions.model.entity.cms.InviteRecord;
import com.letu.solutions.cms.dto.InviteStatisticsDto;
import com.letu.solutions.cms.dto.UserParentInfoDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 邀请记录Mapper
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface InviteRecordMapper extends BaseMapper<InviteRecord> {

    /**
     * 批量查询用户的上级信息
     * @param userIds 用户ID列表
     * @return 用户上级信息DTO列表
     */
    @Select("""
            SELECT
                ir.invitee_id as userId,
                ir.inviter_id as parentUserId,
                u.nick_name as parentUserName
            FROM invite_record ir
            LEFT JOIN user u ON ir.inviter_id = u.id
            WHERE ir.invitee_id IN (${userIdList})
            AND ir.del_flag = 0
            """)
    List<UserParentInfoDto> getParentInfoByUserIds(@Param("userIdList") String userIdList);

    /**
     * 批量查询用户的邀请统计数据（直推和总推广）
     * 使用root_inviter_id字段简化总推广人数统计，不再使用递归查询
     * @param userIdList 用户ID列表字符串
     * @return 邀请统计DTO列表
     */
    @Select("""
            SELECT
                inviter_stats.userId,
                COALESCE(inviter_stats.directInviteCount, 0) as directInviteCount,
                COALESCE(total_stats.totalInviteCount, 0) as totalInviteCount
            FROM (
                -- 直推统计：统计直接邀请的用户数量
                SELECT
                    ir.inviter_id as userId,
                    COUNT(DISTINCT u.id) as directInviteCount
                FROM invite_record ir
                INNER JOIN user u ON ir.invitee_id = u.id
                WHERE ir.inviter_id IN (${userIdList})
                AND ir.del_flag = 0
                AND ir.status = 'registered'
                AND u.account_role = 'provider'
                GROUP BY ir.inviter_id
            ) inviter_stats
            LEFT JOIN (
                -- 总推广统计：使用root_inviter_id字段统计所有下级用户
                SELECT
                    ir.root_inviter_id as userId,
                    COUNT(DISTINCT u.id) as totalInviteCount
                FROM invite_record ir
                INNER JOIN user u ON ir.invitee_id = u.id
                WHERE ir.root_inviter_id IN (${userIdList})
                AND ir.del_flag = 0
                AND ir.status = 'registered'
                AND u.account_role = 'provider'
                GROUP BY ir.root_inviter_id
            ) total_stats ON inviter_stats.userId = total_stats.userId
            """)
    List<InviteStatisticsDto> getInviteStatisticsByUserIds(@Param("userIdList") String userIdList);

}
