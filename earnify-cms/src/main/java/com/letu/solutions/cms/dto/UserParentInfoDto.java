package com.letu.solutions.cms.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 用户上级信息DTO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserParentInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 上级用户ID
     */
    private Long parentUserId;

    /**
     * 上级用户名称
     */
    private String parentUserName;
}
