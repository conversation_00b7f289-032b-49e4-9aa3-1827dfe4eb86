package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.letu.solutions.cms.mapper.cms.*;
import com.letu.solutions.model.entity.cms.DataReportStatistics;
import com.letu.solutions.cms.service.cms.DataReportStatisticsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsListReq;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsSaveReq;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsUpdateReq;
import com.letu.solutions.model.cms.response.cms.DataReportStatisticsPageRes;
import com.letu.solutions.model.cms.response.cms.DataReportStatisticsDetailRes;
import com.letu.solutions.model.cms.request.cms.UserGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.UserGrowthTrendRes;
import com.letu.solutions.model.cms.request.cms.TaskGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.TaskGrowthTrendRes;
import com.letu.solutions.model.cms.request.cms.FundGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.FundGrowthTrendRes;
import com.letu.solutions.model.cms.response.cms.TodoStatisticsRes;
import com.letu.solutions.model.cms.response.cms.FinanceStatisticsRes;
import com.letu.solutions.model.entity.cms.UserKycAuth;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.model.enums.cms.WithdrawStatusEnum;
import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;

import com.letu.solutions.core.configuration.AccountConfiguration;
import com.letu.solutions.model.cms.request.cms.BaseTrendReq;

import java.util.function.Function;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

import com.letu.solutions.core.constant.DateConstants;
import org.springframework.util.Assert;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.letu.solutions.util.util.PageUtil;

/**
 * 数据简报统计 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DataReportStatisticsServiceImpl extends ServiceImpl<DataReportStatisticsMapper, DataReportStatistics> implements DataReportStatisticsService {

    private final UserKycAuthMapper userKycAuthMapper;
    private final WithdrawRecordMapper withdrawRecordMapper;
    private final UserAccountBalanceMapper userAccountBalanceMapper;
    private final PlatformTransactionBillMapper platformTransactionBillMapper;
    private final AccountConfiguration accountConfiguration;
    private final DepositRecordMapper depositRecordMapper;


    @Override
    public Page<DataReportStatisticsPageRes> selectBasicPage(Page<DataReportStatistics> page, DataReportStatisticsListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<DataReportStatistics> queryWrapper = Wrappers.<DataReportStatistics>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),DataReportStatistics::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getStatDate()),DataReportStatistics::getDay, request.getStatDate())
                .eq(ObjectUtil.isNotEmpty(request.getNewUserCountA()),DataReportStatistics::getNewUserCountA, request.getNewUserCountA())
                .eq(ObjectUtil.isNotEmpty(request.getNewUserCountB()),DataReportStatistics::getNewUserCountB, request.getNewUserCountB())
                .eq(ObjectUtil.isNotEmpty(request.getNewUserCount()),DataReportStatistics::getNewUserCount, request.getNewUserCount())
                .eq(ObjectUtil.isNotEmpty(request.getNewProductCount()),DataReportStatistics::getNewProductCount, request.getNewProductCount())
                .eq(ObjectUtil.isNotEmpty(request.getNewTaskCount()),DataReportStatistics::getNewTaskCount, request.getNewTaskCount())
                .eq(ObjectUtil.isNotEmpty(request.getTaskClaimCount()),DataReportStatistics::getTaskClaimCount, request.getTaskClaimCount())
                .eq(ObjectUtil.isNotEmpty(request.getTaskCompleteCount()),DataReportStatistics::getTaskCompleteCount, request.getTaskCompleteCount())
                .eq(ObjectUtil.isNotEmpty(request.getPointsRechargeSuccess()),DataReportStatistics::getPointsRechargeSuccess, request.getPointsRechargeSuccess())
                .eq(ObjectUtil.isNotEmpty(request.getPointsWithdrawSuccess()),DataReportStatistics::getPointsWithdrawSuccess, request.getPointsWithdrawSuccess())
                .eq(ObjectUtil.isNotEmpty(request.getPointsFrozen()),DataReportStatistics::getPointsFrozen, request.getPointsFrozen())
                .eq(ObjectUtil.isNotEmpty(request.getPointsGranted()),DataReportStatistics::getPointsGranted, request.getPointsGranted())
                .eq(ObjectUtil.isNotEmpty(request.getPointsReturned()),DataReportStatistics::getPointsReturned, request.getPointsReturned());

        Page<DataReportStatistics> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, DataReportStatisticsPageRes.class);
        }



    @Override
    public List<DataReportStatisticsPageRes> selectBasicList(DataReportStatisticsListReq request) {
        List<DataReportStatistics> basicList = baseMapper.selectList(Wrappers.<DataReportStatistics>lambdaQuery());
        return BeanUtil.copyToList(basicList, DataReportStatisticsPageRes.class);
    }

    @Override
    public DataReportStatisticsDetailRes selectByIdBasic(Long id) {
        DataReportStatistics record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, DataReportStatisticsDetailRes.class);
    }

    @Override
    public DataReportStatisticsPageRes selectByDate(Integer date) {

        // 构建查询条件，按日期精确查询
        LambdaQueryWrapper<DataReportStatistics> queryWrapper = Wrappers.<DataReportStatistics>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(date),DataReportStatistics::getDay, date)
                .orderByDesc(DataReportStatistics::getDay);

        DataReportStatistics record = baseMapper.selectOne(queryWrapper);
        return record != null ? BeanUtil.copyProperties(record, DataReportStatisticsPageRes.class) : null;
    }

    @Override
    public UserGrowthTrendRes getUserGrowthTrend(UserGrowthTrendReq request) {
        return getTrendData(request, (statisticsMap) -> {
            List<UserGrowthTrendRes.DailyUserGrowthData> dailyGrowthData = new ArrayList<>();

            statisticsMap.forEach((dateStr, stat) -> {
                UserGrowthTrendRes.DailyUserGrowthData dailyData = UserGrowthTrendRes.DailyUserGrowthData.builder()
                        .date(dateStr)
                        .clientGrowth(stat != null ? stat.getNewUserCountA() : 0)
                        .providerGrowth(stat != null ? stat.getNewUserCountB() : 0)
                        .totalGrowth(stat != null ? stat.getNewUserCount() : 0)
                        .build();
                dailyGrowthData.add(dailyData);
            });

            // 获取日期范围
            List<Integer> dates = new ArrayList<>(statisticsMap.keySet());
            Integer startDate = dates.isEmpty() ? 0 : dates.getFirst();
            Integer endDate = dates.isEmpty() ? 0 : dates.getLast();

            return UserGrowthTrendRes.builder()
                    .queryType(request.getQueryType())
                    .startDate(startDate)
                    .endDate(endDate)
                    .dailyGrowthData(dailyGrowthData)
                    .build();
        });
    }

    @Override
    public TaskGrowthTrendRes getTaskGrowthTrend(TaskGrowthTrendReq request) {
        return getTrendData(request, (statisticsMap) -> {
            List<TaskGrowthTrendRes.DailyTaskGrowthData> dailyGrowthData = new ArrayList<>();

            statisticsMap.forEach((dateStr, stat) -> {
                TaskGrowthTrendRes.DailyTaskGrowthData dailyData = TaskGrowthTrendRes.DailyTaskGrowthData.builder()
                        .date(dateStr)
                        .publishedTasks(stat != null ? stat.getNewTaskCount() : 0)
                        .receivedTasks(stat != null ? stat.getTaskClaimCount() : 0)
                        .completedTasks(stat != null ? stat.getTaskCompleteCount() : 0)
                        .build();
                dailyGrowthData.add(dailyData);
            });

            // 获取日期范围
            List<Integer> dates = new ArrayList<>(statisticsMap.keySet());
            Integer startDate = dates.isEmpty() ? 0 : dates.getFirst();
            Integer endDate = dates.isEmpty() ? 0 : dates.getLast();

            return TaskGrowthTrendRes.builder()
                    .queryType(request.getQueryType())
                    .startDate(startDate)
                    .endDate(endDate)
                    .dailyGrowthData(dailyGrowthData)
                    .build();
        });
    }

    @Override
    public FundGrowthTrendRes getFundGrowthTrend(FundGrowthTrendReq request) {
        return getTrendData(request, (statisticsMap) -> {
            List<FundGrowthTrendRes.DailyFundGrowthData> dailyGrowthData = new ArrayList<>();

            statisticsMap.forEach((dateStr, stat) -> {
                FundGrowthTrendRes.DailyFundGrowthData dailyData = FundGrowthTrendRes.DailyFundGrowthData.builder()
                        .date(dateStr)
                        .rechargeAmount(stat != null ? stat.getPointsRechargeSuccess() : BigDecimal.ZERO)
                        .withdrawAmount(stat != null ? stat.getPointsWithdrawSuccess() : BigDecimal.ZERO)
                        .frozenAmount(stat != null ? stat.getPointsFrozen() : BigDecimal.ZERO)
                        .grantedAmount(stat != null ? stat.getPointsGranted() : BigDecimal.ZERO)
                        .build();
                dailyGrowthData.add(dailyData);
            });

            // 获取日期范围
            List<Integer> dates = new ArrayList<>(statisticsMap.keySet());
            Integer startDate = dates.isEmpty() ? 0 : dates.getFirst();
            Integer endDate = dates.isEmpty() ? 0 : dates.getLast();

            return FundGrowthTrendRes.builder()
                    .queryType(request.getQueryType())
                    .startDate(startDate)
                    .endDate(endDate)
                    .dailyGrowthData(dailyGrowthData)
                    .build();
        });
    }

    @Override
    public TodoStatisticsRes getTodoStatistics() {
        // 1. 查询乙方用户身份待审核数量
        Integer providerKycPendingCount = Math.toIntExact(userKycAuthMapper.selectCount(
                Wrappers.<UserKycAuth>lambdaQuery()
                        .eq(UserKycAuth::getVerifyStatus, UserVerifyStatusEnum.pending)
                        .eq(UserKycAuth::getAccountRole, AccountTypeEnum.provider)
        ));

        // 2. 统计提现待处理笔数
        Integer withdrawPendingCount = Math.toIntExact(withdrawRecordMapper.selectCount(
            Wrappers.<WithdrawRecord>lambdaQuery()
                .eq(WithdrawRecord::getWithdrawStatus, WithdrawStatusEnum.unprocessed)
        ));


        return TodoStatisticsRes.builder()
                .providerKycPendingCount(providerKycPendingCount)
                .withdrawPendingCount(withdrawPendingCount)
                .build();
    }

    @Override
    public FinanceStatisticsRes getFinanceStatistics() {
        // 1. 查询平台账户余额（可用+冻结）
        BigDecimal platformAccountBalance = calculatePlatformAccountBalance();

        // 2. 统计用户账户余额
        BigDecimal totalUserAvailableBalance = calculateTotalUserAvailableBalance();
        BigDecimal totalUserFrozenBalance = calculateTotalUserFrozenBalance();

        // 3. 统计业务数据
        BigDecimal totalRewardAmount = sumProviderRewardAmountByDate();
        BigDecimal totalDepositAmount = sumDepositAmountByDate();
        BigDecimal totalWithdrawAmount = sumPointsWithdrawSuccess();

        // 4. 计算汇总数据
        BigDecimal totalUserBalance = totalUserAvailableBalance.add(totalUserFrozenBalance);
        BigDecimal grandTotalBalance = platformAccountBalance.add(totalUserBalance);

        return FinanceStatisticsRes.builder()
                .platformAccountBalance(platformAccountBalance)
                .totalUserAvailableBalance(totalUserAvailableBalance)
                .totalUserFrozenBalance(totalUserFrozenBalance)
                .totalRewardAmount(totalRewardAmount)
                .totalDepositAmount(totalDepositAmount)
                .totalWithdrawAmount(totalWithdrawAmount)
                .totalUserBalance(totalUserBalance)
                .build();
    }

    /**
     * 计算平台账户余额（可用+冻结）
     */
    private BigDecimal calculatePlatformAccountBalance() {
        // 获取平台用户ID
        Long platformUserId = accountConfiguration.getPlatformUserId();

        // 查询平台账户余额
        UserAccountBalance platformAccount = userAccountBalanceMapper.selectOne(
            Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(UserAccountBalance::getUserId, platformUserId)
        );

        if (platformAccount == null) {
            return BigDecimal.ZERO;
        }

        // 返回可用金额 + 冻结金额
        return platformAccount.getAvailableAmount().add(platformAccount.getFrozenAmount());
    }

    /**
     * 计算总计用户可用余额（排除平台用户）
     */
    private BigDecimal calculateTotalUserAvailableBalance() {
        // 获取平台用户ID
        Long platformUserId = accountConfiguration.getPlatformUserId();

        // 查询所有非平台用户的账户余额
        return userAccountBalanceMapper.selectObjs(
                        new QueryWrapper<UserAccountBalance>()
                                .select("COALESCE(SUM(available_amount), 0)")
                                .ne("user_id", platformUserId)
                ).stream()
                .findFirst()
                .map(obj -> (BigDecimal) obj)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 计算总计用户冻结余额（排除平台用户）
     */
    private BigDecimal calculateTotalUserFrozenBalance() {
        // 获取平台用户ID
        Long platformUserId = accountConfiguration.getPlatformUserId();

        // 查询所有非平台用户的账户余额
        List<UserAccountBalance> userAccounts = userAccountBalanceMapper.selectList(
            Wrappers.<UserAccountBalance>lambdaQuery()
                .ne(UserAccountBalance::getUserId, platformUserId)
        );

        return userAccounts.stream()
                .map(UserAccountBalance::getFrozenAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 统计总计已发放奖励金额
     */
    private BigDecimal sumProviderRewardAmountByDate() {
        return platformTransactionBillMapper.sumProviderRewardAmount();
    }

    /**
     * 统计累计充值金额
     */
    private BigDecimal sumDepositAmountByDate() {
        // 查询所有统计数据并汇总充值金额
        return depositRecordMapper.sumDepositAmountByDate();
    }

    /**
     * 统计累计提现金额
     */
    private BigDecimal sumPointsWithdrawSuccess() {
        // 查询所有统计数据并汇总提现金额
        return withdrawRecordMapper.sumWithdrawAmountByDate();
    }

    @Override
    public boolean saveBasic(DataReportStatisticsSaveReq record, ExtendData extendData) {
        DataReportStatistics saveRecord = BeanUtil.copyProperties(record, DataReportStatistics.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(DataReportStatisticsUpdateReq record, ExtendData extendData) {
        DataReportStatistics updateRecord = BeanUtil.copyProperties(record, DataReportStatistics.class);
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    /**
     * 通用的趋势数据查询方法
     * @param request 趋势查询请求
     * @param dataMapper 数据映射函数，将统计数据转换为具体的响应对象
     * @return 趋势数据响应
     */
    private <T> T getTrendData(BaseTrendReq request, Function<Map<Integer, DataReportStatistics>, T> dataMapper) {
        // 0. 参数验证
        validateTrendRequest(request);
        // 1. 根据查询类型计算日期范围
        record DateRange(LocalDate startDate, LocalDate endDate) {}

        DateRange dateRange = switch (request.getQueryType()) {
            case "LAST_7_DAYS" -> {
                var endDate = LocalDate.now().minusDays(1); // 昨天
                yield new DateRange(endDate.minusDays(6), endDate); // 7天前到昨天
            }
            case "THIS_MONTH" -> {
                var thisMonth = YearMonth.now();
                yield new DateRange(thisMonth.atDay(1), LocalDate.now().minusDays(1));
            }
            case "LAST_MONTH" -> {
                var lastMonth = YearMonth.now().minusMonths(1);
                yield new DateRange(lastMonth.atDay(1), lastMonth.atEndOfMonth());
            }
            case "CUSTOM" -> new DateRange(
                    LocalDate.parse(request.getStartDate().toString(), DateConstants.DATE_FORMATTER),
                    LocalDate.parse(request.getEndDate().toString(), DateConstants.DATE_FORMATTER)
            );
            default -> throw new IllegalArgumentException("不支持的查询类型: " + request.getQueryType());
        };

        LocalDate startDate = dateRange.startDate();
        LocalDate endDate = dateRange.endDate();

        // 2. 查询日期范围内的统计数据
        int startDateInt = Integer.parseInt(startDate.format(DateConstants.DATE_FORMATTER));
        int endDateInt = Integer.parseInt(endDate.format(DateConstants.DATE_FORMATTER));

        LambdaQueryWrapper<DataReportStatistics> queryWrapper = Wrappers.<DataReportStatistics>lambdaQuery()
                .ge(DataReportStatistics::getDay, startDateInt)
                .le( DataReportStatistics::getDay, endDateInt)
                .orderByAsc(DataReportStatistics::getDay);

        List<DataReportStatistics> statisticsList = baseMapper.selectList(queryWrapper);

        // 3. 将查询结果转换为Map，以日期为key，并补全缺失日期
        Map<Integer, DataReportStatistics> statisticsMap = new LinkedHashMap<>();

        // 填充所有日期，缺失的用null
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            int dateInt = Integer.parseInt(currentDate.format(DateConstants.DATE_FORMATTER));
            statisticsMap.put(dateInt, null);
            currentDate = currentDate.plusDays(1);
        }

        statisticsList.forEach(item -> statisticsMap.put(item.getDay(), item));

        return dataMapper.apply(statisticsMap);
    }

    /**
     * 统一的趋势请求参数验证
     */
    private void validateTrendRequest(BaseTrendReq request) {
        // 验证请求对象不为空
        Assert.notNull(request, "请求参数不能为空");

        String queryType = request.getQueryType();
        // 验证查询类型不为空
        Assert.isTrue(StrUtil.isNotEmpty(queryType), "查询类型不能为空");

        // 验证queryType是否符合@Pattern注解的要求
        Assert.isTrue(queryType.matches("^(LAST_7_DAYS|THIS_MONTH|LAST_MONTH|CUSTOM)$"),
                     "查询类型只能是：LAST_7_DAYS、THIS_MONTH、LAST_MONTH、CUSTOM");

        // 验证自定义日期参数
        if ("CUSTOM".equals(queryType)) {
            // 验证自定义日期不为空
            Assert.isTrue(ObjectUtil.isNotEmpty(request.getStartDate()) && ObjectUtil.isNotEmpty(request.getEndDate()),
                         "自定义查询时，开始日期和结束日期不能为空");
        }
    }
}