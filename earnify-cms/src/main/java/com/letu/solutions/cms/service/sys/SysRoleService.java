package com.letu.solutions.cms.service.sys;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysRoleQueryReq;
import com.letu.solutions.model.cms.request.sys.SysRoleSaveReq;
import com.letu.solutions.model.cms.request.sys.SysRoleUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysRoleResponse;

import java.util.List;

/**
 * 系统角色 服务类
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface SysRoleService {

    List<SysRoleResponse> selectBasicList(SysRoleQueryReq request);

    SysRoleResponse selectByIdBasic(Long id);

    boolean saveBasic(SysRoleSaveReq record, ExtendData extendData);

    boolean updateBasic(SysRoleUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    void refreshToken(Long roleId);
}
