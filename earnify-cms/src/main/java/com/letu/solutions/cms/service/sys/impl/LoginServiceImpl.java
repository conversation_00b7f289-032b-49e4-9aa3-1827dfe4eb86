package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.mapper.sys.SysUserMapper;
import com.letu.solutions.cms.service.sys.LoginService;
import com.letu.solutions.cms.service.sys.SysInterfaceService;
import com.letu.solutions.core.configuration.WhiteConfiguration;
import com.letu.solutions.core.constant.CacheConstant;
import com.letu.solutions.core.enums.BusinessTypeEnum;
import com.letu.solutions.core.enums.OsEnum;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.Authentication;
import com.letu.solutions.core.model.CmsUserInfo;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.utils.LockUtil;
import com.letu.solutions.dubbo.middle.cms.CmsFacade;
import com.letu.solutions.dubbo.middle.cms.dto.SysUserDto;
import com.letu.solutions.dubbo.middle.cms.req.SysUserQueryReq;
import com.letu.solutions.dubbo.middle.customer.MessageFacade;
import com.letu.solutions.model.cms.request.sys.CmsLoginRequest;
import com.letu.solutions.model.cms.request.sys.CmsSmsRequest;
import com.letu.solutions.model.cms.response.sys.LoginResponse;
import com.letu.solutions.model.entity.sys.SysUser;
import com.letu.solutions.share.model.enums.MessageEnum;
import com.letu.solutions.share.model.request.cms.ClientTokenReq;
import com.letu.solutions.share.model.response.cms.SysUserResponse;
import com.letu.solutions.util.util.TokenUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * c端用户表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LoginServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements LoginService {
    private final TokenUtil tokenUtil;
    private final WhiteConfiguration whiteConfiguration;
    private final LockUtil lockUtil;
    private final SysInterfaceService sysInterfaceService;
    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;
    @DubboReference(check = false)
    private MessageFacade messageFacade;
    @DubboReference(check = false)
    private CmsFacade cmsFacade;

    @Override
    public void sms(CmsSmsRequest request, ExtendData extendData) {
        String phone = request.getPhone();
        Assert.isTrue(StrUtil.isNotEmpty(phone), "手机号不合法");
        boolean whitePhone = CollectionUtil.isNotEmpty(whiteConfiguration.getWhitePhone()) && whiteConfiguration.getWhitePhone().contains(phone);
        String smsCode = (!whiteConfiguration.isLoginCheck() || whitePhone) ? "1234" : RandomUtil.randomNumbers(4);
        // 校验次数是否超限
        String beginIpStr = Ipv4Util.getBeginIpStr(extendData.getIpAddress(), 16);
        log.info("beginIpStr:{}",beginIpStr);
        if (!whiteConfiguration.getWhiteIps().contains(extendData.getIpAddress()) && !whiteConfiguration.getWhiteIps().contains(beginIpStr)) {
            lockUtil.checkLock(MessageEnum.ADMIN_SMS_CODE_CER.name(), extendData.getIpAddress());
            lockUtil.checkLock(MessageEnum.ADMIN_SMS_CODE_CER.name(), phone);
        }
        stringRedisTemplate.opsForValue().set(String.format(CacheConstant.adminSms, MessageEnum.ADMIN_SMS_CODE_CER.name(), SecureUtil.md5(phone)), smsCode, 5, TimeUnit.MINUTES);
        messageFacade.send(MessageEnum.ADMIN_SMS_CODE_CER, phone, smsCode);
    }

    private void checkSms(CmsLoginRequest request) {
        String phone = request.getPhone();
        String code = request.getSmsCode();
        // 获取并删除库中验证码
        String redisKey = String.format(String.format(CacheConstant.adminSms, MessageEnum.ADMIN_SMS_CODE_CER.name(), SecureUtil.md5(phone)));
        String redisKeyLock = String.format(String.format(CacheConstant.adminSmsLock, MessageEnum.ADMIN_SMS_CODE_CER.name(), SecureUtil.md5(phone)));
        if (stringRedisTemplate.hasKey(redisKeyLock)) {
            throw new ThrowException("#账户已锁定，请联系管理员");
        }
        String cacheSmsCode = stringRedisTemplate.opsForValue().get(redisKey);
        boolean checkRes = code.equals(cacheSmsCode);
        if (!checkRes) {
            String limitKey = String.format(String.format(CacheConstant.cusSmsLimit, MessageEnum.ADMIN_SMS_CODE_CER.name(), SecureUtil.md5(phone)));
            long inc = stringRedisTemplate.opsForValue().increment(limitKey, 1);
            if (1 == inc) {
                stringRedisTemplate.expire(limitKey, 5, TimeUnit.MINUTES);
            }
            // 连续错误3次 重新获取
            if (3 < inc) {
                stringRedisTemplate.delete(redisKey);
                throw new ThrowException("#请重新获取验证码");
            }
            // 连续错误5次 锁定账户30分钟
            if (5 < inc) {
                stringRedisTemplate.opsForValue().set(redisKeyLock, "", 30, TimeUnit.MINUTES);
                stringRedisTemplate.delete(limitKey);
                throw new ThrowException("#账户已锁定，请联系管理员");
            }
            throw new ThrowException("#验证码有误，请重新验证");
        }
    }

    @Override
    public LoginResponse detail(Long userId) {
        return BeanUtil.copyProperties(cmsFacade.loginDetail(userId), LoginResponse.class);
    }

    @Override
    public void refresh(Long userId) {
        SysUserResponse sysUserResponse = cmsFacade.sysUserDetail(userId);
        Authentication authentication = Authentication.builder()
                .userId(userId)
                .cmsUserInfo(CmsUserInfo.builder()
                        .id(userId)
                        .userPhone(sysUserResponse.getPhone())
                        .userName(sysUserResponse.getName())
                        .nameAlls(sysInterfaceService.selectUserInterfaces(userId))
                        .build())
                .build();
        tokenUtil.refreshToken(userId, BusinessTypeEnum.earnify_cms, authentication);
    }

    @Override
    public String clientToken(ClientTokenReq request) {
        Pair<com.letu.solutions.share.model.model.Authentication, Long> authenticationLongPair = cmsFacade.validatedToken(request.getMiddleToken());
        Authentication authentication = BeanUtil.copyProperties(authenticationLongPair.getKey(), Authentication.class);
        authentication.getCmsUserInfo().setNameAlls(sysInterfaceService.selectUserInterfaces(authentication.getUserId()));
        return tokenUtil.putToken(request.getMiddleToken(), authentication, BusinessTypeEnum.earnify_cms, authenticationLongPair.getValue());
    }

    @Override
    public LoginResponse login(CmsLoginRequest request, ExtendData extendData) {
        OsEnum osEnum = extendData.getHeaderDto().getOs();
        Assert.notNull(osEnum, "请求头不合法");
        checkSms(request);
        SysUserQueryReq sysUserQueryReq = new SysUserQueryReq();
        sysUserQueryReq.setPhone(request.getPhone());
        List<SysUserDto> sysUserDtos = cmsFacade.sysUserList(sysUserQueryReq);
        if (CollectionUtil.isEmpty(sysUserDtos)){
            log.error("该用户在中台不存在,请联系管理员");
            throw new ThrowException("#该用户在中台不存在,请联系管理员");
        }
        SysUserDto sysUser = sysUserDtos.get(0);
        LoginResponse loginResponse = BeanUtil.copyProperties(sysUserDtos.get(0), LoginResponse.class);
        String token = tokenUtil.createToken(sysUser.getId(), extendData.getHeaderDto().getBusinessType(), extendData.getHeaderDto().getOsType().getCode(), osEnum.getServerType(),
                Authentication.builder()
                        .userId(sysUser.getId())
                        .userName(sysUser.getName())
                        .cmsUserInfo(CmsUserInfo.builder()
                                .id(sysUser.getId())
                                .userPhone(sysUser.getPhone())
                                .userName(sysUser.getName())
                                .nameAlls(sysInterfaceService.selectUserInterfaces(sysUser.getId()))
                                .build())
                        .build()
        );
        loginResponse.setToken(token);
        return loginResponse;
    }

}