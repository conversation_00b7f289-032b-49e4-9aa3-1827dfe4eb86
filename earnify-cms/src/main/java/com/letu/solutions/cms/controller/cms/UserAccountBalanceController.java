package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.UserAccountBalanceService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceListReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceSaveReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserAccountBalancePageRes;
import com.letu.solutions.model.cms.response.cms.UserAccountBalanceDetailRes;

/**
 * 用户/账户资金信息
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:user_account_balance", valueDesc = "用户:账户资金信息")
public class UserAccountBalanceController{
    private final UserAccountBalanceService basicService;
    /**
    * 账户资金信息 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @PostMapping("/userAccountBalance/page")
    public R<Page<UserAccountBalancePageRes>> loadPage(@Validated @RequestBody UserAccountBalanceListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 账户资金信息 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/userAccountBalance/list")
    public R<List<UserAccountBalancePageRes>> loadList(UserAccountBalanceListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 账户资金信息 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/userAccountBalance/selectById")
    public R<UserAccountBalanceDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 账户资金信息 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/userAccountBalance/insert")
    public R<Boolean> insert(@Validated @RequestBody UserAccountBalanceSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 账户资金信息 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/userAccountBalance/update")
    public R<Boolean> update(@Validated @RequestBody UserAccountBalanceUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 账户资金信息 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/userAccountBalance/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }
}
