package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.ProductExcel;
import com.letu.solutions.cms.dto.TaskExcel;
import com.letu.solutions.cms.mapper.cms.TaskStepMapper;
import com.letu.solutions.cms.mapper.cms.UserAccountBalanceMapper;
import com.letu.solutions.cms.mapper.cms.UserTransactionBillMapper;
import com.letu.solutions.cms.service.user.UserService;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.model.cms.request.cms.TaskStepSaveReq;
import com.letu.solutions.model.cms.response.cms.ProductPageRes;
import com.letu.solutions.model.cms.response.cms.TaskDetailRes;
import com.letu.solutions.model.cms.response.cms.TaskStepRes;
import com.letu.solutions.model.cms.response.user.UserDetailRes;
import com.letu.solutions.model.entity.cms.Task;
import com.letu.solutions.cms.mapper.cms.TaskMapper;
import com.letu.solutions.cms.service.cms.TaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.entity.cms.TaskStep;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.model.entity.cms.UserTransactionBill;
import com.letu.solutions.model.enums.cms.*;
import com.letu.solutions.util.util.TimeUtil;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.letu.solutions.model.enums.cms.StepTypeEnum;
import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.TaskListReq;
import com.letu.solutions.model.cms.request.cms.TaskSaveReq;
import com.letu.solutions.model.cms.response.cms.TaskPageRes;
import com.letu.solutions.model.cms.response.cms.TaskDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.cms.mapper.cms.UserTaskMapper;

/**
 * 任务 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskServiceImpl extends ServiceImpl<TaskMapper, Task> implements TaskService {

    private final TransactionalManage transactionalManage;
    private final TaskStepMapper taskStepMapper;
    private final UserAccountBalanceMapper userAccountBalanceMapper;
    private final UserTransactionBillMapper userTransactionBillMapper;
    private final UserTaskMapper userTaskMapper;
    private final UserService userService;

    @Override
    public Page<TaskPageRes> selectBasicPage(Page<Task> page, TaskListReq request) {
        return baseMapper.selectTaskPage(page, request);
    }


    @Override
    public String selectBasicList(TaskListReq request) {
        List<TaskExcel> list = baseMapper.selectTaskList(request);
        return ExcelUtil.getExcel("任务管理列表", TaskExcel.class, list, false);
    }

    @Override
    public TaskDetailRes selectByIdBasic(Long id) {
        Task record = baseMapper.selectById(id);
        TaskDetailRes taskDetailRes = BeanUtil.copyProperties(record, TaskDetailRes.class);

        // 查询任务步骤
        List<TaskStep> taskSteps = taskStepMapper.selectList(Wrappers.<TaskStep>lambdaQuery()
                .eq(TaskStep::getTaskId, id)
                .orderByAsc(TaskStep::getSrot));

        // 转换TaskStep为TaskStepRes，并处理stepType字段
        List<TaskStepRes> taskStepResList = taskSteps.stream()
                .map(this::convertToTaskStepRes)
                .collect(Collectors.toList());

        taskDetailRes.setTaskSteps(taskStepResList);
        return taskDetailRes;
    }

    @Override
    public boolean saveBasic(TaskSaveReq record, ExtendData extendData) {
        Task saveRecord = BeanUtil.copyProperties(record, Task.class);
        saveRecord.setState(TaskStatusEnum.pendingOrders);
        Boolean result = transactionalManage.execute(() -> {
            Boolean un = baseMapper.insert(saveRecord) > 0;
            for (TaskStepSaveReq taskStepSaveReq : record.getTaskStepSaveReqList()) {
                TaskStep taskStep = BeanUtil.copyProperties(taskStepSaveReq, TaskStep.class);
                taskStep.setTaskId(saveRecord.getId());

                // 处理stepType转换：将List<StepTypeEnum>转换为逗号分隔的字符串
                if (taskStepSaveReq.getStepTypes() != null && !taskStepSaveReq.getStepTypes().isEmpty()) {
                    String stepTypeStr = taskStepSaveReq.getStepTypes().stream()
                            .distinct() // 去重
                            .map(StepTypeEnum::getCode)
                            .collect(Collectors.joining(","));
                    taskStep.setStepType(stepTypeStr);
                }

                un = taskStepMapper.insert(taskStep) > 0;
            }
            //冻结金额和扣减保证金
            Long userId = record.getUserId();
            UserDetailRes userDetailRes = userService.selectByIdBasic(userId);
            BigDecimal freezeAmount = record.getPrice().multiply(BigDecimal.valueOf(record.getNumber()));
            BigDecimal bondAmount = BigDecimal.valueOf(record.getBond());
            // 1. 扣减可用余额，增加冻结金额
            UserAccountBalance userAccountBalance = userAccountBalanceMapper.selectOne(Wrappers.<UserAccountBalance>lambdaQuery()
                    .eq(UserAccountBalance::getUserId, userId));
            if (userAccountBalance == null) {
                throw new RuntimeException("用户账户不存在");
            }
            BigDecimal totalDeduct = freezeAmount.add(bondAmount);
            if (userAccountBalance.getAvailableAmount().compareTo(totalDeduct) < 0) {
                throw new RuntimeException("账户余额不足");
            }
            un = userAccountBalanceMapper.update(Wrappers.<UserAccountBalance>lambdaUpdate().eq(UserAccountBalance::getUserId, userId)
                    .eq(UserAccountBalance::getAvailableAmount, userAccountBalance.getAvailableAmount())
                    .set(UserAccountBalance::getAvailableAmount, userAccountBalance.getAvailableAmount().subtract(totalDeduct))
                    .set(UserAccountBalance::getFrozenAmount, userAccountBalance.getFrozenAmount().add(freezeAmount))) > 0;
            userAccountBalance.setAvailableAmount(userAccountBalance.getAvailableAmount().subtract(totalDeduct));
            userAccountBalance.setFrozenAmount(userAccountBalance.getFrozenAmount().add(freezeAmount));
            // 2. 新增冻结金额流水
            UserTransactionBill freezeBill = new UserTransactionBill();
            freezeBill.setUserId(userId);
            freezeBill.setUserName(userDetailRes.getNickName());
            freezeBill.setAccountType(AccountTypeEnum.client);
            freezeBill.setFundType(FundTypeEnum.deposit_freeze); // 任务奖励/保证金冻结
            freezeBill.setSide(FundSideTypeEnum.out); // 出账
            freezeBill.setAmount(freezeAmount);
            freezeBill.setCurrency(userAccountBalance.getCurrency());
            freezeBill.setBalanceBefore(userAccountBalance.getAvailableAmount().add(totalDeduct));
            freezeBill.setBalanceAfter(userAccountBalance.getAvailableAmount());
            freezeBill.setFrozen(freezeAmount);
            freezeBill.setDay(TimeUtil.today(new Date()));
            freezeBill.setRemark("任务发布冻结金额");
            un = userTransactionBillMapper.insert(freezeBill) > 0;
            // 3. 新增保证金流水
            if (bondAmount.compareTo(BigDecimal.ZERO) == 1) {
                UserTransactionBill bondBill = new UserTransactionBill();
                bondBill.setAccountType(AccountTypeEnum.client);
                bondBill.setUserName(userDetailRes.getNickName());
                bondBill.setUserId(userId);
                bondBill.setFundType(FundTypeEnum.deposit_freeze); // 任务奖励/保证金冻结
                bondBill.setSide(FundSideTypeEnum.out); // 出账
                bondBill.setAmount(bondAmount);
                bondBill.setCurrency(userAccountBalance.getCurrency());
                bondBill.setBalanceBefore(userAccountBalance.getAvailableAmount().add(bondAmount));
                bondBill.setBalanceAfter(userAccountBalance.getAvailableAmount());
                bondBill.setFrozen(bondAmount);
                bondBill.setDay(TimeUtil.today(new Date()));
                bondBill.setRemark("任务发布保证金");
                un = userTransactionBillMapper.insert(bondBill) > 0;
            }

            return un;
        });
        Assert.isTrue(result, "添加错误");
        return result;
    }


    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        // 1. 通过 taskId 查 task 表，获取 userId
        Task task = baseMapper.selectById(id);
        if (task == null) {
            log.warn("任务不存在，无法计算冻结金额");
            return false;
        }
        task.setState(TaskStatusEnum.revoked);
        Long userId = task.getUserId();
        // 2. 查 user_account_balance 表，获取该 userId 的 frozenAmount
        UserAccountBalance userAccountBalance = userAccountBalanceMapper.selectOne(Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(UserAccountBalance::getUserId, userId));
        if (userAccountBalance == null) {
            throw new RuntimeException("用户账户不存在");
        }
        BigDecimal frozenAmount = userAccountBalance.getFrozenAmount();
        // 3. 查 user_task 表，筛选 task_id = id 且 state 为以下所以状态 的所有记录，累加 price
        List<String> states = Arrays.asList(UserTaskStatusEnum.incomplete.name(),
                UserTaskStatusEnum.appealInProgress.name(),
                UserTaskStatusEnum.pendingApproval.name(),
                UserTaskStatusEnum.partyARejects.name());
        BigDecimal inProgressSum = userTaskMapper.sumPriceByTaskIdAndStates(id, states);
        // 4. 计算剩余冻结金额
        BigDecimal remainFrozen = frozenAmount.subtract(inProgressSum != null ? inProgressSum : BigDecimal.ZERO);
        log.info("任务撤销，甲方userId={}，冻结金额剩余：{} = {} - {}", userId, remainFrozen, frozenAmount, inProgressSum);
        Boolean result = transactionalManage.execute(() -> {
            Boolean un = userAccountBalanceMapper.update(Wrappers.<UserAccountBalance>lambdaUpdate()
                    .eq(UserAccountBalance::getUserId, userId)
                    .eq(UserAccountBalance::getFrozenAmount, userAccountBalance.getFrozenAmount())
                    .eq(UserAccountBalance::getAvailableAmount, userAccountBalance.getAvailableAmount())
                    .set(UserAccountBalance::getFrozenAmount, inProgressSum)
                    .set(inProgressSum.compareTo(BigDecimal.ZERO)==1,UserAccountBalance::getAvailableAmount, userAccountBalance.getAvailableAmount().add(remainFrozen))
                    .set(inProgressSum.compareTo(BigDecimal.ZERO)==0,UserAccountBalance::getAvailableAmount,
                            userAccountBalance.getAvailableAmount().add(remainFrozen).add(new BigDecimal(task.getBond())))
            )>0;
            un = baseMapper.updateById(task) > 0;
            return un;
        });
        Assert.isTrue(result,"撤销失败");
        return result;
    }

    /**
     * 转换TaskStep为TaskStepRes，处理stepType字段
     * 将逗号分隔的stepType字符串转换为List<StepTypeEnum>
     */
    private TaskStepRes convertToTaskStepRes(TaskStep taskStep) {
        TaskStepRes taskStepRes = BeanUtil.copyProperties(taskStep, TaskStepRes.class);

        // 处理stepType字段：将逗号分隔的字符串转换为枚举列表
        if (taskStep.getStepType() != null && !taskStep.getStepType().trim().isEmpty()) {
            List<StepTypeEnum> stepTypes = Arrays.stream(taskStep.getStepType().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(StepTypeEnum::valueOf)
                    .collect(Collectors.toList());
            taskStepRes.setStepTypes(stepTypes);
        } else {
            taskStepRes.setStepTypes(Arrays.asList()); // 空列表
        }

        return taskStepRes;
    }
}