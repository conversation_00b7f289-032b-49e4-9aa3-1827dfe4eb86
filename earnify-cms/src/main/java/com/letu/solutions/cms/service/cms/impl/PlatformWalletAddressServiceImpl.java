package com.letu.solutions.cms.service.cms.impl;

import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import com.letu.solutions.model.entity.cms.PlatformWalletAddress;
import com.letu.solutions.cms.mapper.cms.PlatformWalletAddressMapper;
import com.letu.solutions.cms.service.cms.PlatformWalletAddressService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Date;
import java.util.stream.Collectors;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressListReq;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressSaveReq;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressUpdateReq;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressPageRes;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressWithConfigRes;

/**
 * 平台钱包地址表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PlatformWalletAddressServiceImpl extends ServiceImpl<PlatformWalletAddressMapper, PlatformWalletAddress> implements PlatformWalletAddressService {
    @Override
    public Page<PlatformWalletAddressPageRes> selectBasicPage(Page<PlatformWalletAddress> page, PlatformWalletAddressListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<PlatformWalletAddress> queryWrapper = Wrappers.<PlatformWalletAddress>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),PlatformWalletAddress::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getNetwork()),PlatformWalletAddress::getNetwork, request.getNetwork())
                .eq(ObjectUtil.isNotEmpty(request.getAddress()),PlatformWalletAddress::getAddress, request.getAddress())
                .eq(ObjectUtil.isNotEmpty(request.getTag()),PlatformWalletAddress::getTag, request.getTag())
                .eq(ObjectUtil.isNotEmpty(request.getIsEnabled()),PlatformWalletAddress::getIsEnabled, request.getIsEnabled())
                .eq(ObjectUtil.isNotEmpty(request.getRemark()),PlatformWalletAddress::getRemark, request.getRemark());

        Page<PlatformWalletAddress> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, PlatformWalletAddressPageRes.class);
        }



    @Override
    public List<PlatformWalletAddressPageRes> selectBasicList(PlatformWalletAddressListReq request) {
        List<PlatformWalletAddress> basicList = baseMapper.selectList(Wrappers.<PlatformWalletAddress>lambdaQuery());
        return BeanUtil.copyToList(basicList, PlatformWalletAddressPageRes.class);
    }

    @Override
    public PlatformWalletAddressDetailRes selectByIdBasic(Long id) {
        PlatformWalletAddress record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, PlatformWalletAddressDetailRes.class);
    }

    @Override
    public boolean saveBasic(PlatformWalletAddressSaveReq record, ExtendData extendData) {
        PlatformWalletAddress saveRecord = BeanUtil.copyProperties(record, PlatformWalletAddress.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(PlatformWalletAddressUpdateReq record, ExtendData extendData) {
        PlatformWalletAddress updateRecord = BeanUtil.copyProperties(record, PlatformWalletAddress.class);
        updateRecord.setUpdateTime(DateUtil.date());
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public List<PlatformWalletAddressWithConfigRes> selectByNetwork(String network) {
        LambdaQueryWrapper<PlatformWalletAddress> query = new LambdaQueryWrapper<>();
        query.eq(PlatformWalletAddress::getNetwork, network);
        List<PlatformWalletAddress> record = baseMapper.selectList(query);
        return BeanUtil.copyToList(record, PlatformWalletAddressWithConfigRes.class);
    }

    @Override
    public List<String> getAddressesByNetwork(String network) {
        log.info("根据协议网络查询钱包地址列表，network：{}", network);

        LambdaQueryWrapper<PlatformWalletAddress> query = Wrappers.<PlatformWalletAddress>lambdaQuery()
                .eq(PlatformWalletAddress::getNetwork, network)
                .orderByAsc(PlatformWalletAddress::getId);

        List<PlatformWalletAddress> records = baseMapper.selectList(query);

        // 使用lambda表达式提取地址列表
        List<String> addresses = records.stream()
                .map(PlatformWalletAddress::getAddress)
                .filter(address -> address != null && !address.trim().isEmpty())
                .collect(Collectors.toList());

        log.info("查询到{}个钱包地址", addresses.size());
        return addresses;
    }
}