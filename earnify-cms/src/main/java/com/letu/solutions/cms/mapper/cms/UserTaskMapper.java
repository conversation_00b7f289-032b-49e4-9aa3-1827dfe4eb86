package com.letu.solutions.cms.mapper.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.dto.UserTaskExcel;
import com.letu.solutions.model.cms.request.cms.UserTaskListReq;
import com.letu.solutions.model.cms.response.cms.UserTaskPageRes;
import com.letu.solutions.model.entity.cms.UserTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import com.letu.solutions.cms.dto.UserTaskStatisticsDto;

import java.util.Map;

import java.math.BigDecimal;
import java.util.List;

import com.letu.solutions.model.cms.response.cms.UserTaskDetailRes;

/**
 * 任务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper

public interface UserTaskMapper extends BaseMapper<UserTask> {

    /**
     * 统计某任务下所有 states集合 状态的 price 总和
     */
    BigDecimal sumPriceByTaskIdAndStates(@Param("taskId") Long taskId, @Param("states") List<String> states);

    /**
     * 分页多表联合查询用户任务
     */
    Page<UserTaskPageRes> selectUserTaskPage(
            @Param("page") Page page,
            @Param("req") UserTaskListReq req);

    List<UserTaskExcel> selectUserTaskList(@Param("req") UserTaskListReq req);

    /**
     * 查询任务详情（含步骤列表）
     */
    UserTaskDetailRes selectUserTaskDetailWithSteps(@Param("taskId") Long taskId,@Param("partyBUserId")Long partyBUserId);

    /**
     * 批量查询用户任务统计数据
     * @param userIdList 用户ID列表字符串
     * @return 用户任务统计DTO列表
     */
    @Select("""
            SELECT
                user_id as userId,
                COUNT(*) as receivedTaskCount,
                SUM(CASE WHEN state IN (3, 10) THEN 1 ELSE 0 END) as completedTaskCount,
                COALESCE(SUM(CASE WHEN state IN (4, 5, 8) THEN price ELSE 0 END), 0) as pendingReward,
                COALESCE(SUM(CASE WHEN state IN (3, 10) THEN price ELSE 0 END), 0) as settledReward
            FROM user_task
            WHERE user_id IN (${userIdList})
            GROUP BY user_id
            """)
    List<UserTaskStatisticsDto> getUserTaskStatsByUserIds(@Param("userIdList") String userIdList);
}
