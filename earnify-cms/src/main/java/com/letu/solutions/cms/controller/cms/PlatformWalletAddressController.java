package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.PlatformWalletAddressService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressListReq;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressSaveReq;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressUpdateReq;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressPageRes;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressDetailRes;

/**
 * 财务/平台钱包地址表
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:platform_wallet_address", valueDesc = "财务:平台钱包地址表")
public class PlatformWalletAddressController{
    private final PlatformWalletAddressService basicService;
    /**
    * 平台钱包地址表 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @PostMapping("/platformWalletAddress/page")
    public R<Page<PlatformWalletAddressPageRes>> loadPage(@Validated @RequestBody PlatformWalletAddressListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 平台钱包地址表 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/platformWalletAddress/list")
    public R<List<PlatformWalletAddressPageRes>> loadList(PlatformWalletAddressListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 平台钱包地址表 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/platformWalletAddress/selectById")
    public R<PlatformWalletAddressDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 平台钱包地址表 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/platformWalletAddress/insert")
    public R<Boolean> insert(@Validated @RequestBody PlatformWalletAddressSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 平台钱包地址表 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/platformWalletAddress/update")
    public R<Boolean> update(@Validated @RequestBody PlatformWalletAddressUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 平台钱包地址表 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/platformWalletAddress/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }
}
