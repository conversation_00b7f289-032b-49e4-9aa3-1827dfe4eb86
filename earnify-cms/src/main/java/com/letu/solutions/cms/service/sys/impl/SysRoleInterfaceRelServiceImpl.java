package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.mapper.sys.SysInterfaceMapper;
import com.letu.solutions.cms.mapper.sys.SysRoleInterfaceRelMapper;
import com.letu.solutions.cms.mapper.sys.SysUserRoleRelMapper;
import com.letu.solutions.cms.service.sys.LoginService;
import com.letu.solutions.cms.service.sys.SysRoleInterfaceRelService;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysRoleInterfaceRelSaveReq;
import com.letu.solutions.model.entity.sys.SysRoleInterfaceRel;
import com.letu.solutions.model.entity.sys.SysUserRoleRel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 系统角色-接口关系 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysRoleInterfaceRelServiceImpl extends ServiceImpl<SysRoleInterfaceRelMapper, SysRoleInterfaceRel> implements SysRoleInterfaceRelService {
    private final SysUserRoleRelMapper sysUserRoleRelMapper;
    private final LoginService loginService;
    private final SysInterfaceMapper sysInterfaceMapper;

    @Override
    public List<String> selectRoleInterface(Long roleId) {
        if (NumberUtil.equals(roleId, Long.valueOf(1))) {
            return sysInterfaceMapper.allInterface();
        }
        List<SysRoleInterfaceRel> resList = baseMapper.selectList(Wrappers.<SysRoleInterfaceRel>lambdaQuery().eq(SysRoleInterfaceRel::getRoleId, roleId));
        if (CollectionUtil.isEmpty(resList)) {
            return ListUtil.empty();
        }
        return resList.stream().map(SysRoleInterfaceRel::getNameAll).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveRoleInterface(SysRoleInterfaceRelSaveReq record, ExtendData extendData) {
        if (NumberUtil.equals(record.getRoleId(), Long.valueOf(1))) {
            throw new ThrowException("#超管权限不可修改");
        }
        List<SysRoleInterfaceRel> list = new ArrayList<>();
        for (String nameAll : record.getNameAlls()) {
            SysRoleInterfaceRel details = new SysRoleInterfaceRel();
            details.setRoleId(record.getRoleId());
            details.setNameAll(nameAll);
            list.add(details);
        }
        baseMapper.delete(Wrappers.<SysRoleInterfaceRel>lambdaQuery().eq(SysRoleInterfaceRel::getRoleId, record.getRoleId()));
        if (CollectionUtil.isNotEmpty(list)) {
            this.saveBatch(list);
        }
        return true;
    }

    @Override
    @Async("asyncPool")
    public void refreshAllRoleUser(Long roleId) {
        List<SysUserRoleRel> sysUserRoleRels = sysUserRoleRelMapper.selectList(Wrappers.<SysUserRoleRel>lambdaQuery().eq(SysUserRoleRel::getRoleId, roleId));
        Set<Long> userIds = sysUserRoleRels.stream().map(SysUserRoleRel::getUserId).collect(Collectors.toSet());
        for (Long userId : userIds) {
            loginService.refresh(userId);
        }
    }
}