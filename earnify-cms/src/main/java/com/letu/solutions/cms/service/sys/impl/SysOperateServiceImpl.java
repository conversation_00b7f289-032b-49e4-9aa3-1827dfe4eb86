package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.mapper.sys.SysOperateMapper;
import com.letu.solutions.cms.service.sys.SysInterfaceService;
import com.letu.solutions.cms.service.sys.SysOperateService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysOperateListReq;
import com.letu.solutions.model.cms.response.sys.SysOperatePageRes;
import com.letu.solutions.model.entity.sys.SysOperate;
import com.letu.solutions.model.enums.LogTypeEnum;
import com.letu.solutions.util.util.PageUtil;
import com.letu.solutions.util.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 系统-操作日志 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysOperateServiceImpl extends ServiceImpl<SysOperateMapper, SysOperate> implements SysOperateService {
    @Override
    public Page<SysOperatePageRes> selectBasicPage(Page<SysOperate> page, SysOperateListReq request) {
        Page<SysOperate> basicPage = baseMapper.selectPage(page,
                Wrappers.<SysOperate>lambdaQuery()
                        .ge(ObjectUtil.isNotNull(request.getBeginDate()), SysOperate::getDay, request.getBeginDate())
                        .le(ObjectUtil.isNotNull(request.getEndDate()), SysOperate::getDay, request.getEndDate())
                        .eq(ObjectUtil.isNotNull(request.getLogType()), SysOperate::getLogType, request.getLogType())
                        .eq(ObjectUtil.isNotNull(request.getUserId()), SysOperate::getUserId, request.getUserId())
                        .likeLeft(StrUtil.isNotEmpty(request.getMethodNameAll()), SysOperate::getMethodNameAll, request.getMethodNameAll())
                        .likeLeft(StrUtil.isNotEmpty(request.getMethodDescAll()), SysOperate::getMethodDescAll, request.getMethodDescAll())
                        .likeLeft(StrUtil.isNotEmpty(request.getRequestUrl()), SysOperate::getRequestUrl, request.getRequestUrl())
                        .orderByDesc(SysOperate::getId)
        );
        return PageUtil.builderPage(basicPage, SysOperatePageRes.class);
    }

    @Override
    public SysOperate loadDate(ExtendData extendData, JoinPoint joinPoint, String result, boolean isOk, String error, boolean isError) {
        Pair<String, String> authPair = loadPreAuth(joinPoint, false);
        String uri = loadUri();
        String param = loadParam(joinPoint, joinPoint.getArgs(), uri);
        return SysOperate.builder()
                .day(TimeUtil.today())
                .logType(isError ? LogTypeEnum.error : LogTypeEnum.common)
                .userId(extendData.getUserId())
                .userName(extendData.isLogin() ? extendData.getAuthentication().getCmsUserInfo().getUserName() : StrUtil.EMPTY)
                .methodNameAll(authPair.getKey())
                .methodDescAll(authPair.getValue())
                .requestUrl(uri)
                .requestIp(extendData.getIpAddress())
                .location(null)
                .requestParam(param)
                .operateRes(isOk ? 1 : 0)
                .result(result)
                .build();
    }

    @Async("asyncPool")
    @Override
    public void saveLog(SysOperate record) {
        baseMapper.insert(record);
    }

    @Override
    public Pair<String, String> loadPreAuth(JoinPoint joinPoint, boolean check) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Preauthorize methodAnnotation = AnnotationUtil.getAnnotation(method, Preauthorize.class);
        if (null == methodAnnotation) {
            return null;
        }
        Class<?> aClass = joinPoint.getTarget().getClass();
        Preauthorize classAnnotation = AnnotationUtil.getAnnotation(aClass, Preauthorize.class);
        if (null == classAnnotation) {
            return null;
        }
        if (check && (classAnnotation.disCheck() || methodAnnotation.disCheck())) {
            return null;
        }
        return new Pair<>(StrUtil.join(SysInterfaceService.splitKey, classAnnotation.value(), methodAnnotation.value()),
                StrUtil.join(SysInterfaceService.splitKey, classAnnotation.valueDesc(), methodAnnotation.valueDesc()));
    }


    /**
     * 拼接参数
     */
    private String loadParam(JoinPoint joinPoint, Object[] args, String uri) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = signature.getParameterNames();
        if (null == parameterNames) {
            return "";
        }
        int length = parameterNames.length;
       JSONObject jsonObject = new JSONObject(16, 0.75f, true);
        for (int i = 0; i < length; i++) {
            if (!parameterNames[i].equals("extendData") && !parameterNames[i].equals("file")) {
                jsonObject.put(parameterNames[i], args[i]);
            }
        }
        return StrUtil.sub(JSONObject.toJSONString(jsonObject, JSONWriter.Feature.MapSortField), 0, 2000);
    }

    private String loadUri() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            return request.getRequestURI();
        } catch (Exception e) {
            return null;
        }
    }
}