package com.letu.solutions.cms.service.user;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.cms.request.cms.ClientUserManagementReq;
import com.letu.solutions.model.cms.request.user.*;
import com.letu.solutions.model.cms.response.cms.ClientUserManagementRes;
import com.letu.solutions.model.entity.user.User;
import java.util.List;

import com.letu.solutions.model.cms.response.user.UserPageRes;
import com.letu.solutions.model.cms.response.user.UserDetailRes;

/**
 * 用户 服务类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface UserService {
    Page<UserPageRes> selectBasicPage(Page<User> page, UserListReq request);

    List<UserPageRes> selectBasicList(UserListReq request);

    UserDetailRes selectByIdBasic(Long id);

    boolean saveBasic(UserSaveReq record, ExtendData extendData);

    boolean updateBasic(UserUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    Boolean updateEnable(UserEnableReq request, ExtendData extendData);

    boolean sync(UserSyncReq record, ExtendData extendData);
}
