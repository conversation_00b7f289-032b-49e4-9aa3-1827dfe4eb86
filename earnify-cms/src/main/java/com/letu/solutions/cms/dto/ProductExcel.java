package com.letu.solutions.cms.dto;


import com.letu.solutions.cms.aspect.ExcelField;
import com.letu.solutions.model.enums.cms.ProductStatusEnum;
import com.letu.solutions.model.enums.cms.ProductTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 产品表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class ProductExcel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 产品自增ID
     */
    @ExcelField(title = "藏品ID")
    private Long id;
    /**
     * 产品名称
     */
    @ExcelField(title = "产品名称")
    private String productName;
    /**
     * 产品logo的URL
     */
    @ExcelField(title = "产品logo的URL")
    private String logo;
    /**
     * 甲方用户ID
     */
    @ExcelField(title = "甲方用户ID")
    private Long partyUserId;
    /**
     * 甲方用户昵称
     */
    @ExcelField(title = "甲方用户昵称")
    private String partyUserNickname;
    /**
     * 产品类型，
     */
    @ExcelField(title = "产品类型",
            dictType = {"tool", "nft"},
            dictValue = {"工具", "链游"}
    )
    private ProductTypeEnum productType;
    /**
     * 产品状态
     */
    @ExcelField(title = "产品状态",
            dictType = {"notList", "stopList", "stopShelf"},
            dictValue = {"未上架", "已上架", "已下架"}
    )
    private ProductStatusEnum productStatus;
    /**
     * 任务发布次数
     */
    @ExcelField(title = "任务发布次数")
    private Integer taskPublishCount;
    /**
     * 任务发布个数
     */
    @ExcelField(title = "任务发布个数")
    private Integer taskPublishNum;
    /**
     * 任务领取个数
     */
    @ExcelField(title = "任务领取个数")
    private Integer taskReceiveNum;
    /**
     * 任务完成个数
     */
    @ExcelField(title = "任务完成个数")
    private Integer taskFinishNum;
    /**
     * 已经发送奖励金额
     */
    @ExcelField(title = "已经发送奖励金额")
    private BigDecimal sentRewardAmount;
    /**
     * 剩余冻结金额
     */
    @ExcelField(title = "剩余冻结金额")
    private BigDecimal freezeAmount;
    /**
     * 创建时间
     */
    @ExcelField(title = "创建时间")
    private Date createTime;
}
