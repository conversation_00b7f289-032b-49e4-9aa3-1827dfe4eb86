package com.letu.solutions.cms.dto;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.letu.solutions.cms.aspect.ExcelField;

/**
 * 提现记录导出Excel DTO
 */
@Data
public class WithdrawRecordExcel implements Serializable {
    @ExcelField(title = "提现ID")
    private Long id;
    @ExcelField(title = "用户ID")
    private Long userId;
    @ExcelField(title = "用户名")
    private String username;
    @ExcelField(title = "协议网络")
    private String network;
    @ExcelField(title = "提现数量")
    private BigDecimal amount;
    @ExcelField(title = "币种")
    private String tokenSymbol;
    @ExcelField(title = "提现钱包地址")
    private String withdrawAddress;
    @ExcelField(title = "链上交易哈希")
    private String txHash;
    @ExcelField(title = "手续费")
    private BigDecimal fee;
    @ExcelField(title = "状态")
    private String withdrawStatus;
    @ExcelField(title = "操作人")
    private String operator;
    @ExcelField(title = "备注")
    private String remark;
    @ExcelField(title = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 