package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.AppealRecord;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.AppealRecordListReq;
import com.letu.solutions.model.cms.request.cms.AppealRecordSaveReq;
import com.letu.solutions.model.cms.request.cms.AppealRecordUpdateReq;
import com.letu.solutions.model.cms.response.cms.AppealRecordPageRes;
import com.letu.solutions.model.cms.response.cms.AppealRecordDetailRes;

/**
 * 申述记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface AppealRecordService {
    Page<AppealRecordPageRes> selectBasicPage(Page<AppealRecord> page, AppealRecordListReq request);

    String selectBasicList(AppealRecordListReq request);

    AppealRecordDetailRes selectByIdBasic(Long id);

    boolean updateBasic(AppealRecordUpdateReq record, ExtendData extendData);

}
