package com.letu.solutions.cms.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.dto.TaskStatistics;
import com.letu.solutions.cms.dto.WithdrawStatistics;
import com.letu.solutions.cms.mapper.user.UserMapper;
import com.letu.solutions.cms.service.user.UserService;
import com.letu.solutions.core.configuration.RegisterConfiguration;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.service.NovelMessageSender;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.dubbo.middle.customer.UserFacade;
import com.letu.solutions.dubbo.earnify.util.ExtendCovert;
import com.letu.solutions.model.cms.request.user.*;
import com.letu.solutions.model.cms.response.user.UserDetailRes;
import com.letu.solutions.model.cms.response.user.UserPageRes;
import com.letu.solutions.model.entity.user.User;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.share.model.request.user.UserRegisterDeviceIdReq;
import com.letu.solutions.share.model.request.user.UserRegisterMobileReq;
import com.letu.solutions.share.model.request.user.UserRegisterPwdReq;
import com.letu.solutions.util.util.PageUtil;
import com.letu.solutions.util.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 用户 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    private final TransactionalManage transactionalManage;
    private final RegisterConfiguration registerConfiguration;
    private final NovelMessageSender novelMessageSender;

    @DubboReference(check = false)
    private UserFacade userFacade;

    @Override
    public Page<UserPageRes> selectBasicPage(Page<User> page, UserListReq request) {
        Page<User> basicPage = baseMapper.selectPage(page,
                Wrappers.<User>lambdaQuery()
                        .ge(ObjectUtil.isNotNull(request.getBeginTime()), User::getDay, request.getBeginTime())
                        .le(ObjectUtil.isNotNull(request.getEndTime()), User::getDay, request.getEndTime())
                        .likeLeft(StrUtil.isNotEmpty(request.getUserPhone()), User::getUserPhone, request.getUserPhone())
                        .like(StrUtil.isNotEmpty(request.getNickName()), User::getNickName, request.getNickName())
                        .eq(ObjectUtil.isNotNull(request.getUserType()), User::getUserType, request.getUserType())
                        .eq(ObjectUtil.isNotNull(request.getEnable()), User::getEnable, request.getEnable())
                        .eq(ObjectUtil.isNotNull(request.getSourceCode()), User::getSourceCode, request.getSourceCode())
                        .and(StrUtil.isNotEmpty(request.getQuery()), wrapper -> wrapper
                                .like(StrUtil.isNotEmpty(request.getQuery()), User::getId, request.getQuery())
                                .or()
                                .like(StrUtil.isNotEmpty(request.getQuery()), User::getNickName, request.getQuery())
                        )
        );
        return PageUtil.builderPage(basicPage, UserPageRes.class);
    }

    @Override
    public List<UserPageRes> selectBasicList(UserListReq request) {
        List<User> basicList = baseMapper.selectList(
                Wrappers.<User>lambdaQuery()
                        .ge(ObjectUtil.isNotNull(request.getBeginDate()), User::getDay, request.getBeginDate())
                        .le(ObjectUtil.isNotNull(request.getEndDate()), User::getDay, request.getEndDate())
                        .likeLeft(StrUtil.isNotEmpty(request.getUserPhone()), User::getUserPhone, request.getUserPhone())
                        .eq(ObjectUtil.isNotNull(request.getEnable()), User::getEnable, request.getEnable())
        );
        return BeanUtil.copyToList(basicList, UserPageRes.class);
    }

    @Override
    public UserDetailRes selectByIdBasic(Long id) {
        User record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, UserDetailRes.class);
    }

    @Override
    public boolean saveBasic(UserSaveReq record, ExtendData extendData) {
        if (StringUtils.isEmpty(record.getUserImage())) {
            record.setUserImage(registerConfiguration.getUserImgUrls().get(0));
            Collections.shuffle(registerConfiguration.getUserImgUrls());
        }
        String salt = RandomUtil.randomString(4);
        User saveRecord = BeanUtil.copyProperties(record, User.class);
        saveRecord.setSalt(salt);
        saveRecord.setDay(TimeUtil.today(new Date()));
        transactionalManage.execute(() -> {
            boolean res = baseMapper.insert(saveRecord) > 0;
            Assert.isTrue(res, "新增用户失败，请稍后重试");
            noticeMiddle(saveRecord, extendData, DateUtil.date());
            return true;
        });
        return true;
    }

    @Override
    public boolean updateBasic(UserUpdateReq record, ExtendData extendData) {
        User updateRecord = BeanUtil.copyProperties(record, User.class);
        updateRecord.setUpdateTime(DateUtil.date());
        boolean res = baseMapper.updateById(updateRecord) > 0;
        return res;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        boolean res = baseMapper.update(null,
                Wrappers.<User>lambdaUpdate()
                        .set(User::getEnable, 0)
                        .eq(User::getId, id)) > 0;
        return res;
    }

    @Override
    public Boolean updateEnable(UserEnableReq request, ExtendData extendData) {
        //查询用户
        User user = getById(request.getId());
        if (user == null) {
            throw new ThrowException("用户不存在");
        }
        boolean res = baseMapper.update(null,
                Wrappers.<User>lambdaUpdate()
                        .set(User::getEnable, request.getEnable())
                        .eq(User::getId, request.getId())) > 0;
        return res;
    }

    @Override
    public boolean sync(UserSyncReq record, ExtendData extendData) {
        List<User> users = baseMapper.selectList(Wrappers.<User>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(record.getIds()), User::getId, record.getIds())
                .in(CollectionUtil.isNotEmpty(record.getPhones()), User::getUserPhone, record.getPhones())
        );
        for (User user : users) {
            noticeMiddle(user, extendData, DateUtil.date());
        }
        return true;
    }

    private void noticeMiddle(User user, ExtendData extendData, Date now) {
        if (StrUtil.isNotEmpty(user.getPwd())) {
            userFacade.registerPwd(UserRegisterPwdReq.builder()
                            .clientUserId(user.getId())
                            .phone(user.getUserPhone())
                            .pwd(user.getPwd())
                            .salt(user.getSalt())
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .registerDate(now)
                            .build()
                    , ExtendCovert.covert(extendData));
        } else if (StrUtil.isNotEmpty(user.getUserPhone())) {
            userFacade.registerMobile(UserRegisterMobileReq.builder()
                            .clientUserId(user.getId())
                            .phone(user.getUserPhone())
                            .registerDate(now)
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .build()
                    , ExtendCovert.covert(extendData));
        } else {
            userFacade.registerDeviceId(UserRegisterDeviceIdReq.builder()
                            .deviceId(user.getDeviceId())
                            .clientUserId(user.getId())
                            .registerDate(now)
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .build()
                    , ExtendCovert.covert(extendData));
        }
    }

}