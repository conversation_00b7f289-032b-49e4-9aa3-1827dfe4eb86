package com.letu.solutions.cms.aspect;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Excel异步获取监听
 * @author: 木鱼
 * @Date: 2022/2/10 9:31
 */
@Slf4j
public class ExcelModelListener<T> extends AnalysisEventListener<T> {

    private static final int BATCH_COUNT = 2000;
    List<T> list =  CollectionUtil.newArrayList();
    private static int count = 1;
    private List<T> returnList;

    public ExcelModelListener(List<T> returnList) {
        this.returnList = returnList;
    }

    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", t.toString());
        list.add(t);
        count ++;
        if (list.size() >= BATCH_COUNT) {
            saveData( count );
            list.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        saveData( count );
       log.info("所有数据解析完成,共：{} 条数据",count);
    }

    /**
     * 加上存储数据库
     */
    private void saveData(int count) {
        returnList.addAll(list);
        log.info("{}条数据，开始存储数据库,当前存储数据条数:{}" ,count,list.size());
    }
}
