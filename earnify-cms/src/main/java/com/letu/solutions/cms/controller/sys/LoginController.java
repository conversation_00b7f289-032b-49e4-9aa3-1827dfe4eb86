package com.letu.solutions.cms.controller.sys;

import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.sys.LoginService;
import com.letu.solutions.cms.service.sys.SysMenuService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.cms.request.sys.CmsLoginRequest;
import com.letu.solutions.model.cms.request.sys.CmsSmsRequest;
import com.letu.solutions.model.cms.response.sys.LoginResponse;
import com.letu.solutions.model.cms.response.sys.SysMenuVo;
import com.letu.solutions.share.model.request.cms.ClientTokenReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * cms登录相关
 */
@RestController
@RequiredArgsConstructor
@Preauthorize(value = "sys:login", valueDesc = "系统:登录", disCheck = true)
@Slf4j
public class LoginController {
    private final LoginService loginService;
    private final SysMenuService sysMenuService;

    /**
     * 发送短信验证码
     */
    @Preauthorize(value = "sys:sms", valueDesc = "验证码发送", disCheck = true)
    @GetMapping("/login/sms.e")
    public R<Void> sms(CmsSmsRequest request, @RequestHeader ExtendData extendData) {
        loginService.sms(request, extendData);
        return R.success();
    }

    /**
     * cms登录
     */
    @Preauthorize(value = "sys:login", valueDesc = "登录", disCheck = true)
    @PostMapping("/login/login.e")
    public R<LoginResponse> login(@Validated @RequestBody CmsLoginRequest request, @RequestHeader ExtendData extendData) {
        return R.success(loginService.login(request, extendData));
    }

    /**
     * 当前用户信息查询
     */
    @Preauthorize(value = "sys:detail", valueDesc = "当前用户信息查询", disCheck = true)
    @PostMapping("/login/detail")
    public R<LoginResponse> detail(@RequestHeader ExtendData extendData) {
        return R.success(loginService.detail(extendData.getUserId()));
    }

    /**
     * 获取登录用户已有菜单树
     */
    @Preauthorize(value = "sys:detail", valueDesc = "获取登录用户已有菜单树", disCheck = true)
    @GetMapping("/sys/getUserMenuList")
    public R<List<SysMenuVo>> getUserMenuList(@RequestHeader ExtendData extendData) {
        return R.success(sysMenuService.selectUserMenus(extendData.getUserId()));
    }


    /**
     * 获取某个客户端管理端token信息
     */
    @Preauthorize(value = "sys:client", valueDesc = "获取某个客户端管理端token信息", disCheck = true)
    @PostMapping("/sys/client/token.e")
    public R<String> clientToken(@Validated @RequestBody ClientTokenReq request, @RequestHeader ExtendData extendData) {
        return R.success(loginService.clientToken(request));
    }
}