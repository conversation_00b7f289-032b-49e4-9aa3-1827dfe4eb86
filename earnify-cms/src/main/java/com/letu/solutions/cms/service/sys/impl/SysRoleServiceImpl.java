package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.mapper.sys.SysRoleMapper;
import com.letu.solutions.cms.mapper.sys.SysUserRoleRelMapper;
import com.letu.solutions.cms.service.sys.LoginService;
import com.letu.solutions.cms.service.sys.SysRoleService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysRoleQueryReq;
import com.letu.solutions.model.cms.request.sys.SysRoleSaveReq;
import com.letu.solutions.model.cms.request.sys.SysRoleUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysRoleResponse;
import com.letu.solutions.model.entity.sys.SysRole;
import com.letu.solutions.model.entity.sys.SysUserRoleRel;
import com.letu.solutions.util.util.TreeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统角色 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    private final SysUserRoleRelMapper sysUserRoleRelMapper;
    private final LoginService loginService;

    @Override
    public List<SysRoleResponse> selectBasicList(SysRoleQueryReq request) {
        List<SysRole> sysRoles = baseMapper.selectList(Wrappers.<SysRole>lambdaQuery()
                .eq(StrUtil.isNotEmpty(request.getName()), SysRole::getName, request.getName())
                .eq(ObjectUtil.isNotNull(request.getEnable()), SysRole::getEnable, request.getEnable())
                .eq(SysRole::getDel, 0)
                .orderByAsc(SysRole::getParentId)
                .orderByAsc(SysRole::getId)
        );
        List<SysRoleResponse> resList = BeanUtil.copyToList(sysRoles, SysRoleResponse.class);
        return TreeUtil.listToTree(resList);
    }

    @Override
    public SysRoleResponse selectByIdBasic(Long id) {
        return BeanUtil.copyProperties(baseMapper.selectById(id), SysRoleResponse.class);
    }

    @Override
    public boolean saveBasic(SysRoleSaveReq record, ExtendData extendData) {
        SysRole body = BeanUtil.copyProperties(record, SysRole.class);
        return baseMapper.insert(body) > 0;
    }

    @Override
    public boolean updateBasic(SysRoleUpdateReq record, ExtendData extendData) {
        SysRole body = BeanUtil.copyProperties(record, SysRole.class);
        return baseMapper.updateById(body) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.update(null,
                Wrappers.<SysRole>lambdaUpdate()
                        .set(SysRole::getDel, 1)
                        .eq(SysRole::getId, id)) > 0;
    }

    @Async("asyncPool")
    @Override
    public void refreshToken(Long roleId) {
        List<SysUserRoleRel> sysUserRoleRels = sysUserRoleRelMapper.selectList(Wrappers.<SysUserRoleRel>lambdaQuery().eq(SysUserRoleRel::getRoleId, roleId));
        List<Long> userIds = sysUserRoleRels.stream().map(SysUserRoleRel::getUserId).collect(Collectors.toList());
        for (Long userId : userIds) {
            loginService.refresh(userId);
        }
    }
}