package com.letu.solutions.cms.service.cms.impl;

import com.letu.solutions.model.entity.cms.TaskStep;
import com.letu.solutions.cms.mapper.cms.TaskStepMapper;
import com.letu.solutions.cms.service.cms.TaskStepService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.TaskStepListReq;
import com.letu.solutions.model.cms.request.cms.TaskStepSaveReq;
import com.letu.solutions.model.cms.request.cms.TaskStepUpdateReq;
import com.letu.solutions.model.cms.response.cms.TaskStepPageRes;
import com.letu.solutions.model.cms.response.cms.TaskStepDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;

/**
 * 步骤 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskStepServiceImpl extends ServiceImpl<TaskStepMapper, TaskStep> implements TaskStepService {
    @Override
    public Page<TaskStepPageRes> selectBasicPage(Page<TaskStep> page, TaskStepListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<TaskStep> queryWrapper = Wrappers.<TaskStep>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),TaskStep::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getTaskId()),TaskStep::getTaskId, request.getTaskId())
                .eq(ObjectUtil.isNotEmpty(request.getPrice()),TaskStep::getPrice, request.getPrice())
                .eq(ObjectUtil.isNotEmpty(request.getSrot()),TaskStep::getSrot, request.getSrot())
               ;

        Page<TaskStep> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, TaskStepPageRes.class);
        }



    @Override
    public List<TaskStepPageRes> selectBasicList(TaskStepListReq request) {
        List<TaskStep> basicList = baseMapper.selectList(Wrappers.<TaskStep>lambdaQuery());
        return BeanUtil.copyToList(basicList, TaskStepPageRes.class);
    }

    @Override
    public TaskStepDetailRes selectByIdBasic(Long id) {
        TaskStep record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, TaskStepDetailRes.class);
    }

    @Override
    public boolean saveBasic(TaskStepSaveReq record, ExtendData extendData) {
        TaskStep saveRecord = BeanUtil.copyProperties(record, TaskStep.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(TaskStepUpdateReq record, ExtendData extendData) {
        TaskStep updateRecord = BeanUtil.copyProperties(record, TaskStep.class);

        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }
}