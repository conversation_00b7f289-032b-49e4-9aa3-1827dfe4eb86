package com.letu.solutions.cms.service.cms.impl;

import com.letu.solutions.model.dto.PlatformTransactionBillDto;
import com.letu.solutions.model.entity.cms.PlatformTransactionBill;
import com.letu.solutions.cms.mapper.cms.PlatformTransactionBillMapper;
import com.letu.solutions.cms.service.cms.PlatformTransactionBillService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import com.letu.solutions.util.util.TimeUtil;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.Date;
import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.PlatformTransactionBillListReq;
import com.letu.solutions.model.cms.request.cms.PlatformTransactionBillSaveReq;
import com.letu.solutions.model.cms.request.cms.PlatformTransactionBillUpdateReq;
import com.letu.solutions.model.cms.response.cms.PlatformTransactionBillPageRes;
import com.letu.solutions.model.cms.response.cms.PlatformTransactionBillDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.PlatformTransactionBillExcel;


/**
 * 平台账户流水表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PlatformTransactionBillServiceImpl extends ServiceImpl<PlatformTransactionBillMapper, PlatformTransactionBill> implements PlatformTransactionBillService {
    @Override
    public Page<PlatformTransactionBillPageRes> selectBasicPage(Page<PlatformTransactionBill> page, PlatformTransactionBillListReq request) {
        // 使用新的查询方法，包含协议网络、平台钱包地址、操作人员信息
        Page<PlatformTransactionBillPageRes> resultPage = new Page<>(page.getCurrent(), page.getSize());
        return baseMapper.selectPageWithDetails(resultPage, request);
    }



    @Override
    public List<PlatformTransactionBillPageRes> selectBasicList(PlatformTransactionBillListReq request) {
        List<PlatformTransactionBill> basicList = baseMapper.selectList(Wrappers.<PlatformTransactionBill>lambdaQuery());
        return BeanUtil.copyToList(basicList, PlatformTransactionBillPageRes.class);
    }

    @Override
    public PlatformTransactionBillDetailRes selectByIdBasic(Long id) {
        PlatformTransactionBill record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, PlatformTransactionBillDetailRes.class);
    }

    @Override
    public boolean saveBasic(PlatformTransactionBillSaveReq record, ExtendData extendData) {
        PlatformTransactionBill saveRecord = BeanUtil.copyProperties(record, PlatformTransactionBill.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(PlatformTransactionBillUpdateReq record, ExtendData extendData) {
        PlatformTransactionBill updateRecord = BeanUtil.copyProperties(record, PlatformTransactionBill.class);
        updateRecord.setUpdateTime(DateUtil.date());
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public void createPlatformTransactionBill(PlatformTransactionBillDto bill, ExtendData extendData) {
        PlatformTransactionBillSaveReq req = PlatformTransactionBillSaveReq.builder()
                .userId(bill.getUserId())
                .userName(bill.getUserName())
                .accountType(bill.getAccountType())
                .fundType(bill.getFundType())
                .side(bill.getSide())
                .amount(bill.getAmount())
                .currency(bill.getCurrency())
                .fee(bill.getFee())
                .balanceBefore(bill.getBefore())
                .balanceAfter(bill.getAfter())
                .fundId(bill.getFundId())
                .remark(bill.getRemark())
                .createdAt(new java.util.Date())
                .updatedAt(new java.util.Date())
                .platformUserId(bill.getPlatformUserId())
                .day(TimeUtil.today(new Date()))
                .build();
        this.saveBasic(req, extendData);
    }

    @Override
    public String exportExcel(PlatformTransactionBillListReq request) {
        // 查询数据
        List<PlatformTransactionBillPageRes> data = selectBasicList(request);
        // 转为Excel DTO
        List<PlatformTransactionBillExcel> excelList = cn.hutool.core.bean.BeanUtil.copyToList(data, PlatformTransactionBillExcel.class);
        return ExcelUtil.getExcel("平台流水导出", PlatformTransactionBillExcel.class, excelList, false);
    }
}