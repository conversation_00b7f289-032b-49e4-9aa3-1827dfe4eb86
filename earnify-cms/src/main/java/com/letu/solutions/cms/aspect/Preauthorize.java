
package com.letu.solutions.cms.aspect;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限校验注解
 * <AUTHOR>
 * @since 2025-01-08
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface Preauthorize {
	/**
	 * 权限值
	 */
	String value();
	/**
	 * 权限描述
	 */
	String valueDesc();

	/**
	 * 免校验
	 */
	boolean disCheck() default false;
}
