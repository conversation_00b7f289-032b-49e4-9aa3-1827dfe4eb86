package com.letu.solutions.cms.mapper.cms;

import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 账户资金信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper

public interface UserAccountBalanceMapper extends BaseMapper<UserAccountBalance> {

    /**
     * 查询指定用户的冻结金额
     */
    BigDecimal selectFrozenAmountByUserId(@Param("userId") Long userId);

    /**
     * 统计所有用户冻结奖励金额总和
     * @return 冻结奖励金额总和（BigDecimal）
     */
    BigDecimal sumAllFrozenAmount();

    /**
     * 批量获取用户当前余额
     * @param userIds 用户ID列表
     * @return 用户当前余额映射
     */
    @Select("""
            <script>
            SELECT
                CAST(user_id AS SIGNED) as userId,
                COALESCE(available_amount, 0) as availableAmount
            FROM user_account_balance
            WHERE user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            </script>
            """)
    List<Map<String, Object>> getCurrentBalanceByUserIds(@Param("userIds") List<Long> userIds);
}
