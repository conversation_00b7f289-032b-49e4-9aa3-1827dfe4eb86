package com.letu.solutions.cms.mapper.cms;

import com.letu.solutions.model.entity.cms.PlatformWalletAddress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Select;
import java.util.List;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressWithConfigRes;

/**
 * 平台钱包地址表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Mapper

public interface PlatformWalletAddressMapper extends BaseMapper<PlatformWalletAddress> {

}
