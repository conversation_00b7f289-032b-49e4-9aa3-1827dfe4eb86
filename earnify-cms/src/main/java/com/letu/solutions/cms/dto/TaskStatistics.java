package com.letu.solutions.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 任务统计数据DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskStatistics {
    private Long userId;
    private Integer taskCount = 0;              // 发布次数
    private Integer totalTaskNumber = 0;       // 发布个数
    private Integer totalReceiveNum = 0;       // 已领取数量
    private Integer totalFinishNum = 0;        // 已完成数量
    private BigDecimal totalGrantedAmount = BigDecimal.ZERO;  // 已发放奖励
    private BigDecimal totalFrozenAmount = BigDecimal.ZERO;   // 任务冻结中
}
