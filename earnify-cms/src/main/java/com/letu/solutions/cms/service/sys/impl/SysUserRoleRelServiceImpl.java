package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.cms.mapper.sys.SysUserRoleRelMapper;
import com.letu.solutions.cms.service.sys.SysUserRoleRelService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysUserRoleRelSaveReq;
import com.letu.solutions.model.entity.sys.SysUserRoleRel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户-角色关系 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysUserRoleRelServiceImpl extends ServiceImpl<SysUserRoleRelMapper, SysUserRoleRel> implements SysUserRoleRelService {

    @Override
    public List<Long> selectBasicList(Long userId) {
        List<SysUserRoleRel> resList = baseMapper.selectList(Wrappers.<SysUserRoleRel>lambdaQuery().eq(SysUserRoleRel::getUserId, userId));
        if (CollectionUtil.isEmpty(resList)) {
            return ListUtil.empty();
        }
        return resList.stream().map(SysUserRoleRel::getRoleId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUserRole(SysUserRoleRelSaveReq record, ExtendData extendData) {
        List<SysUserRoleRel> list = new ArrayList<>();
        for (Long roleId : record.getRoleIds()) {
            SysUserRoleRel sysUserRoleRel = new SysUserRoleRel();
            sysUserRoleRel.setRoleId(roleId);
            sysUserRoleRel.setUserId(record.getUserId());
            list.add(sysUserRoleRel);
        }
        baseMapper.delete(Wrappers.<SysUserRoleRel>lambdaQuery().eq(SysUserRoleRel::getUserId, record.getUserId()));
        if (CollectionUtil.isNotEmpty(list)) {
            this.saveBatch(list);
        }
        return true;
    }
}