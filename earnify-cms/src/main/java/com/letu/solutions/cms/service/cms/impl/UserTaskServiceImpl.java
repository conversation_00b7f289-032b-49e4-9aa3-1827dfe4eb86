package com.letu.solutions.cms.service.cms.impl;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.TaskExcel;
import com.letu.solutions.cms.dto.UserTaskExcel;
import com.letu.solutions.cms.mapper.cms.PlatformTransactionBillMapper;
import com.letu.solutions.cms.mapper.cms.TaskMapper;
import com.letu.solutions.cms.mapper.cms.UserTaskStepMapper;
import com.letu.solutions.cms.service.cms.UserAccountBalanceService;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.model.cms.request.cms.AppealRecordUpdateReq;
import com.letu.solutions.model.entity.cms.*;
import com.letu.solutions.cms.mapper.cms.UserTaskMapper;
import com.letu.solutions.cms.service.cms.UserTaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.UserTaskListReq;
import com.letu.solutions.model.cms.request.cms.UserTaskSaveReq;
import com.letu.solutions.model.cms.request.cms.UserTaskUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserTaskPageRes;
import com.letu.solutions.model.cms.response.cms.UserTaskDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;

/**
 * 任务 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserTaskServiceImpl extends ServiceImpl<UserTaskMapper, UserTask> implements UserTaskService {
    private final UserAccountBalanceService userAccountBalanceService;
    private final PlatformTransactionBillMapper platformTransactionBillMapper;
    private final TransactionalManage transactionalManage;
    private final TaskMapper taskMapper;
    private final UserTaskStepMapper userTaskStepMapper;
    @Override
    public Page<UserTaskPageRes> selectBasicPage(Page<UserTask> page, UserTaskListReq request) {
        // 调用多表联合分页SQL
        return baseMapper.selectUserTaskPage(page, request);
    }



    @Override
    public String selectBasicList(UserTaskListReq request) {
        List<UserTaskExcel> basicList = baseMapper.selectUserTaskList(request);
        return ExcelUtil.getExcel("任务记录", UserTaskExcel.class, basicList, false);
    }

    @Override
    public UserTaskDetailRes selectByIdBasic(Long id,Long partyBUserId) {
        UserTaskDetailRes detail = baseMapper.selectUserTaskDetailWithSteps(id,partyBUserId);
        if (detail != null && detail.getSteps() != null) {
            int total = detail.getSteps().size();
            int finished = (int) detail.getSteps().stream().filter(s -> 1 == s.getSaved()).count();
            detail.setStepStatusSummary(finished + "/" + total);
        }
        return detail;
    }

    @Override
    public boolean updateBasic(UserTaskUpdateReq record) {
        UserTask userTask= baseMapper.selectOne(Wrappers.<UserTask>lambdaQuery()
                .eq(UserTask::getUserId, record.getUserId())
                .eq(UserTask::getTaskId, record.getTaskId()));
        Assert.notNull(userTask,"参数错误");
        userTask.setExamineTime(new Date());
        // 查询任务获取price
        Task task = taskMapper.selectById(record.getTaskId());
        BigDecimal amount = task.getPrice();
        // 甲方余额
        UserAccountBalance aBalance = userAccountBalanceService.getOne(Wrappers.<UserAccountBalance>lambdaQuery().eq(UserAccountBalance::getUserId, task.getUserId()));
        // 乙方余额
        UserAccountBalance bBalance = userAccountBalanceService.getOne(Wrappers.<UserAccountBalance>lambdaQuery().eq(UserAccountBalance::getUserId, record.getUserId()));
        Boolean result = transactionalManage.execute(() -> {
            Boolean un=true;
            //通过
            if (record.getState() == 1 ) {
                userTask.setState(UserTaskStatusEnum.completed);
                un = baseMapper.updateById(userTask) > 0;
                // 甲方冻结金额减少
                un = userAccountBalanceService.update(Wrappers.<UserAccountBalance>lambdaUpdate()
                        .eq(UserAccountBalance::getUserId, task.getUserId())
                        .eq(UserAccountBalance::getFrozenAmount, aBalance.getFrozenAmount())
                        .setSql("frozen_amount = frozen_amount - " + amount));
                // 甲方冻结金额减少流水
                PlatformTransactionBill freezeBill = new PlatformTransactionBill();
                freezeBill.setUserId(task.getUserId());
                freezeBill.setAccountType(AccountTypeEnum.client); // 甲方
                freezeBill.setFundType(FundTypeEnum.task_reward_unfreeze_send); // 提现/扣除
                freezeBill.setSide(FundSideTypeEnum.out); // 出账
                freezeBill.setAmount(amount);
                freezeBill.setCurrency("USDT");
                freezeBill.setFee(BigDecimal.ZERO);
                freezeBill.setBalanceBefore(aBalance.getFrozenAmount().add(amount));
                freezeBill.setBalanceAfter(aBalance.getFrozenAmount());
                freezeBill.setFundId(task.getId());
                freezeBill.setRemark("任务完成，甲方冻结金额减少");
                un =  platformTransactionBillMapper.insert(freezeBill) > 0;
                // 乙方余额增加
                un = userAccountBalanceService.update(Wrappers.<UserAccountBalance>lambdaUpdate()
                        .eq(UserAccountBalance::getUserId, record.getUserId())
                        .eq(UserAccountBalance::getAvailableAmount,bBalance.getAvailableAmount())
                        .setSql("available_amount = available_amount + " + amount));
                // 乙方流水
                PlatformTransactionBill bill = new PlatformTransactionBill();
                bill.setUserId(record.getUserId());
                bill.setAccountType(AccountTypeEnum.provider); // 乙方
                bill.setFundType(FundTypeEnum.task_reward_to_account);
                bill.setSide(FundSideTypeEnum.in); // 入账
                bill.setAmount(amount);
                bill.setCurrency("USDT"); // 如有币种字段可替换
                bill.setFee(BigDecimal.ZERO);
                bill.setBalanceBefore(bBalance.getAvailableAmount().subtract(amount));
                bill.setBalanceAfter(bBalance.getAvailableAmount());
                bill.setFundId(task.getId());
                bill.setRemark("任务完成，乙方获得奖励");
                un = platformTransactionBillMapper.insert(freezeBill) > 0;
            } else{
                userTask.setState(UserTaskStatusEnum.partyARejects);
                userTask.setAppealTime(new Date());
                un = baseMapper.updateById(userTask) > 0;
            }
            return un;
        });
        Assert.isTrue(result, "任务审核失败");
        return result;
    }

    @Override
    public List<UserTaskStep> selectUserTask(Long userId, Long taskId) {
        return userTaskStepMapper.selectList(Wrappers.<UserTaskStep>lambdaQuery()
                .eq(UserTaskStep::getUserId, userId)
                .eq(UserTaskStep::getTaskId, taskId)
                .orderByAsc(UserTaskStep::getSrot));
    }

}