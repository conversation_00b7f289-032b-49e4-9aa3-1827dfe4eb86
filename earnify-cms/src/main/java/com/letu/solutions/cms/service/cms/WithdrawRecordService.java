package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordListReq;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordSaveReq;
import com.letu.solutions.model.cms.request.cms.WithdrawRecordUpdateReq;
import com.letu.solutions.model.cms.response.cms.WithdrawRecordPageRes;
import com.letu.solutions.model.cms.response.cms.WithdrawRecordDetailRes;

/**
 * 提现记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface WithdrawRecordService {
    Page<WithdrawRecordPageRes> selectBasicPage(Page<WithdrawRecord> page, WithdrawRecordListReq request);

    List<WithdrawRecordPageRes> selectBasicList(WithdrawRecordListReq request);

    WithdrawRecordDetailRes selectByIdBasic(Long id);

    boolean saveBasic(WithdrawRecordSaveReq record, ExtendData extendData);

    boolean updateBasic(WithdrawRecordUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    String exportExcel(WithdrawRecordListReq request);
}
