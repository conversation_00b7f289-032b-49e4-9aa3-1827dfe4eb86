package com.letu.solutions.cms.aspect;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.config.SpringUtil;
import com.letu.solutions.core.configuration.UploadConfig;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.util.util.upload.UploadUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;

/**
 * 导出Excel文件（导出“XLSX”格式，支持大数据量导出 @see org.apache.poi.ss.SpreadsheetVersion）
 *
 * <AUTHOR>
 * @version 20191227
 */
@Slf4j
public class ExcelUtil {
    /**
     * 持久化目录
     */
    private static final String FILE_PATH_PERSISTENCE = "/persistence";
    /**
     * 临时目录
     */
    private static final String FILE_PATH_TEMP = "/temp";

    /**
     * @param title  文件标题
     * @param cls    实体泛型
     * @param list   导出实体列表
     * @param temp   是否是临时数据
     * @param groups 分组
     * @return 文件地址
     */
    public static String getExcel(String title, Class<?> cls, List<?> list, boolean temp, int... groups) {
        Assert.isFalse(CollectionUtil.isEmpty(list), "无导出数据");
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            if (groups == null || groups.length < 1) {
                new ExportExcel(title, cls, 1)
                        .setDataList(list).write(baos);
            } else {
                new ExportExcel(title, cls, 1, groups)
                        .setDataList(list).write(baos);
            }
            UploadUtil uploadUtil = SpringUtil.getBean(UploadUtil.class);

            return uploadUtil.uploadExcel(baos.toByteArray(), getFilePath(temp) + "/" + getFileName(title));
        } catch (Exception e) {
            log.error("excel导出异常", e);
            throw new ThrowException("#excel导出异常，请联系管理员");
        }
    }

    /**
     * @param title  文件标题
     * @param cls    实体泛型
     * @param page   导出实体列表
     * @param temp   是否是临时数据
     * @param groups 分组
     * @return 文件地址
     */
    public static String getExcel(String title, Class<?> cls, Page<?> page, boolean temp, int... groups) {
        Assert.notNull(page, "无导出数据");
        try {
            List<?> list = page.getRecords();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            if (groups == null || groups.length < 1) {
                new ExportExcel(title, cls, 1)
                        .setDataList(list).write(baos);
            } else {
                new ExportExcel(title, cls, 1, groups)
                        .setDataList(list).write(baos);
            }
            UploadUtil uploadUtil = SpringUtil.getBean(UploadUtil.class);

            String uri = SpringUtil.getBean(UploadConfig.class).getBaseUrl() + uploadUtil.uploadExcel(baos.toByteArray(), getFilePath(temp) + "/" + getFileName(title));
            return uri;
        } catch (Exception e) {
            log.error("excel导出异常", e);
            throw new ThrowException("#excel导出异常，请联系管理员");
        }
    }

    /**
     * 获取导出文件名称
     *
     * @param title
     * @return 导出的文件名称
     */
    public static String getFileName(String title) {
        String fileName = String.format("%s-%s.xlsx", title, DateUtil.format(new Date(), "yyyy-MM-dd_HH:mm:dd"));
        return fileName;
    }

    public static String getFilePath(boolean temp) {
        return temp ? FILE_PATH_TEMP : FILE_PATH_PERSISTENCE;
    }
}
