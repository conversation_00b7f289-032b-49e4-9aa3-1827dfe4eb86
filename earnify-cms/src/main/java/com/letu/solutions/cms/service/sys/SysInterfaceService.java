package com.letu.solutions.cms.service.sys;

import com.letu.solutions.model.cms.response.sys.SysInterfaceResponse;
import com.letu.solutions.model.entity.sys.SysInterface;
import com.letu.solutions.model.request.BaseRequest;

import java.util.List;

/**
 * 系统接口 服务类
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface SysInterfaceService {
    String splitKey = ":";

    List<SysInterfaceResponse> selectBasicList(BaseRequest request);

    void scan();

    List<String> selectUserInterfaces(Long userId);
}
