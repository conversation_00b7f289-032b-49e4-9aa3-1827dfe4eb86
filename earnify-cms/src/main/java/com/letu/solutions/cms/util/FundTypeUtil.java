package com.letu.solutions.cms.util;

import com.letu.solutions.model.enums.cms.FrozenSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;

public class FundTypeUtil {

    public static FundSideTypeEnum getSideByFundType(FundTypeEnum fundType) {
        return fundType.getSide();
    }

    public static FrozenSideTypeEnum getFrozenSideType(FundTypeEnum fundType) {
        return fundType.getFrozenSide();
    }
}
