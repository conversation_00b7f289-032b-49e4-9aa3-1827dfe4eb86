package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.PlatformWalletAddress;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressListReq;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressSaveReq;
import com.letu.solutions.model.cms.request.cms.PlatformWalletAddressUpdateReq;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressPageRes;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressDetailRes;
import com.letu.solutions.model.cms.response.cms.PlatformWalletAddressWithConfigRes;

/**
 * 平台钱包地址表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface PlatformWalletAddressService {
    Page<PlatformWalletAddressPageRes> selectBasicPage(Page<PlatformWalletAddress> page, PlatformWalletAddressListReq request);

    List<PlatformWalletAddressPageRes> selectBasicList(PlatformWalletAddressListReq request);

    PlatformWalletAddressDetailRes selectByIdBasic(Long id);

    boolean saveBasic(PlatformWalletAddressSaveReq record, ExtendData extendData);

    boolean updateBasic(PlatformWalletAddressUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);

    List<PlatformWalletAddressWithConfigRes> selectByNetwork(String network);

    /**
     * 根据协议网络查询钱包地址列表
     * @param network 协议网络
     * @return 钱包地址列表
     */
    List<String> getAddressesByNetwork(String network);
}
