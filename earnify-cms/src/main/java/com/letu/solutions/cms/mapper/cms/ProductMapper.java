package com.letu.solutions.cms.mapper.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.dto.ProductExcel;
import com.letu.solutions.model.entity.cms.Product;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import java.util.Map;
import com.letu.solutions.model.cms.request.cms.ProductListReq;
import com.letu.solutions.model.cms.response.cms.ProductPageRes;
import java.util.List;

/**
 * 产品表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper

public interface ProductMapper extends BaseMapper<Product> {
    Page<ProductPageRes> selectProductPage(@Param("page") Page page,@Param("req") ProductListReq req);

    List<ProductExcel> selectProductList(@Param("req")ProductListReq req);

    /**
     * 批量获取用户关联产品数量
     * @param userIds 用户ID列表
     * @return 用户产品数量映射
     */
    @Select("""
            <script>
            SELECT
                CAST(party_user_id AS SIGNED) as userId,
                CAST(COUNT(*) AS SIGNED) as productCount
            FROM product
            WHERE party_user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            GROUP BY party_user_id
            </script>
            """)
    List<Map<String, Object>> getProductCountByUserIds(@Param("userIds") List<Long> userIds);
}
