package com.letu.solutions.cms.service.sys;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysMenuSaveReq;
import com.letu.solutions.model.cms.request.sys.SysMenuUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysMenuDetail;
import com.letu.solutions.model.cms.response.sys.SysMenuVo;

import java.util.List;

/**
 * 系统权限（菜单） 服务类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface SysMenuService {

    /**
     * 菜单保存
     */
    boolean saveMenu(SysMenuSaveReq request) throws RuntimeException;

    /**
     * 角色修改
     */
    boolean updateMenu(SysMenuUpdateReq request) throws RuntimeException;

    SysMenuDetail detail(Long menuId);

    /**
     * 查询菜单详情
     */
    List<SysMenuDetail> getMenuList();

    List<Long> getRoleMenuList(Long roleId);

    /**
     * 获取用户菜单树
     */
    List<SysMenuVo> selectUserMenus(Long userId);

    /**
     * 删除菜单
     */
    Boolean basicDelete(String menuId, ExtendData extendData);
}
