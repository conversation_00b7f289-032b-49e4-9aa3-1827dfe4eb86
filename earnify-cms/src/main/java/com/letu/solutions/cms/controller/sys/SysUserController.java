package com.letu.solutions.cms.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.sys.LoginService;
import com.letu.solutions.cms.service.sys.SysMenuService;
import com.letu.solutions.cms.service.sys.SysUserRoleRelService;
import com.letu.solutions.cms.service.sys.SysUserService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.dubbo.middle.cms.dto.SysUserDto;
import com.letu.solutions.dubbo.middle.cms.req.SysUserQueryReq;
import com.letu.solutions.model.cms.request.sys.SysUserRoleRelSaveReq;
import com.letu.solutions.model.cms.response.sys.SysMenuVo;
import com.letu.solutions.model.cms.response.sys.SysUserResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统/后台系统用户
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "sys:user", valueDesc = "系统:用户")
public class SysUserController {
    private final SysUserService basicService;
    private final SysUserRoleRelService sysUserRoleRelService;
    private final SysMenuService sysMenuService;
    private final LoginService loginService;

    /**
     * 后台系统用户表 分页查询
     */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/sysUser/page")
    public R<Page<SysUserDto>> loadPage(SysUserQueryReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 后台系统用户表 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/sysUser/list")
    public R<List<SysUserDto>> loadList(SysUserQueryReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 后台系统用户表 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "详情查询")
    @GetMapping("/sysUser/selectById")
    public R<SysUserResponse> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 后台系统 用户已绑角色查询
     *
     * @param userId 用户id
     */
    @Preauthorize(value = "userRoleList", valueDesc = "用户已绑角色查询")
    @GetMapping("/sysUser/userRoleList")
    public R<List<Long>> loadUserRoleList(@RequestParam("userId") Long userId) {
        return R.success(sysUserRoleRelService.selectBasicList(userId));
    }


    /**
     * 后台系统 用户绑定角色修改
     */
    @Preauthorize(value = "saveUserRole", valueDesc = "用户绑定角色修改")
    @PostMapping("/sysUser/saveUserRole")
    public R<Boolean> saveUserRole(@Validated @RequestBody SysUserRoleRelSaveReq request, @RequestHeader ExtendData extendData) {
        boolean b = sysUserRoleRelService.saveUserRole(request, extendData);
        if (b) {
            loginService.refresh(request.getUserId());
        }
        return R.success(b);
    }

    /**
     * 获取登录用户已有菜单树
     */
    @GetMapping("/sysUser/selectUserMenus")
    public R<List<SysMenuVo>> getUserMenuList(@RequestHeader ExtendData extendData) {
        return R.success(sysMenuService.selectUserMenus(extendData.getUserId()));
    }
}
