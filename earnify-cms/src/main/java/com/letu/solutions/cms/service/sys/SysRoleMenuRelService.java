package com.letu.solutions.cms.service.sys;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.cms.request.sys.SysRoleMenuRelSaveReq;

import java.util.List;

/**
 * 系统角色-菜单关系 服务类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface SysRoleMenuRelService {
    List<Long> selectBasicList(Long roleId);

    boolean saveRoleMenu(SysRoleMenuRelSaveReq record, ExtendData extendData);
}
