package com.letu.solutions.cms.service.cms.impl;

import com.letu.solutions.model.entity.cms.UserKycAuth;
import com.letu.solutions.cms.mapper.cms.UserKycAuthMapper;
import com.letu.solutions.cms.service.cms.UserKycAuthService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.UserKycAuthListReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthSaveReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthUpdateReq;
import com.letu.solutions.model.cms.request.cms.UserKycAuthVerifyReq;
import com.letu.solutions.model.cms.response.cms.UserKycAuthPageRes;
import com.letu.solutions.model.cms.response.cms.UserKycAuthDetailRes;
import com.letu.solutions.model.cms.response.cms.UserKycAuthInfoRes;
import com.letu.solutions.model.enums.cms.UserVerifyStatusEnum;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;

/**
 * KYC身份认证表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserKycAuthServiceImpl extends ServiceImpl<UserKycAuthMapper, UserKycAuth> implements UserKycAuthService {
    @Override
    public Page<UserKycAuthPageRes> selectBasicPage(Page<UserKycAuth> page, UserKycAuthListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<UserKycAuth> queryWrapper = Wrappers.<UserKycAuth>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),UserKycAuth::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getUserId()),UserKycAuth::getUserId, request.getUserId())
                .eq(ObjectUtil.isNotEmpty(request.getIdType()),UserKycAuth::getIdType, request.getIdType())
                .eq(ObjectUtil.isNotEmpty(request.getIdNumber()),UserKycAuth::getIdNumber, request.getIdNumber())
                .eq(ObjectUtil.isNotEmpty(request.getDocumentImages()),UserKycAuth::getDocumentImages, request.getDocumentImages())
                .eq(ObjectUtil.isNotEmpty(request.getSubmitTime()),UserKycAuth::getSubmitTime, request.getSubmitTime())
                .eq(ObjectUtil.isNotEmpty(request.getVerifyStatus()),UserKycAuth::getVerifyStatus, request.getVerifyStatus())
                .eq(ObjectUtil.isNotEmpty(request.getVerifyTime()),UserKycAuth::getVerifyTime, request.getVerifyTime())
                .eq(ObjectUtil.isNotEmpty(request.getRemark()),UserKycAuth::getRemark, request.getRemark());

        Page<UserKycAuth> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, UserKycAuthPageRes.class);
        }



    @Override
    public List<UserKycAuthPageRes> selectBasicList(UserKycAuthListReq request) {
        List<UserKycAuth> basicList = baseMapper.selectList(Wrappers.<UserKycAuth>lambdaQuery());
        return BeanUtil.copyToList(basicList, UserKycAuthPageRes.class);
    }

    @Override
    public UserKycAuthDetailRes selectByIdBasic(Long id) {
        UserKycAuth record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, UserKycAuthDetailRes.class);
    }

    @Override
    public boolean saveBasic(UserKycAuthSaveReq record, ExtendData extendData) {
        UserKycAuth saveRecord = BeanUtil.copyProperties(record, UserKycAuth.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(UserKycAuthUpdateReq record, ExtendData extendData) {
        UserKycAuth updateRecord = BeanUtil.copyProperties(record, UserKycAuth.class);
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public boolean verifyUserKyc(UserKycAuthVerifyReq request, ExtendData extendData) {
        log.info("开始审核用户KYC认证，用户ID：{}，审核状态：{}", request.getUserId(), request.getVerifyStatus());

        // 1. 查询用户KYC认证记录
        UserKycAuth kycAuth = baseMapper.selectOne(
                Wrappers.<UserKycAuth>lambdaQuery()
                        .eq(UserKycAuth::getUserId, request.getUserId())
        );

        Assert.notNull(kycAuth, "用户KYC认证记录不存在");

        // 2. 验证审核状态
        if (request.getVerifyStatus() == UserVerifyStatusEnum.rejected) {
            Assert.hasText(request.getRejectReason(), "审核不通过时必须填写拒绝原因");
        }

        // 3. 更新审核状态
        UserKycAuth updateRecord = new UserKycAuth();
        updateRecord.setId(kycAuth.getId());
        updateRecord.setVerifyStatus(request.getVerifyStatus());
        updateRecord.setRemark(request.getRejectReason());
        updateRecord.setVerifyTime(DateUtil.date());

        boolean result = baseMapper.updateById(updateRecord) > 0;

        if (result) {
            log.info("用户KYC认证审核完成，用户ID：{}，审核状态：{}", request.getUserId(), request.getVerifyStatus());
        } else {
            log.error("用户KYC认证审核失败，用户ID：{}", request.getUserId());
        }

        return result;
    }

    @Override
    public UserKycAuthInfoRes getUserKycAuthInfo(Long userId) {
        log.info("查询用户KYC认证信息，用户ID：{}", userId);

        // 查询用户KYC认证记录
        UserKycAuth kycAuth = baseMapper.selectOne(
                Wrappers.<UserKycAuth>lambdaQuery()
                        .eq(UserKycAuth::getUserId, userId)
        );

        if (kycAuth == null) {
            log.warn("用户KYC认证记录不存在，用户ID：{}", userId);
            return null;
        }

        // 处理证件图片
        List<String> documentImages = kycAuth.getDocumentImages();

        return UserKycAuthInfoRes.builder()
                .userId(kycAuth.getUserId())
                .idType(kycAuth.getIdType())
                .idNumber(kycAuth.getIdNumber())
                .documentCount(documentImages.size())
                .documentImages(documentImages)
                .verifyStatus(kycAuth.getVerifyStatus())
                .rejectReason(kycAuth.getRemark())
                .build();
    }
}