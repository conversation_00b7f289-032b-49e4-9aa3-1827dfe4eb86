package com.letu.solutions.cms.service.cms.impl;

import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.cms.mapper.cms.UserAccountBalanceMapper;
import com.letu.solutions.cms.service.cms.UserAccountBalanceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceListReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceSaveReq;
import com.letu.solutions.model.cms.request.cms.UserAccountBalanceUpdateReq;
import com.letu.solutions.model.cms.response.cms.UserAccountBalancePageRes;
import com.letu.solutions.model.cms.response.cms.UserAccountBalanceDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;

/**
 * 账户资金信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserAccountBalanceServiceImpl extends ServiceImpl<UserAccountBalanceMapper, UserAccountBalance> implements UserAccountBalanceService {
    @Override
    public Page<UserAccountBalancePageRes> selectBasicPage(Page<UserAccountBalance> page, UserAccountBalanceListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getId()),UserAccountBalance::getId, request.getId())
                .eq(ObjectUtil.isNotEmpty(request.getUserId()),UserAccountBalance::getUserId, request.getUserId())
                .eq(ObjectUtil.isNotEmpty(request.getAvailableAmount()),UserAccountBalance::getAvailableAmount, request.getAvailableAmount())
                .eq(ObjectUtil.isNotEmpty(request.getFrozenAmount()),UserAccountBalance::getFrozenAmount, request.getFrozenAmount())
                .eq(ObjectUtil.isNotEmpty(request.getCurrency()),UserAccountBalance::getCurrency, request.getCurrency());

        Page<UserAccountBalance> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, UserAccountBalancePageRes.class);
        }



    @Override
    public List<UserAccountBalancePageRes> selectBasicList(UserAccountBalanceListReq request) {
        List<UserAccountBalance> basicList = baseMapper.selectList(Wrappers.<UserAccountBalance>lambdaQuery());
        return BeanUtil.copyToList(basicList, UserAccountBalancePageRes.class);
    }

    @Override
    public UserAccountBalanceDetailRes selectByIdBasic(Long id) {
        UserAccountBalance record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, UserAccountBalanceDetailRes.class);
    }

    @Override
    public boolean saveBasic(UserAccountBalanceSaveReq record, ExtendData extendData) {
        UserAccountBalance saveRecord = BeanUtil.copyProperties(record, UserAccountBalance.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(UserAccountBalanceUpdateReq record, ExtendData extendData) {
        UserAccountBalance updateRecord = BeanUtil.copyProperties(record, UserAccountBalance.class);
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }
}