package com.letu.solutions.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 提现统计数据DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawStatistics {
    private Long userId;
    private BigDecimal withdrawingAmount = BigDecimal.ZERO;   // 提现中
    private BigDecimal withdrawnAmount = BigDecimal.ZERO;     // 已提现
}
