package com.letu.solutions.cms.controller.sys;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.sys.SysParamService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.sys.SysParamListReq;
import com.letu.solutions.model.cms.request.sys.SysParamSaveReq;
import com.letu.solutions.model.cms.request.sys.SysParamUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysParamPageRes;
import com.letu.solutions.model.cms.response.sys.SysParamDetailRes;

/**
 * 系统/系统参数
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "sys:param", valueDesc = "系统:系统参数")
public class SysParamController{
    private final SysParamService basicService;
    /**
    * 系统参数 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/sysParam/page")
    public R<Page<SysParamPageRes>> loadPage(SysParamListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 系统参数 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/sysParam/list")
    public R<List<SysParamPageRes>> loadList(SysParamListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 系统参数 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/sysParam/selectById")
    public R<SysParamDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 系统参数 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/sysParam/insert")
    public R<Boolean> insert(@Validated @RequestBody SysParamSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 系统参数 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/sysParam/update")
    public R<Boolean> update(@Validated @RequestBody SysParamUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 系统参数 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/sysParam/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(Long.valueOf(request.getId()), extendData));
    }
}
