package com.letu.solutions.cms.controller.cms;

import com.letu.solutions.cms.aspect.ExcelUtil;
import com.letu.solutions.cms.dto.ProductExcel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.DepositRecordService;
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.DepositRecordListReq;
import com.letu.solutions.model.cms.request.cms.DepositRecordSaveReq;
import com.letu.solutions.model.cms.request.cms.DepositRecordUpdateReq;
import com.letu.solutions.model.cms.response.cms.DepositRecordPageRes;
import com.letu.solutions.model.cms.response.cms.DepositRecordDetailRes;

/**
 * 财务管理/用户充值管理
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:deposit_record", valueDesc = "用户:充值记录表")
public class DepositRecordController{
    private final DepositRecordService basicService;
    /**
    * 充值记录 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/depositRecord/page")
    public R<Page<DepositRecordPageRes>> loadPage(@Validated DepositRecordListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }
    /**
     * 充值记录 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/depositRecord/list")
    public R<List<DepositRecordPageRes>> loadList(DepositRecordListReq request) {
        return R.success(basicService.selectBasicList(request));
    }
    /**
     * 充值记录 Excel导出
     */
    @Preauthorize(value = "list", valueDesc = "充值记录导出")
    @GetMapping("/depositRecord/exportExcel")
    public R<String> exportExcel(DepositRecordListReq request) {
        return R.success(basicService.exportExcel(request));
    }
    /**
     * 充值记录 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/depositRecord/selectById")
    public R<DepositRecordDetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }
    /**
     * 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/depositRecord/insert")
    public R<Boolean> insert(@Validated @RequestBody DepositRecordSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }
    /**
     * 充值记录 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/depositRecord/update")
    public R<Boolean> update(@Validated @RequestBody DepositRecordUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }
    /**
     * 充值记录 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/depositRecord/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }
}
