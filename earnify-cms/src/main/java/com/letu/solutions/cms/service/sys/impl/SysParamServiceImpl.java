package com.letu.solutions.cms.service.sys.impl;

import cn.hutool.core.util.ObjectUtil;
import com.letu.solutions.model.entity.sys.SysParam;
import com.letu.solutions.cms.mapper.sys.SysParamMapper;
import com.letu.solutions.cms.service.sys.SysParamService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.model.cms.request.sys.SysParamListReq;
import com.letu.solutions.model.cms.request.sys.SysParamSaveReq;
import com.letu.solutions.model.cms.request.sys.SysParamUpdateReq;
import com.letu.solutions.model.cms.response.sys.SysParamPageRes;
import com.letu.solutions.model.cms.response.sys.SysParamDetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;

/**
 * 系统参数 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysParamServiceImpl extends ServiceImpl<SysParamMapper, SysParam> implements SysParamService {
    @Override
    public Page<SysParamPageRes> selectBasicPage(Page<SysParam> page, SysParamListReq request) {
        Page<SysParam> basicPage = baseMapper.selectPage(page, Wrappers.<SysParam>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getKey()), SysParam::getKey, request.getKey())
                .eq(ObjectUtil.isNotEmpty(request.getValue()), SysParam::getValue, request.getValue())
                .eq(ObjectUtil.isNotEmpty(request.getName()), SysParam::getName, request.getName())
                .eq(ObjectUtil.isNotEmpty(request.getId()), SysParam::getId, request.getId())
        );
        return PageUtil.builderPage(basicPage, SysParamPageRes.class);
    }

    @Override
    public List<SysParamPageRes> selectBasicList(SysParamListReq request) {
        List<SysParam> basicList = baseMapper.selectList(Wrappers.<SysParam>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(request.getKey()), SysParam::getKey, request.getKey())
                .eq(ObjectUtil.isNotEmpty(request.getValue()), SysParam::getValue, request.getValue())
                .eq(ObjectUtil.isNotEmpty(request.getName()), SysParam::getName, request.getName())
                .eq(ObjectUtil.isNotEmpty(request.getId()), SysParam::getId, request.getId()));
        return BeanUtil.copyToList(basicList, SysParamPageRes.class);
    }

    @Override
    public SysParamDetailRes selectByIdBasic(Long id) {
        SysParam record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, SysParamDetailRes.class);
    }

    @Override
    public boolean saveBasic(SysParamSaveReq record, ExtendData extendData) {
        SysParam saveRecord = BeanUtil.copyProperties(record, SysParam.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(SysParamUpdateReq record, ExtendData extendData) {
        SysParam updateRecord = BeanUtil.copyProperties(record, SysParam.class);
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        return baseMapper.deleteById(id) > 0;
    }
}