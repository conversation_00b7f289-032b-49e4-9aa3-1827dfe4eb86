package com.letu.solutions.cms.dto;

import java.io.Serializable;
import java.util.Date;

import com.letu.solutions.cms.aspect.ExcelField;
import com.letu.solutions.model.enums.cms.TaskTypeEnum;
import lombok.Data;

/**
 * 申述记录表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class AppealRecordExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @ExcelField(title = "任务ID")
    private Long taskId;
    /** 任务名称 */
    @ExcelField(title = "任务名称")
    private String taskName;
    /**
     * 甲方用户ID
     */
    @ExcelField(title = "甲方用户ID")
    private Long partyAUserId;
    /**
     * 甲方用户名
     */
    @ExcelField(title = "甲方用户名")
    private String partyAUsername;
    /**
     * 乙方用户ID
     */
    @ExcelField(title = "乙方用户ID")
    private Long partyBUserId;
    /**
     * 乙方用户名
     */
    @ExcelField(title = "乙方用户名")
    private String partyBUsername;
    /**
     * 产品ID
     */
    @ExcelField(title = "产品ID")
    private Long productId;
    /**
     * 产品名称
     */
    @ExcelField(title = "产品名称")
    private String productName;
    /**
     * 任务属性 1人工 2自动
     */
    @ExcelField(title = "任务属性 1人工 2自动")
    private Integer taskAttribute;
    /**
     * 任务类型
     */
    @ExcelField(title = "任务类型")
    private TaskTypeEnum taskType;
    /**
     * 任务完成时限
     */
    @ExcelField(title = "任务完成时限")
    private Integer time;
    /**
     * 申述状态（0待审核，1通过，2不通过
     */
    @ExcelField(title = "申述状态（0待审核，1通过，2不通过")
    private Integer appealStatus;
    /**
     * 任务奖励
     */
    @ExcelField(title = "任务奖励")
    private Integer price;
    /**
     * 创建时间
     */
    @ExcelField(title = "创建时间")
    private String createTime;
}
