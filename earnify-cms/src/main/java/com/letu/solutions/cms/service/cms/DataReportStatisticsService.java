package com.letu.solutions.cms.service.cms;

import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.DataReportStatistics;
import java.util.List;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsListReq;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsSaveReq;
import com.letu.solutions.model.cms.request.cms.DataReportStatisticsUpdateReq;
import com.letu.solutions.model.cms.response.cms.DataReportStatisticsPageRes;
import com.letu.solutions.model.cms.response.cms.DataReportStatisticsDetailRes;
import com.letu.solutions.model.cms.request.cms.UserGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.UserGrowthTrendRes;
import com.letu.solutions.model.cms.request.cms.TaskGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.TaskGrowthTrendRes;
import com.letu.solutions.model.cms.request.cms.FundGrowthTrendReq;
import com.letu.solutions.model.cms.response.cms.FundGrowthTrendRes;
import com.letu.solutions.model.cms.response.cms.TodoStatisticsRes;
import com.letu.solutions.model.cms.response.cms.FinanceStatisticsRes;

/**
 * 数据简报统计 服务类
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface DataReportStatisticsService {
    Page<DataReportStatisticsPageRes> selectBasicPage(Page<DataReportStatistics> page, DataReportStatisticsListReq request);

    List<DataReportStatisticsPageRes> selectBasicList(DataReportStatisticsListReq request);

    DataReportStatisticsDetailRes selectByIdBasic(Long id);

    /**
     * 根据日期查询单条统计数据
     * @param date 日期字符串，格式：yyyyMMdd
     * @return 统计数据，如果没有找到则返回null
     */
    DataReportStatisticsPageRes selectByDate(Integer date);

    /**
     * 查询用户增长趋势
     * @param request 查询请求参数
     * @return 用户增长趋势数据
     */
    UserGrowthTrendRes getUserGrowthTrend(UserGrowthTrendReq request);

    /**
     * 查询任务增长趋势
     * @param request 查询请求参数
     * @return 任务增长趋势数据
     */
    TaskGrowthTrendRes getTaskGrowthTrend(TaskGrowthTrendReq request);

    /**
     * 查询资金增长趋势
     * @param request 查询请求参数
     * @return 资金增长趋势数据
     */
    FundGrowthTrendRes getFundGrowthTrend(FundGrowthTrendReq request);

    /**
     * 获取待办事项统计
     * @return 待办事项统计数据
     */
    TodoStatisticsRes getTodoStatistics();

    /**
     * 获取财务统计
     * @return 财务统计数据
     */
    FinanceStatisticsRes getFinanceStatistics();

    boolean saveBasic(DataReportStatisticsSaveReq record, ExtendData extendData);

    boolean updateBasic(DataReportStatisticsUpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);
}
