package com.letu.solutions.cms.controller.cms;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import com.letu.solutions.cms.service.cms.UserTransactionBillService;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.cms.UserTransactionBillListReq;
import com.letu.solutions.model.cms.response.cms.UserTransactionBillPageRes;

/**
 * 财务管理/用户账户流水对账
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "cms:user_transaction_bill", valueDesc = "用户:账户流水对账")
public class UserTransactionBillController{
    private final UserTransactionBillService basicService;
    /**
    * 账户流水对账 分页查询
    */
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/userTransactionBill/page")
    public R<Page<UserTransactionBillPageRes>> loadPage(UserTransactionBillListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * 账户流水对账 Excel导出
     */
    @Preauthorize(value = "list", valueDesc = "账户流水导出")
    @GetMapping("/userTransactionBill/exportExcel")
    public R<String> exportExcel(UserTransactionBillListReq request) {
        return R.success(basicService.exportExcel(request));
    }
}
