package com.letu.solutions.cms.controller.sys;

import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.cms.service.sys.SysRoleInterfaceRelService;
import com.letu.solutions.cms.service.sys.SysRoleMenuRelService;
import com.letu.solutions.cms.service.sys.SysRoleService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import com.letu.solutions.model.cms.request.sys.*;
import com.letu.solutions.model.cms.response.sys.SysRoleResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统/系统角色
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@Preauthorize(value = "sys:role", valueDesc = "系统:角色")
public class SysRoleController {
    private final SysRoleService basicService;
    private final SysRoleMenuRelService sysRoleMenuRelService;
    private final SysRoleInterfaceRelService sysRoleInterfaceRelService;

    /**
     * 系统角色 列表树查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/sysRole/list")
    public R<List<SysRoleResponse>> loadList(SysRoleQueryReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * 系统角色 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "详情查询")
    @GetMapping("/sysRole/selectById")
    public R<SysRoleResponse> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * 系统角色 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/sysRole/update")
    public R<Boolean> update(@Validated @RequestBody SysRoleUpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * 系统角色 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/sysRole/insert")
    public R<Boolean> insert(@Validated @RequestBody SysRoleSaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * 系统角色 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/sysRole/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }

    /**
     * 系统角色 角色已绑菜单查询
     *
     * @param roleId 角色id
     */
    @Preauthorize(value = "roleMenuList", valueDesc = "角色已绑菜单查询")
    @GetMapping("/sysRole/roleMenuList")
    public R<List<Long>> roleMenuList(@RequestParam("roleId") Long roleId) {
        return R.success(sysRoleMenuRelService.selectBasicList(roleId));
    }

    /**
     * 系统角色 角色已绑接口查询
     *
     * @param roleId 角色id
     */
    @Preauthorize(value = "selectRoleInterface", valueDesc = "角色已绑接口查询")
    @GetMapping("/sysRole/selectRoleInterface")
    public R<List<String>> selectRoleInterface(@RequestParam("roleId") Long roleId) {
        return R.success(sysRoleInterfaceRelService.selectRoleInterface(roleId));
    }

    /**
     * 系统角色 角色已绑菜单修改
     */
    @Preauthorize(value = "saveRoleMenu", valueDesc = "角色已绑菜单修改")
    @PostMapping("/sysRole/saveRoleMenu")
    public R<Boolean> saveRoleMenu(@Validated @RequestBody SysRoleMenuRelSaveReq request, @RequestHeader ExtendData extendData) {
        boolean res = sysRoleMenuRelService.saveRoleMenu(request, extendData);
        if(res){
            basicService.refreshToken(request.getRoleId());
        }
        return R.success(res);
    }

    /**
     * 系统角色 角色已绑接口修改
     */
    @Preauthorize(value = "saveRoleInterface", valueDesc = "角色已绑接口修改")
    @PostMapping("/sysRole/saveRoleInterface")
    public R<Boolean> saveRoleInterface(@Validated @RequestBody SysRoleInterfaceRelSaveReq request, @RequestHeader ExtendData extendData) {
        boolean res = sysRoleInterfaceRelService.saveRoleInterface(request, extendData);
        if(res){
            basicService.refreshToken(request.getRoleId());
        }
        return R.success(res);
    }
}
