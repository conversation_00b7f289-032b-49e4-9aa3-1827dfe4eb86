<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.UserAccountBalanceMapper">

    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.cms.UserAccountBalance" autoMapping="true">
            <id column="id" property="id"/>
            <id column="user_id" property="userId"/>
            <id column="available_amount" property="availableAmount"/>
            <id column="frozen_amount" property="frozenAmount"/>
            <id column="currency" property="currency"/>
            <id column="create_time" property="createTime"/>
            <id column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
                id,
                user_id,
                available_amount,
                frozen_amount,
                currency,
                create_time,
                update_time
    </sql>

    <select id="selectFrozenAmountByUserId" resultType="java.math.BigDecimal">
        SELECT IFNULL(frozen_amount,0) FROM user_account_balance WHERE user_id = #{userId}
    </select>

    <select id="sumAllFrozenAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(frozen_amount), 0)
        FROM user_account_balance
    </select>
</mapper>
