<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.PlatformTransactionBillMapper">

    <select id="sumProviderRewardAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM platform_transaction_bill
        WHERE account_type = 'provider'
          AND side = 'in'
          AND fund_type IN ('task_reward_to_account', 'task_reward_unfreeze_send')
    </select>

    <select id="sumClientUnfreezeRefundAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM platform_transaction_bill
        WHERE account_type = 'client'
          AND side = 'in'
          AND fund_type IN ('task_reward_unfreeze_refund', 'deposit_unfreeze_refund')
    </select>

    <!-- 分页查询平台流水（包含协议网络、平台钱包地址、操作人员信息） -->
    <select id="selectPageWithDetails" resultType="com.letu.solutions.model.cms.response.cms.PlatformTransactionBillPageRes">
        SELECT
            ptb.id,
            ptb.user_id as userId,
            u.nick_name as userName,
            ptb.account_type as accountType,
            ptb.fund_type as fundType,
            ptb.side,
            ptb.amount,
            ptb.fee,
            ptb.balance_before as balanceBefore,
            ptb.balance_after as balanceAfter,
            ptb.create_time as createdAt,
            CASE
                WHEN ptb.fund_type = 'deposit' THEN dr.network
                WHEN ptb.fund_type = 'withdraw' THEN wr.network
                ELSE NULL
            END as network,
            CASE
                WHEN ptb.fund_type = 'deposit' THEN dr.from_address
                WHEN ptb.fund_type = 'withdraw' THEN wr.to_address
                ELSE NULL
            END as platformWalletAddress,
            CASE
                WHEN ptb.fund_type = 'deposit' THEN dr.operator
                WHEN ptb.fund_type = 'withdraw' THEN wr.operator
                ELSE NULL
            END as operator
        FROM platform_transaction_bill ptb
        LEFT JOIN user u ON ptb.user_id = u.id
        LEFT JOIN deposit_record dr ON ptb.fund_type = 'deposit' AND ptb.fund_id = dr.id
        LEFT JOIN withdraw_record wr ON ptb.fund_type = 'withdraw' AND ptb.fund_id = wr.id
        WHERE 1=1
        <if test="request.id != null">
            AND ptb.id = #{request.id}
        </if>
        <if test="request.userId != null">
            AND ptb.user_id = #{request.userId}
        </if>
        <if test="request.userName != null and request.userName != ''">
            AND u.nick_name LIKE CONCAT('%', #{request.userName}, '%')
        </if>
        <if test="request.accountType != null">
            AND ptb.account_type = #{request.accountType}
        </if>
        <if test="request.fundType != null">
            AND ptb.fund_type = #{request.fundType}
        </if>
        <if test="request.network != null and request.network != ''">
            AND (
                (ptb.fund_type = 'deposit' AND dr.network = #{request.network})
                OR
                (ptb.fund_type = 'withdraw' AND wr.network = #{request.network})
            )
        </if>
        <if test="request.beginDate != null">
            AND ptb.day >= #{request.beginDate}
        </if>
        <if test="request.endDate != null">
            AND ptb.day &lt;= #{request.endDate}
        </if>
        ORDER BY ptb.day DESC
    </select>

</mapper>
