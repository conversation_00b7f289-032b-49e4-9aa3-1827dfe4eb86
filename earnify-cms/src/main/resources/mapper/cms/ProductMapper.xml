<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.ProductMapper">

    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.cms.Product" autoMapping="true">
            <id column="id" property="id"/>
            <id column="product_name" property="productName"/>
            <id column="logo" property="logo"/>
            <id column="party_user_id" property="partyUserId"/>
            <id column="product_type" property="productType"/>
            <id column="product_status" property="productStatus"/>
            <id column="create_time" property="createTime"/>
            <id column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
                id,
                product_id,
                product_name,
                logo,
                party_user_id,
                product_type,
                product_status,
                create_time,
                update_time
    </sql>

    <select id="selectProductPage" resultType="com.letu.solutions.model.cms.response.cms.ProductPageRes">
        SELECT
            p.id AS id,
            p.product_name AS productName,
            p.logo AS logo,
            p.host_url,
            u.nick_name AS partyUserNickname,
            u.id AS partyUserId,
            p.product_type AS productType,
            p.product_status AS productStatus,
            -- 任务发布次数：task表中productid等于当前产品id的记录数
            (SELECT COUNT(1) FROM task t1 WHERE t1.product_id = p.id) AS taskPublishCount,
            -- 任务发布个数：所有task表number字段累加
            (SELECT IFNULL(SUM(t2.number), 0) FROM task t2 WHERE t2.product_id = p.id) AS taskPublishNum,
            -- 任务领取个数：usertask表，统计每个产品下所有task的领取记录，
            (SELECT IFNULL(SUM(t3.task_receive_num), 0) FROM task t3 WHERE t3.product_id = p.id) AS taskReceiveNum,
            -- 任务完成个数：usertask表，userId+taskId为一组，只有该组所有步骤（sort）都为state=completed，才算完成1个任务
            (SELECT IFNULL(SUM(t3.task_finish_num), 0) FROM task t3 WHERE t3.product_id = p.id) AS taskFinishNum,
            -- 已经发送奖励金额：usertask表，state = 'completed' 时累加 price 字段
            (SELECT IFNULL(SUM(t4.task_finish_num * t4.price), 0) FROM task t4 WHERE t4.product_id = p.id) AS sentRewardAmount,
            -- 冻结中
            (
              -- 第一部分
              (SELECT IFNULL(SUM((t1.number - t1.task_finish_num) * t1.price), 0)
               FROM task t1
               WHERE t1.product_id = p.id
                 AND t1.state IN ('pendingOrders', 'inProgress')
              )
              +
              -- 第二部分
              (SELECT IFNULL(SUM(ut2.user_count * t2.price), 0)
               FROM task t2
               JOIN (
                   SELECT ut.task_id, COUNT(1) AS user_count
                   FROM user_task ut
                   WHERE ut.state IN ('incomplete', 'pendingApproval', 'toBeRevised', 'appealInProgress')
                   GROUP BY ut.task_id,ut.user_id
               ) ut2 ON ut2.task_id = t2.id
               WHERE t2.product_id = p.id
                 AND t2.state = 'revoked'
              )
            ) AS freezeAmount,
            p.create_time AS createTime
        FROM product p
        LEFT JOIN user u ON p.party_user_id = u.id
        LEFT JOIN task t on t.product_id = p.id
        <where>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.id != null">
                AND p.id = #{req.id}
            </if>
            <if test="req.partyUserNickname != null and req.partyUserNickname != ''">
                AND u.nickname LIKE CONCAT('%', #{req.partyUserNickname}, '%')
            </if>
            <if test="req.partyUserId != null">
                AND u.id = #{req.partyUserId}
            </if>
            <if test="req.productType != null">
                AND p.product_type = #{req.productType}
            </if>
            <if test="req.productStatus != null">
                AND p.product_status = #{req.productStatus}
            </if>
        </where>
        GROUP BY p.id
        ORDER BY p.id DESC
    </select>


    <select id="selectProductList" resultType="com.letu.solutions.cms.dto.ProductExcel">
        SELECT
        p.id AS id,
        p.product_name AS productName,
        p.logo AS logo,
        u.nick_name AS partyUserNickname,
        u.id AS partyUserId,
        p.product_type AS productType,
        p.product_status AS productStatus,
        -- 任务发布次数：task表中productid等于当前产品id的记录数
        (SELECT COUNT(1) FROM task t1 WHERE t1.product_id = p.id) AS taskPublishCount,
        -- 任务发布个数：所有task表number字段累加
        (SELECT IFNULL(SUM(t2.number), 0) FROM task t2 WHERE t2.product_id = p.id) AS taskPublishNum,
        -- 任务领取个数：usertask表，统计每个产品下所有task的领取记录，
        (SELECT IFNULL(SUM(t3.task_receive_num), 0) FROM task t3 WHERE t3.product_id = p.id) AS taskReceiveNum,
        -- 任务完成个数：usertask表，userId+taskId为一组，只有该组所有步骤（sort）都为state=completed，才算完成1个任务
        (SELECT IFNULL(SUM(t3.task_finish_num), 0) FROM task t3 WHERE t3.product_id = p.id) AS taskFinishNum,
        -- 已经发送奖励金额：usertask表，state = 'completed' 时累加 price 字段
        (SELECT IFNULL(SUM(t4.task_finish_num * t4.price), 0) FROM task t4 WHERE t4.product_id = p.id) AS sentRewardAmount,
        -- 冻结中
        (
        -- 第一部分
        (SELECT IFNULL(SUM((t1.number - t1.task_finish_num) * t1.price), 0)
        FROM task t1
        WHERE t1.product_id = p.id
        AND t1.state IN ('pendingOrders', 'inProgress')
        )
        +
        -- 第二部分
        (SELECT IFNULL(SUM(ut2.user_count * t2.price), 0)
        FROM task t2
        JOIN (
        SELECT ut.task_id, COUNT(1) AS user_count
        FROM user_task ut
        WHERE ut.state IN ('incomplete', 'pendingApproval', 'toBeRevised', 'appealInProgress')
        GROUP BY ut.task_id,ut.user_id
        ) ut2 ON ut2.task_id = t2.id
        WHERE t2.product_id = p.id
        AND t2.state = 'revoked'
        )
        ) AS freezeAmount,
        p.create_time AS createTime
        FROM product p
        LEFT JOIN user u ON p.party_user_id = u.id
        LEFT JOIN task t on t.product_id = p.id
        <where>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.id != null">
                AND p.id = #{req.id}
            </if>
            <if test="req.partyUserNickname != null and req.partyUserNickname != ''">
                AND u.nickname LIKE CONCAT('%', #{req.partyUserNickname}, '%')
            </if>
            <if test="req.partyUserId != null">
                AND u.id = #{req.partyUserId}
            </if>
            <if test="req.productType != null">
                AND p.product_type = #{req.productType}
            </if>
            <if test="req.productStatus != null">
                AND p.product_status = #{req.productStatus}
            </if>
        </where>
        GROUP BY p.id
        ORDER BY p.id DESC
    </select>
</mapper>
