<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.DataReportStatisticsMapper">

    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.cms.DataReportStatistics" autoMapping="true">
            <id column="id" property="id"/>
            <id column="stat_date" property="statDate"/>
            <id column="new_user_count_a" property="newUserCountA"/>
            <id column="new_user_count_b" property="newUserCountB"/>
            <id column="new_user_count" property="newUserCount"/>
            <id column="new_product_count" property="newProductCount"/>
            <id column="new_task_count" property="newTaskCount"/>
            <id column="task_claim_count" property="taskClaimCount"/>
            <id column="task_complete_count" property="taskCompleteCount"/>
            <id column="points_recharge_success" property="pointsRechargeSuccess"/>
            <id column="points_withdraw_success" property="pointsWithdrawSuccess"/>
            <id column="points_frozen" property="pointsFrozen"/>
            <id column="points_granted" property="pointsGranted"/>
            <id column="points_returned" property="pointsReturned"/>
            <id column="create_time" property="createTime"/>
            <id column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
                id,
                stat_date,
                new_user_count_a,
                new_user_count_b,
                new_user_count,
                new_product_count,
                new_task_count,
                task_claim_count,
                task_complete_count,
                points_recharge_success,
                points_withdraw_success,
                points_frozen,
                points_granted,
                points_returned,
                create_time,
                update_time
    </sql>
</mapper>
