<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.AppealRecordMapper">

    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.cms.AppealRecord" autoMapping="true">
            <id column="id" property="id"/>
            <id column="task_id" property="taskId"/>
            <id column="party_a_user_id" property="partyAUserId"/>
            <id column="party_a_username" property="partyAUsername"/>
            <id column="party_b_user_id" property="partyBUserId"/>
            <id column="party_b_username" property="partyBUsername"/>
            <id column="product_id" property="productId"/>
            <id column="product_name" property="productName"/>
            <id column="task_attribute" property="taskAttribute"/>
            <id column="task_type" property="taskType"/>
            <id column="task_deadline" property="taskDeadline"/>
            <id column="appeal_status" property="appealStatus"/>
            <id column="task_points" property="taskPoints"/>
            <id column="granted_points" property="grantedPoints"/>
            <id column="review_remark" property="reviewRemark"/>
            <id column="evidence_images" property="evidenceImages"/>
            <id column="create_time" property="createTime"/>
            <id column="update_time" property="updateTime"/>
            <id column="end_time" property="endTime"/>
    </resultMap>

    <sql id="Base_Column_List">
                id,
                task_id,
                party_a_user_id,
                party_a_username,
                party_b_user_id,
                party_b_username,
                product_id,
                product_name,
                task_attribute,
                task_type,
                task_deadline,
                appeal_status,
                task_points,
                granted_points,
                review_remark,
                evidence_images,
                create_time,
                update_time,
                end_time
    </sql>

    <select id="selectBasicPage" resultType="com.letu.solutions.model.cms.response.cms.AppealRecordPageRes">
        SELECT
            ar.*,
            t.name AS task_name,
            t.id AS task_id,
            t.attribute AS task_attribute,
            t.task_type,
            t.time,
            t.price,
            p.product_name,
            p.id AS product_id
        FROM appeal_record ar
        LEFT JOIN task t ON ar.task_id = t.id
        LEFT JOIN product p ON ar.product_id = p.id
        <where>
            <if test="req.taskName != null and req.taskName != ''">
                AND t.name LIKE CONCAT('%', #{req.taskName}, '%')
            </if>
            <if test="req.taskId != null">
                AND t.id = #{req.taskId}
            </if>
            <if test="req.partyAUsername != null and req.partyAUsername != ''">
                AND ar.party_a_username LIKE CONCAT('%', #{req.partyAUsername}, '%')
            </if>
            <if test="req.partyAUserId != null">
                AND ar.party_a_user_id = #{req.partyAUserId}
            </if>
            <if test="req.partyBUsername != null and req.partyBUsername != ''">
                AND ar.party_b_username LIKE CONCAT('%', #{req.partyBUsername}, '%')
            </if>
            <if test="req.partyBUserId != null">
                AND ar.party_b_user_id = #{req.partyBUserId}
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.productId != null">
                AND p.id = #{req.productId}
            </if>
            <if test="req.staTime != null and req.staTime !=''">
                AND ar.create_time >= #{req.staTime}
            </if>
            <if test="req.finishTime != null and req.finishTime !=''">
                AND ar.create_time &lt;= #{req.finishTime}
            </if>
        </where>
        order by ar.id desc
    </select>

    <select id="selectBasicList" resultType="com.letu.solutions.cms.dto.AppealRecordExcel">
        SELECT
        ar.*,
        t.name AS task_name,
        t.id AS task_id,
        t.attribute AS task_attribute,
        t.task_type,
        t.time,
        t.price,
        p.product_name,
        p.id AS product_id
        FROM appeal_record ar
        LEFT JOIN task t ON ar.task_id = t.id
        LEFT JOIN product p ON ar.product_id = p.id
        <where>
            <if test="req.taskName != null and req.taskName != ''">
                AND t.name LIKE CONCAT('%', #{req.taskName}, '%')
            </if>
            <if test="req.taskId != null">
                AND t.id = #{req.taskId}
            </if>
            <if test="req.partyAUsername != null and req.partyAUsername != ''">
                AND ar.party_a_username LIKE CONCAT('%', #{req.partyAUsername}, '%')
            </if>
            <if test="req.partyAUserId != null">
                AND ar.party_a_user_id = #{req.partyAUserId}
            </if>
            <if test="req.partyBUsername != null and req.partyBUsername != ''">
                AND ar.party_b_username LIKE CONCAT('%', #{req.partyBUsername}, '%')
            </if>
            <if test="req.partyBUserId != null">
                AND ar.party_b_user_id = #{req.partyBUserId}
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.productId != null">
                AND p.id = #{req.productId}
            </if>
        </where>
        order by ar.id desc
    </select>
</mapper>
