<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.TaskMapper">


    <resultMap id="TaskPageResMap" type="com.letu.solutions.model.cms.response.cms.TaskPageRes">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="attribute" column="attribute"/>
        <result property="taskType" column="task_type"/>
        <result property="weightSorting" column="weight_sorting"/>
        <result property="time" column="time"/>
        <result property="state" column="state"/>
        <result property="bond" column="bond"/>
        <result property="price" column="price"/>
        <result property="taskReceiveNum" column="task_receive_num"/>
        <result property="taskFinishNum" column="task_finish_num"/>
        <result property="number" column="number"/>
        <result property="createTime" column="create_time"/>
        <result property="grantedAmount" column="granted_amount"/>
        <result property="frozenAmount" column="frozenAmount"/>
    </resultMap>
    <resultMap id="TaskExcel" type="com.letu.solutions.cms.dto.TaskExcel">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="attribute" column="attribute"/>
        <result property="taskType" column="task_type"/>
        <result property="weightSorting" column="weight_sorting"/>
        <result property="time" column="time"/>
        <result property="state" column="state"/>
        <result property="bond" column="bond"/>
        <result property="price" column="price"/>
        <result property="taskReceiveNum" column="task_receive_num"/>
        <result property="taskFinishNum" column="task_finish_num"/>
        <result property="number" column="number"/>
        <result property="createTime" column="create_time"/>
        <result property="grantedAmount" column="granted_amount"/>
        <result property="frozenAmount" column="frozenAmount"/>
    </resultMap>
    <sql id="Base_Column_List">
                id,
                attribute,
                name,
                user_id,
                product_id,
                weight_sorting,
                task_type,
                number,
                price,
                time,
                state,
                step_number,
                url,
                create_time,
                update_time
    </sql>

    <select id="selectTaskPage" resultMap="TaskPageResMap">
        SELECT
            t.id,
            t.name,
            t.user_id,
            u.nick_name,
            t.product_id,
            p.product_name,
            t.attribute,
            t.task_type,
            t.weight_sorting,
            t.time,
            t.state,
            t.bond,
            t.price,
            t.task_finish_num,
            t.task_receive_num,
            t.create_time,
            t.number,
            (t.task_finish_num * t.price) AS granted_amount,
            ((t.number- t.task_finish_num) * t.price) AS frozenAmount
        FROM task t
        LEFT JOIN `user` u ON t.user_id = u.id
        LEFT JOIN product p ON t.product_id = p.id
        <where>
            <if test="req.name != null and req.name != ''">
                AND t.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.id != null">
                AND t.id = #{req.id}
            </if>
            <if test="req.nickName != null and req.nickName != ''">
                AND u.nick_name LIKE CONCAT('%', #{req.nickName}, '%')
            </if>
            <if test="req.userId != null">
                AND t.user_id = #{req.userId}
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.productId != null">
                AND t.product_id = #{req.productId}
            </if>
            <if test="req.staTime != null and req.staTime !=''">
                AND t.create_time >= #{req.staTime}
            </if>
            <if test="req.finishTime != null and req.finishTime !=''">
                AND t.create_time &lt;= #{req.finishTime}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="selectTaskList" resultMap="TaskExcel">
        SELECT
        t.id,
        t.name,
        t.user_id,
        u.nick_name,
        t.product_id,
        p.product_name,
        t.attribute,
        t.task_type,
        t.weight_sorting,
        t.time,
        t.state,
        t.bond,
        t.price,
        t.task_finish_num,
        t.task_receive_num,
        t.create_time,
        t.number,
        (t.task_finish_num * t.price) AS granted_amount,
        ((t.number- t.task_finish_num) * t.price) AS frozenAmount
        FROM task t
        LEFT JOIN `user` u ON t.user_id = u.id
        LEFT JOIN product p ON t.product_id = p.id
        <where>
            <if test="req.name != null and req.name != ''">
                AND t.name LIKE CONCAT('%', #{req.name}, '%')
            </if>
            <if test="req.id != null">
                AND t.id = #{req.id}
            </if>
            <if test="req.nickName != null and req.nickName != ''">
                AND u.nick_name LIKE CONCAT('%', #{req.nickName}, '%')
            </if>
            <if test="req.userId != null">
                AND t.user_id = #{req.userId}
            </if>
            <if test="req.productName != null and req.productName != ''">
                AND p.product_name LIKE CONCAT('%', #{req.productName}, '%')
            </if>
            <if test="req.productId != null">
                AND t.product_id = #{req.productId}
            </if>
            <if test="req.staTime != null and req.staTime !=''">
                AND t.create_time >= #{req.staTime}
            </if>
            <if test="req.finishTime != null and req.finishTime !=''">
                AND t.create_time &lt;= #{req.finishTime}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
</mapper>
