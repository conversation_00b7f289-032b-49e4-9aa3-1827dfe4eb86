<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.cms.UserSocialMapper">

    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.cms.UserSocial" autoMapping="true">
            <id column="id" property="id"/>
            <id column="user_id" property="userId"/>
            <id column="twitter" property="twitter"/>
            <id column="telegram" property="telegram"/>
            <id column="discord" property="discord"/>
            <id column="reddit" property="reddit"/>
            <id column="tiktok" property="tiktok"/>
            <id column="medium" property="medium"/>
            <id column="create_time" property="createTime"/>
            <id column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
                id,
                user_id,
                twitter,
                telegram,
                discord,
                reddit,
                tiktok,
                medium,
                create_time,
                update_time
    </sql>
</mapper>
