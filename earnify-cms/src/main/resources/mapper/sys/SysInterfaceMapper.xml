<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.sys.SysInterfaceMapper">
    <select id="allInterface" resultType="java.lang.String">
        select name_all
        from sys_interface
        order by name_all
    </select>
    <select id="userVoList" resultType="java.lang.String">
        select
            distinct name_all
        from sys_interface si
        where type = 'method' and si.name_all in (
            select srmr.name_all
            from
                sys_user_role_rel surr
                    left join
                sys_role so
                on surr.role_id = so.id
                    left join
                sys_role_interface_rel srmr
                on surr.role_id = srmr.role_id
            where surr.user_id = #{userId} and so.enable = 1 and so.del = 0 and srmr.name_all is not null
            )
        order by si.name_all
    </select>
</mapper>
