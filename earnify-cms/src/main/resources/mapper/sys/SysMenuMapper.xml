<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.sys.SysMenuMapper">
    <select id="detailList" resultType="com.letu.solutions.model.cms.response.sys.SysMenuDetail">
        select
            *
        from
            sys_menu
        <where>
            <if test="parentId != null">
                and parentId = #{parentId}
            </if>
        </where>
        order by parent_id, sort, id
    </select>

    <select id="allVoList" resultType="com.letu.solutions.model.cms.response.sys.SysMenuVo">
        select
        *
        from
        sys_menu
        where enable = 1
        order by parent_id, sort, id
    </select>

    <select id="userVoList" resultType="com.letu.solutions.model.cms.response.sys.SysMenuVo">
        select  distinct *
        from sys_menu sm
        where sm.enable = 1 and sm.id in (
            select
                srmr.menu_id
            from
                sys_user_role_rel surr
                    left join
                sys_role so
                on surr.role_id = so.id
                    left join
                sys_role_menu_rel srmr
                on surr.role_id = srmr.role_id
            where surr.user_id = #{userId} and so.enable = 1 and so.del = 0 and srmr.menu_id is not null
        )
        order by sm.parent_id, sm.sort, sm.id
    </select>
</mapper>
