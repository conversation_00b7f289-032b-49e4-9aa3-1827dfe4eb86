<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.sys.SysParamMapper">

    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.sys.SysParam" autoMapping="true">
            <id column="id" property="id"/>
            <id column="name" property="name"/>
            <id column="key" property="key"/>
            <id column="value" property="value"/>
            <id column="describe" property="describe"/>
            <id column="create_time" property="createTime"/>
            <id column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
                id,
                name,
                key,
                value,
                describe,
                create_time,
                update_time
    </sql>
</mapper>
