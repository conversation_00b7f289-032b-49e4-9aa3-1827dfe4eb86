<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.cms.mapper.user.UserMapper">

    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.user.User" autoMapping="true">
            <id column="id" property="id"/>
            <id column="user_phone" property="userPhone"/>
            <id column="user_image" property="userImage"/>
            <id column="nick_name" property="nickName"/>
            <id column="user_type" property="userType"/>
            <id column="creator" property="creator"/>
            <id column="pwd" property="pwd"/>
            <id column="salt" property="salt"/>
            <id column="status" property="status"/>
            <id column="day" property="day"/>
            <id column="last_login_time" property="lastLoginTime"/>
            <id column="enable" property="enable"/>
            <id column="create_time" property="createTime"/>
            <id column="update_time" property="updateTime"/>
            <id column="device_id" property="deviceId"/>
    </resultMap>


    <sql id="Base_Column_List">
                id,
                user_phone,
                user_image,
                user_type,
                creator,
                pwd,
                salt,
                status,
                day,
                last_login_time,
                enable,]
                create_time,
                update_time,
                device_id
    </sql>
</mapper>
