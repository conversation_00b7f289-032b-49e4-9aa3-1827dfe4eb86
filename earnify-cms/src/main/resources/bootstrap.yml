server:
  port: 11801
  max-http-request-header-size: 4194304
  servlet:
    context-path: /earnify-cms
dubbo:
  protocol:
    port: 21801
  cloud:
    subscribed-services: 'middle-customer,middle-cms'
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: earnify-cms
    type: cms
  cloud:
    nacos:
      server-addr: http://192.168.77.101:28848
      discovery:
        namespace: ${discoverNameSpace:server}
        username: earnify
        password: 6sqGL8nj4oIt5P8n
      config:
        file-extension: yml
        namespace: earnify
        username: earnify
        password: 6sqGL8nj4oIt5P8n
  config:
    import:
      - nacos:redis.yaml
      - nacos:mysql.yaml
      - nacos:mybatis.yaml
      - nacos:base.yaml
      - nacos:nos.yaml
      - nacos:actuator.yaml
      - nacos:dubbo${discoverDubboEnv:}.yaml
      - nacos:account.yaml
      - nacos:oss.yaml?refresh=true
      - nacos:refresh.yaml?refresh=true
