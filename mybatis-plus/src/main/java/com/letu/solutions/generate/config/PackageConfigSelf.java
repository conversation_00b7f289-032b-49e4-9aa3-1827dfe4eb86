package com.letu.solutions.generate.config;

import com.baomidou.mybatisplus.generator.config.OutputFile;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class PackageConfigSelf {

    /**
     * 是否为案例，如果是案例，会在d盘根目录生成
     */
    private boolean demo = false;

    /**
     * 服务名称
     */
    private String modelName = "";

    /**
     * 是否生成controller接口
     */
    private boolean mkControllerMethod = true;

    /**
     * 服务缩写（modelName + modelNamePath计算获得）
     */
    private String modelAbb = "";

    /**
     * 实体模块目录
     */
    private String entityModelPath = "";

    /**
     * 生成目标服务包名最后一级公共删除(全删除为all)
     * 如 parent:com.letu.solutions,serviceName:middle-cms,serverNamePath:middle
     * 则删除后为 com.letu.solutions.cms
     */
    private String modelNamePath = "";

    /**
     * 服务前缀（默认使用表名的第一段，如果定义了，会强制使用此前缀）
     */
    private String serverPrefix;

    /**
     * 服务前缀（默认使用表名的第一段，如果定义了，会强制使用此前缀） (接口文档与注释文档分类使用)
     */
    private String serverPrefixChina;

    /**
     * 生成业务分类文件夹的服务名称列表 如： controller/sys/SysUserController.java
     */
    private List<String> mkdirPrefixServer = new ArrayList<>();

    /**
     * 父包名。如果为空，将下面子包名必须写全部， 否则就只需写子包名
     */
    private String parent = "com.baomidou";

    /**
     * Entity包名
     */
    private String entity = "entity";

    /**
     * Service包名
     */
    private String service = "service";

    /**
     * Service Impl包名
     */
    private String serviceImpl = "service.impl";

    /**
     * Mapper包名
     */
    private String mapper = "mapper";

    /**
     * Mapper XML包名
     */
    private String xml = "mapper.xml";

    /**
     * Controller包名
     */
    private String controller = "controller";

    /**
     * 路径配置信息
     */
    private Map<OutputFile, String> pathInfo;

    /**
     * 包配置信息
     *
     * @since 3.5.0
     */
    private final Map<String, String> packageInfo = new HashMap<>();
}