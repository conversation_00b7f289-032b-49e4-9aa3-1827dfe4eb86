//package com.letu.solutions.generate.controller;
//
///**
// * 测试
// *
// * <AUTHOR>
// * @since 2025-01-11
// */
//
//import com.baomidou.mybatisplus.generator.AutoGenerator;
//import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//@RestController
//@RequiredArgsConstructor
//@Slf4j
//public class TestController {
//    private final DataSourceConfig dataSourceConfig;
//
//    /**
//     * 测试
//     *
//     * @return
//     */
//    @GetMapping("/test.e")
//    public void test() {
//        System.out.println(dataSourceConfig.getPassword());
//        System.out.println(1);
//        AutoGenerator generator = new AutoGenerator(dataSourceConfig);
//        generator.execute();
//    }
//}
