package com.letu.solutions.generate.config;

import com.baomidou.mybatisplus.generator.config.rules.DateType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class GlobalConfigSelf {
    /**
     * 生成文件的输出目录【 windows:D://  linux or mac:/tmp 】
     */
    private String outputDir = System.getProperty("os.name").toLowerCase().contains("windows") ? "D://" : "/tmp";

    /**
     * 是否打开输出目录
     */
    private boolean open = true;

    /**
     * 作者
     */
    private String author = System.getenv("COMPUTERNAME");

    /**
     * 开启 Kotlin 模式（默认 false）
     */
    private boolean kotlin = false;

    /**
     * 开启 swagger 模式（默认 false 与 springdoc 不可同时使用）
     */
    private boolean swagger = false;
    /**
     * 开启 springdoc 模式（默认 false 与 swagger 不可同时使用）
     */
    private boolean springdoc;

    /**
     * 时间类型对应策略
     */
    private DateType dateType = DateType.ONLY_DATE;
    /**
     * 是否生成service 接口（默认 true）
     * 增加此开关的原因：在某些项目实践中，只需要生成service实现类，不需要抽象sevice接口
     * 针对某些项目，生成service接口，开发时反而麻烦，这种情况，可以将该属性设置为false
     */
    private boolean serviceInterface = true;
}