package com.letu.solutions.generate.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
public class StrategyConfigSelf {

    /**
     * 是否大写命名（默认 false）
     */
    private boolean isCapitalMode = false;

    /**
     * 是否跳过视图（默认 false）
     */
    private boolean skipView = false;

    /**
     * 过滤表前缀
     * example: addTablePrefix("t_")
     * result: t_simple -> Simple
     */
    private final List<String> tablePrefix = new ArrayList<>();

    /**
     * 需要包含的表名，允许正则表达式（与exclude二选一配置）<br/>
     */
    private final List<String> include = new ArrayList<>();
}