package com.letu.solutions.generate.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.letu.solutions.generate.config.GlobalConfigSelf;
import com.letu.solutions.generate.config.PackageConfigSelf;
import com.letu.solutions.generate.config.StrategyConfigSelf;
import com.letu.solutions.generate.config.TemplateConfigSelf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础测试类
 *
 * <AUTHOR>
 * @since 3.5.3
 */
@Slf4j
public class GeneratorUtil {
    private final static String javaPath = "src/main/java";
    private final static String resourcesPath = "src/main/resources";

    /**
     * 策略配置
     */
    public static StrategyConfig strategyConfig(StrategyConfigSelf config, TemplateConfigSelf templateConfig) {
        StrategyConfig.Builder builder = new StrategyConfig.Builder();
        if (config.isCapitalMode()) {
            builder.enableCapitalMode();
        }
        if (config.isSkipView()) {
            builder.enableSkipView();
        }
        if (CollectionUtil.isNotEmpty(config.getTablePrefix())) {
            builder.addTablePrefix(config.getTablePrefix());
        }
        if (CollectionUtil.isNotEmpty(config.getInclude())) {
            builder.addInclude(config.getInclude());
        }
        builder.controllerBuilder()
                .enableFileOverride()
                .template(templateConfig.getController());
        if (StrUtil.isEmpty(templateConfig.getController())) {
            builder.controllerBuilder().disable();
        }
        builder.serviceBuilder()
                .enableFileOverride()
                .serviceTemplate(templateConfig.getService())
                .serviceImplTemplate(templateConfig.getServiceImpl())
                .convertServiceFileName((e) -> e + ConstVal.SERVICE);
        if (StrUtil.isEmpty(templateConfig.getService())) {
            builder.serviceBuilder().disableService();
        }
        if (StrUtil.isEmpty(templateConfig.getServiceImpl())) {
            builder.serviceBuilder().disableServiceImpl();
        }
        builder.mapperBuilder()
                .enableFileOverride()
                .mapperTemplate(templateConfig.getMapper())
                .mapperXmlTemplate(templateConfig.getXml());
        if (StrUtil.isEmpty(templateConfig.getMapper())) {
            builder.mapperBuilder().disableMapper();
        }
        if (StrUtil.isEmpty(templateConfig.getXml())) {
            builder.mapperBuilder().disableMapperXml();
        }
        builder.entityBuilder()
                .enableFileOverride()
                .javaTemplate(templateConfig.getEntity());
        if (StrUtil.isEmpty(templateConfig.getEntity())) {
            builder.entityBuilder().disable();
        }
        return builder.build();
    }

    /**
     * 全局配置
     */
    public static GlobalConfig globalConfig(GlobalConfigSelf config) {
        GlobalConfig.Builder builder = new GlobalConfig.Builder();
        builder.outputDir(FileUtil.file(System.getProperty("user.dir")).getParent());
        if (!config.isOpen()) {
            builder.disableOpenDir();
        }
        if (!config.isServiceInterface()) {
            builder.disableServiceInterface();
        }
        builder.dateType(config.getDateType());
        builder.author(StrUtil.isEmpty(config.getAuthor()) ? System.getenv("COMPUTERNAME") : config.getAuthor());
        return builder.build();
    }

    /**
     * 包配置
     */
    public static PackageConfig packageConfig(PackageConfigSelf config) {
        if (config.isDemo()) {
            return new PackageConfig.Builder().parent(config.getParent()).build();
        }
        config.setModelAbb(config.getModelName().replaceAll(config.getModelNamePath(), ""));
        boolean needModelPre = config.getMkdirPrefixServer().contains(config.getModelName());
        String prePackage = needModelPre ? config.getServerPrefix() : "";
        Map<String, Object> configMap = JSONUtil.parseObj(config);
        PackageConfig.Builder builder = new PackageConfig.Builder()
                .parent(replaceParam(config.getParent(), configMap))
                .entity(replaceParam(config.getEntity(), configMap))
                .service(joinPackage(replaceParam(config.getService(), configMap), prePackage))
                .serviceImpl(joinPackage(replaceParam(config.getService(), configMap), prePackage, "impl"))
                .mapper(joinPackage(replaceParam(config.getMapper(), configMap), prePackage))
                .controller(joinPackage(replaceParam(config.getController(), configMap), prePackage));
        PackageConfig packageConfig = builder.build();
        String projectBasePath = FileUtil.file(System.getProperty("user.dir")).getParent();
        Map<String, String> packageInfo = packageConfig.getPackageInfo();
        Map<OutputFile, String> pathInfo = packageConfig.getPathInfo();
        if (null == pathInfo) {
            pathInfo = new HashMap<>();
        }
        pathInfo.put(OutputFile.entity, joinPath(projectBasePath, config.getEntityModelPath(), javaPath, packageInfo.get(ConstVal.ENTITY)));
        pathInfo.put(OutputFile.service, joinPath(projectBasePath, config.getModelName(), javaPath, packageInfo.get(ConstVal.SERVICE)));
        pathInfo.put(OutputFile.controller, joinPath(projectBasePath, config.getModelName(), javaPath, packageInfo.get(ConstVal.CONTROLLER)));
        pathInfo.put(OutputFile.serviceImpl, joinPath(projectBasePath, config.getModelName(), javaPath, packageInfo.get(ConstVal.SERVICE_IMPL)));
        pathInfo.put(OutputFile.mapper, joinPath(projectBasePath, config.getModelName(), javaPath, packageInfo.get(ConstVal.MAPPER)));
        pathInfo.put(OutputFile.xml, joinPath(projectBasePath, config.getModelName(), resourcesPath, "mapper", prePackage));
        ReflectUtil.setFieldValue(packageConfig, "pathInfo", pathInfo);
        return packageConfig;
    }

    private final static String[] listReq = {"create_time", "update_time", "del"};
    private final static String[] saveReq = {"id", "create_time", "update_time", "del"};
    private final static String[] updateReq = {"create_time", "update_time", "del"};
    private final static String[] pageRes = {"del"};
    private final static String[] detailRes = {"create_time", "update_time", "del"};
    private final static String delKey = "del";

    /**
     * 注入配置
     */
    public static InjectionConfig injectionConfig(PackageConfigSelf config, PackageConfig packageConfig) {
        // 测试自定义输出文件之前注入操作，该操作再执行生成代码前 debug 查看
        return new InjectionConfig.Builder().beforeOutputFile((tableInfo, objectMap) -> {
            String comment = tableInfo.getComment();
            boolean isDel = false;
            if (null != comment) {
                if (!comment.contains("-")) {
                    log.error("表注释必须使用【{menu}-{business}】结构");
                }
                if (comment.contains("-")) {
                    String[] split = comment.split("-");
                    String serverPrefixChina = split[0];
                    String tableComment = split[1];
                    tableInfo.setComment(tableComment);
                    objectMap.put("serverPrefixChina", serverPrefixChina);
                    objectMap.put("serverPrefix", config.getServerPrefix());
                    objectMap.put("mkControllerMethod", config.isMkControllerMethod());
                    String serverInterface = tableInfo.getName().replaceAll(String.format("%s_", config.getServerPrefix()), "");
                    objectMap.put("serverInterface", serverInterface);
                }
            }
            if (StrUtil.isNotEmpty(config.getServerPrefixChina())) {
                objectMap.put("serverPrefixChina", config.getServerPrefixChina());
            }
            for (TableField field : tableInfo.getFields()) {
                Map<String, Object> map = new HashMap<>();
                map.put("listReq", !ArrayUtil.contains(listReq, field.getName()));
                map.put("saveReq", !ArrayUtil.contains(saveReq, field.getName()));
                map.put("updateReq", !ArrayUtil.contains(updateReq, field.getName()));
                map.put("pageRes", !ArrayUtil.contains(pageRes, field.getName()));
                map.put("detailRes", !ArrayUtil.contains(detailRes, field.getName()));
                field.setCustomMap(map);
                isDel = isDel || field.getName().equals(delKey);
            }
            if (isDel) {
                objectMap.put("logicDel", isDel);
            }
        }).customFile(customFileList(config, packageConfig)).build();
    }

    public static List<CustomFile> customFileList(PackageConfigSelf config, PackageConfig packageConfig) {
        if (!config.isMkControllerMethod()) {
            return ListUtil.empty();
        }
        List<CustomFile> customFiles = new ArrayList<>();
        String projectBasePath = FileUtil.file(System.getProperty("user.dir")).getParent();
        Map<String, String> packageInfo = new HashMap<>();
        packageInfo.putAll(packageConfig.getPackageInfo());
        Map<String, String> customPath = new HashMap<>();
        boolean needModelPre = config.getMkdirPrefixServer().contains(config.getModelName());
        String prePackage = needModelPre ? config.getServerPrefix() : "";
        String parent = FileUtil.getParent(joinPath(projectBasePath, config.getEntityModelPath(), javaPath, packageInfo.get(ConstVal.ENTITY)), 2);
        customPath.put("ListReq", joinPath(parent, config.getModelAbb(), "request", prePackage));
        customPath.put("SaveReq", joinPath(parent, config.getModelAbb(), "request", prePackage));
        customPath.put("UpdateReq", joinPath(parent, config.getModelAbb(), "request", prePackage));
        customPath.put("PageRes", joinPath(parent, config.getModelAbb(), "response", prePackage));
        customPath.put("DetailRes", joinPath(parent, config.getModelAbb(), "response", prePackage));
        String[] basePackageStrs = packageInfo.get("Entity").split("\\.");
        String parentPackage = ArrayUtil.join(ArrayUtil.sub(basePackageStrs, 0, basePackageStrs.length - 2), ".");
        packageInfo.put("reqPkg", joinPackage(parentPackage, config.getModelAbb(), "request", prePackage));
        packageInfo.put("resPkg", joinPackage(parentPackage, config.getModelAbb(), "response", prePackage));
        ReflectUtil.setFieldValue(packageConfig, "packageInfo", packageInfo);
        for (String s : customPath.keySet()) {
            customFiles.add(new CustomFile.Builder()
                    .fileName(String.format("%s.java", s))
                    .enableFileOverride()
                    .filePath(customPath.get(s))
                    .templatePath(String.format("templates/%s.java.vm", s))
                    .build());
        }
        return customFiles;
    }

    /**
     * 连接路径字符串
     *
     * @param parentDir   路径常量字符串
     * @param packageName 包名
     * @return 连接后的路径
     */
    private static String joinPath(String parentDir, String... packageName) {
        packageName = trim(packageName);
        String path = StrUtil.join(File.separator, parentDir, packageName);
        path = path.replaceAll("\\.", StringPool.BACK_SLASH + File.separator);
        return path;
    }

    /**
     * 连接路径字符串
     */
    private static String joinPackage(String parentPackage, String... packageName) {
        packageName = trim(packageName);
        return StrUtil.join(StringPool.DOT, parentPackage, packageName);
    }

    public static String[] trim(String[] strings) {
        List<String> resultList = new ArrayList<>();
        for (String str : strings) {
            if (StrUtil.isNotEmpty(str)) {
                resultList.add(str);
            }
        }
        return resultList.toArray(new String[0]);
    }


    /**
     * 替换参数
     *
     * @param teContent
     * @param params
     * @return
     */
    public static String replaceParam(String teContent, Map<String, Object> params) {
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        //把方法参数放入SPEL上下文中
        if (CollectionUtil.isNotEmpty(params)) {
            for (String key : params.keySet()) {
                context.setVariable(key, params.get(key));
            }
        } else {
            return teContent;
        }
        ParserContext parserContext = new TemplateParserContext();
        return parser.parseExpression(teContent, parserContext).getValue(context, String.class);
    }
}