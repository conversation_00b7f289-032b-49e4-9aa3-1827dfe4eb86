
package com.letu.solutions.generate.config;

import com.baomidou.mybatisplus.generator.config.InjectionConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 配置读取
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@ConfigurationProperties(prefix = "generator")
@Configuration
public class GeneratorConfiguration {

    /**
     * 策略配置信息
     */
    private StrategyConfigSelf strategyConfig;

    /**
     * 全局配置信息
     */
    private GlobalConfigSelf globalConfig;

    /**
     * 注入配置信息
     */
    private InjectionConfig injectionConfig;

    /**
     * 包 相关配置
     */
    private PackageConfigSelf packageInfo;

    /**
     * 模板 相关配置
     */
    private TemplateConfigSelf template;
}
