package com.letu.solutions.generate.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class TemplateConfigSelf {

    /**
     * 设置实体模板路径
     */
    private String entity;

    /**
     * 设置控制器模板路径
     */
    private String controller;

    /**
     * 设置Mapper模板路径
     */
    private String mapper;

    /**
     * 设置MapperXml模板路径
     */
    private String xml;

    /**
     * 设置Service模板路径
     */
    private String service;

    /**
     * 设置ServiceImpl模板路径
     */
    private String serviceImpl;
}