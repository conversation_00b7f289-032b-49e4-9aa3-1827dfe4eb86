package com.letu.solutions.generate.config;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.engine.AbstractTemplateEngine;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 生成文件
 *
 * <AUTHOR> tangguo, hubin
 * @since 2016-08-30
 */
@Slf4j
public class AutoGeneratorSelf extends AutoGenerator {

    /**
     * 构造方法
     *
     * @param dataSourceConfig 数据库配置
     * @since 3.5.0
     */
    public AutoGeneratorSelf(DataSourceConfig dataSourceConfig) {
        super(dataSourceConfig);
    }

    /**
     * 生成代码
     *
     * @param templateEngine 模板引擎
     */
    @Override
    public void execute(AbstractTemplateEngine templateEngine) {
        log.debug("==========================准备生成文件...==========================");
        // 初始化配置
        if (null == config) {
            config = new ConfigBuilder(getPackageInfo(), getDataSource(), getStrategy(), getTemplate(), getGlobalConfig(), getInjectionConfig());
        }
        if (null == templateEngine) {
            // 为了兼容之前逻辑，采用 Velocity 引擎 【 默认 】
            templateEngine = new VelocityTemplateEngine();
        }
        templateEngine.setConfigBuilder(config);
        // 模板引擎初始化执行文件输出
        AbstractTemplateEngine init = templateEngine.init(config);
        getAllTableInfoList(config);
        init.batchOutput().open();
        log.debug("==========================文件生成完成！！！==========================");
    }

    /**
     * 开放表信息、预留子类重写
     *
     * @param config 配置信息
     * @return ignore
     */
    @Override
    protected List<TableInfo> getAllTableInfoList(ConfigBuilder config) {
        List<TableInfo> tableInfoList = config.getTableInfoList();
//      二次修改入口
        for (TableInfo tableInfo : tableInfoList) {
            log.info("无二次修改项");
        }
        return tableInfoList;
    }

}
