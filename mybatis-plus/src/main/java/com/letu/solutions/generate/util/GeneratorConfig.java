package com.letu.solutions.generate.util;


import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;


@Component
@Slf4j
public class GeneratorConfig {
    @Bean
    public DataSourceConfig dataSourceConfig(DataSource dataSource) {
        DataSourceConfig.Builder builder = new DataSourceConfig.Builder(dataSource);
        builder.typeConvertHandler(new MySqlTypeConvertCustom());
        return builder.build();
    }
}
