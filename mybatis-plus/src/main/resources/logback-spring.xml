<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false">
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>
    <property name="USERNAME" value="${user.name:-unknown}"/>

    <!-- 判断是否为Mac系统 -->
    <if condition='property("os.name").contains("<PERSON>")'>
        <then>
            <!-- Mac系统路径：/Users/<USER>/data/project/novel/logs -->
            <property name="log.path" value="/Users/<USER>/data/project/novel/logs/${spring.application.name}"/>
        </then>
        <else>
            <!-- 其他系统路径：/data/project/novel/logs -->
            <property name="log.path" value="/data/project/novel/logs/${spring.application.name}"/>
        </else>
    </if>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(---){faint} %clr([%15.15t]){faint} %clr([%X{traceId:-},%X{spanId:-}]){yellow} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <!--先不开启敏感日志隐藏-->
    <!--    <conversionRule conversionWord="msg" converterClass="utils.com.letu.solutions.core.SensitiveLogDataConverter"/>-->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- Console log output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- Log file debug output -->
    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/info.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxFileSize>512MB</maxFileSize>
            <maxHistory>1</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level %clr([%thread]){blue} [%X{traceId},%X{spanId}] [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- Log file warn output -->
    <appender name="warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/warn.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxFileSize>512MB</maxFileSize>
            <maxHistory>1</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level %clr([%thread]){blue} [%X{traceId},%X{spanId}] [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- Log file debug output -->
    <appender name="debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/debug.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxFileSize>1024MB</maxFileSize>
            <maxHistory>1</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level %clr([%thread]){blue} [%X{traceId},%X{spanId}] [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- Log file error output -->
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>512MB</maxFileSize>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date %-5level %clr([%thread]){blue} [%X{traceId},%X{spanId}] [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="warn"/>
        <appender-ref ref="info"/>
        <appender-ref ref="error"/>
    </root>

</configuration>