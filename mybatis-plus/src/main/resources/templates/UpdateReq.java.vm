package ${package.reqPkg};

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
public class ${entity}UpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if(${field.customMap.updateReq})
## 普通字段
    /**
     * ${field.comment}
     */
    #if(!${field.MetaInfo.nullable})
    #if(${field.propertyType.equals("String")})
    @NotEmpty(message = "${field.comment}不可为空")
    #else
    @NotNull(message = "${field.comment}不可为空")
    #end
    #end
    private ${field.propertyType} ${field.propertyName};
    #end
## --foreach end---
  #end
}
