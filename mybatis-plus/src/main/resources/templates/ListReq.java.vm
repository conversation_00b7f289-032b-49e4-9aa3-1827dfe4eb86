package ${package.reqPkg};

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
import com.letu.solutions.model.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ${entity}ListReq extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if(${field.customMap.listReq})
    /**
     * ${field.comment}
     */
    private ${field.propertyType} ${field.propertyName};
    #end
#end
}
