package ${package.ServiceImpl};

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ${package.Mapper}.${table.mapperName};
import com.letu.solutions.count.service.CountService;
import ${package.Entity}.${entity};
import com.letu.solutions.util.CountWrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * $!{table.comment} 服务实现类
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
#if(${kotlin})
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
#else
@Slf4j
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements CountService<${entity}> {
	
	@Override
    public boolean insertOrUpdate(${entity} record, String valueKey, String[] uniqueKeys) {
        UpdateWrapper<${entity}> updateWrapper = new UpdateWrapper<>();
        CountWrapperUtil.setValue(updateWrapper, record, valueKey, uniqueKeys);
        boolean update = baseMapper.update(null, updateWrapper) > 0;
        if (!update) {
            try {
                baseMapper.insert(record);
            } catch (Exception e) {
                baseMapper.update(null, updateWrapper);
            }
        }
        return true;
    }
    
}
#end
