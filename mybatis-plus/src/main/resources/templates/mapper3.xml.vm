<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package.Mapper}.${table.mapperName}">

    #set($notUpdateArr = ["id", "day", "month", "hour", "platform", "create_time", "update_time", "date", "channel_id", "platform", "user_id", "os"])
    <update id="updateByUnique">
        update `${table.name}`
        <set >
            #foreach($field in ${table.fields})
                #if(!${field.keyFlag} && $notUpdateArr.indexOf(${field.name})==-1)##生成普通字段
            <if test="${field.propertyName} != null">
                ${field.name} = ${field.name} + #{${field.propertyName}},
            </if>
                #end
            #end
        </set>
--      todo 该处需手动添加合理唯一索引
        where day = #{day}
    </update>
</mapper>
