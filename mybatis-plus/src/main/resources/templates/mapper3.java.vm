package ${package.Mapper};

import ${package.Entity}.${entity};
import ${superMapperClassPackage};
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * $!{table.comment} Mapper 接口
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${kotlin})
interface ${table.mapperName} : ${superMapperClass}<${entity}>
#else
@Mapper

public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {
    int updateByUnique(${entity} record);
}
#end
