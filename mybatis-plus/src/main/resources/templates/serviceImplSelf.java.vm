package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
#if(${mkControllerMethod})
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import java.util.Date;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import ${package.reqPkg}.${entity}ListReq;
import ${package.reqPkg}.${entity}SaveReq;
import ${package.reqPkg}.${entity}UpdateReq;
import ${package.resPkg}.${entity}PageRes;
import ${package.resPkg}.${entity}DetailRes;
import cn.hutool.core.bean.BeanUtil;
import com.letu.solutions.util.util.PageUtil;
#end

/**
 * $!{table.comment} 服务实现类
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {
#if(${mkControllerMethod})
    @Override
    public Page<${entity}PageRes> selectBasicPage(Page<${entity}> page, ${entity}ListReq request) {
        // 动态构建查询条件
        LambdaQueryWrapper<${entity}> queryWrapper = Wrappers.<${entity}>lambdaQuery()
    #foreach($field in ${table.fields})
        #if(${field.propertyType.equals("String")})
                .eq(ObjectUtil.isNotEmpty(request.get${field.capitalName}()),${entity}::get${field.capitalName}, request.get${field.capitalName}())
        #else
                .eq(ObjectUtil.isNotEmpty(request.get${field.capitalName}()),${entity}::get${field.capitalName}, request.get${field.capitalName}())
        #end
    #end
        .ge(ObjectUtil.isNotEmpty(request.getBeginDate()), ${entity}::day, request.getBeginDate())
        .lt(ObjectUtil.isNotEmpty(request.getEndDate()), ${entity}::day, request.getEndDate());

        Page<${entity}> basicPage = baseMapper.selectPage(page, queryWrapper);
        return PageUtil.builderPage(basicPage, ${entity}PageRes.class);
        }



    @Override
    public List<${entity}PageRes> selectBasicList(${entity}ListReq request) {
        List<${entity}> basicList = baseMapper.selectList(Wrappers.<${entity}>lambdaQuery());
        return BeanUtil.copyToList(basicList, ${entity}PageRes.class);
    }

    @Override
    public ${entity}DetailRes selectByIdBasic(Long id) {
        ${entity} record = baseMapper.selectById(id);
        return BeanUtil.copyProperties(record, ${entity}DetailRes.class);
    }

    @Override
    public boolean saveBasic(${entity}SaveReq record, ExtendData extendData) {
        ${entity} saveRecord = BeanUtil.copyProperties(record, ${entity}.class);
        return baseMapper.insert(saveRecord) > 0;
    }

    @Override
    public boolean updateBasic(${entity}UpdateReq record, ExtendData extendData) {
        ${entity} updateRecord = BeanUtil.copyProperties(record, ${entity}.class);
        updateRecord.setUpdatedAt(DateUtil.date());
        updateRecord.setUpdatedBy(extendData.getAuthentication().getUserName());
        return baseMapper.updateById(updateRecord) > 0;
    }

    @Override
    public boolean removeBasic(Long id, ExtendData extendData) {
        #if(${logicDel})
        return baseMapper.update(null,
                Wrappers.<${entity}>lambdaUpdate()
                        .set(${entity}::getDel, 1)
                        .eq(${entity}::getId, id)) > 0;
        #else
        return baseMapper.deleteById(id) > 0;
        #end
    }
#end
}