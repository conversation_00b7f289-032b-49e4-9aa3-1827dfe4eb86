package ${package.Service};

#if(${mkControllerMethod})
import com.letu.solutions.core.model.ExtendData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${package.Entity}.${entity};
import java.util.List;
import ${package.reqPkg}.${entity}ListReq;
import ${package.reqPkg}.${entity}SaveReq;
import ${package.reqPkg}.${entity}UpdateReq;
import ${package.resPkg}.${entity}PageRes;
import ${package.resPkg}.${entity}DetailRes;
#end

/**
 * $!{table.comment} 服务类
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${kotlin})
interface ${table.serviceName} : ${superServiceClass}<${entity}>
#else
public interface ${table.serviceName} {
    #if(${mkControllerMethod})
    Page<${entity}PageRes> selectBasicPage(Page<${entity}> page, ${entity}ListReq request);

    List<${entity}PageRes> selectBasicList(${entity}ListReq request);

    ${entity}DetailRes selectByIdBasic(Long id);

    boolean saveBasic(${entity}SaveReq record, ExtendData extendData);

    boolean updateBasic(${entity}UpdateReq record, ExtendData extendData);

    boolean removeBasic(Long id, ExtendData extendData);
    #end
}
#end
