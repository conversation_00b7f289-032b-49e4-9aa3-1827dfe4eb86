package ${package.resPkg};

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
import lombok.Data;
import java.io.Serializable;

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
public class ${entity}DetailRes implements Serializable {

    private static final long serialVersionUID = 1L;
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if(${field.customMap.detailRes})
    /**
     * ${field.comment}
     */
    private ${field.propertyType} ${field.propertyName};
    #end
#end
}
