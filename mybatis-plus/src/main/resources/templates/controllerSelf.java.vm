package ${package.Controller};

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import ${package.Service}.${table.serviceName};
#if(${mkControllerMethod})
import com.letu.solutions.core.model.ExtendData;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.core.model.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.cms.aspect.Preauthorize;
import com.letu.solutions.model.cms.request.user.DeleteReq;
import java.util.List;
import ${package.reqPkg}.${entity}ListReq;
import ${package.reqPkg}.${entity}SaveReq;
import ${package.reqPkg}.${entity}UpdateReq;
import ${package.resPkg}.${entity}PageRes;
import ${package.resPkg}.${entity}DetailRes;
#end

/**
 * $!{serverPrefixChina}/$!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@RestController
@Slf4j
@RequiredArgsConstructor
#if(${mkControllerMethod})
@Preauthorize(value = "${serverPrefix}:${serverInterface}", valueDesc = "$!{serverPrefixChina}:$!{table.comment}")
#end
public class ${table.controllerName}{
    private final ${table.serviceName} basicService;
    #if(${mkControllerMethod})
    /**
    * $!{table.comment} 分页查询
    */
    #set($pageAddr = "/"+${table.entityPath}+"/page")
    @Preauthorize(value = "page", valueDesc = "分页查询")
    @GetMapping("/${table.entityPath}/page")
    public R<Page<${entity}PageRes>> loadPage(@Validated ${entity}ListReq request) {
        return R.success(basicService.selectBasicPage(request.getPage(), request));
    }

    /**
     * $!{table.comment} 列表查询
     */
    @Preauthorize(value = "list", valueDesc = "列表查询")
    @GetMapping("/${table.entityPath}/list")
    public R<List<${entity}PageRes>> loadList(@Validated ${entity}ListReq request) {
        return R.success(basicService.selectBasicList(request));
    }

    /**
     * $!{table.comment} 根据id查询
     */
    @Preauthorize(value = "selectById", valueDesc = "根据id查询")
    @GetMapping("/${table.entityPath}/selectById")
    public R<${entity}DetailRes> selectById(@RequestParam("id") Long id) {
        return R.success(basicService.selectByIdBasic(id));
    }

    /**
     * $!{table.comment} 新增
     */
    @Preauthorize(value = "insert", valueDesc = "新增")
    @PostMapping("/${table.entityPath}/insert")
    public R<Boolean> insert(@Validated @RequestBody ${entity}SaveReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.saveBasic(record, extendData));
    }

    /**
     * $!{table.comment} 修改
     */
    @Preauthorize(value = "update", valueDesc = "修改")
    @PostMapping("/${table.entityPath}/update")
    public R<Boolean> update(@Validated @RequestBody ${entity}UpdateReq record, @RequestHeader ExtendData extendData) {
        return R.success(basicService.updateBasic(record, extendData));
    }

    /**
     * $!{table.comment} 删除
     */
    @Preauthorize(value = "delete", valueDesc = "删除")
    @PostMapping("/${table.entityPath}/delete")
    public R<Boolean> delete(@Validated @RequestBody DeleteReq request, @RequestHeader ExtendData extendData) {
        return R.success(basicService.removeBasic(request.getId(), extendData));
    }
    #end
}
