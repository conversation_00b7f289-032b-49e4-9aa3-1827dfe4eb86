package ${package.Entity};

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@TableName("${schemaName}${table.name}")
public class ${entity} implements Serializable {

    private static final long serialVersionUID = 1L;
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})

    /**
    * ${field.comment}
    */
    #if(${field.keyFlag})
## 主键
    #if(${field.keyIdentityFlag})
    @TableId(value = "${field.annotationColumnName}", type = IdType.AUTO)
    #elseif(!$null.isNull(${idType}) && "$!idType" != "")
    @TableId(value = "${field.annotationColumnName}", type = IdType.${idType})
    #elseif(${field.convert})
    @TableId("${field.annotationColumnName}")
    #end
## 普通字段
    #end
    private ${field.propertyType} ${field.propertyName};
## --foreach end---
  #end
}
