package com.letu.solutions.generate;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.letu.solutions.generate.config.AutoGeneratorSelf;
import com.letu.solutions.generate.config.GeneratorConfiguration;
import com.letu.solutions.generate.util.GeneratorUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;


/**
 * 代码生成器
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@SpringBootTest
public class GeneratorTest {
    @Resource
    private DataSourceConfig dataSourceConfig;
    @Resource
    private GeneratorConfiguration generatorConfiguration;

    @Test
    public void generate() {
        AutoGenerator gc = new AutoGeneratorSelf(dataSourceConfig)
                .strategy(GeneratorUtil.strategyConfig(generatorConfiguration.getStrategyConfig(), generatorConfiguration.getTemplate()))
                .global(GeneratorUtil.globalConfig(generatorConfiguration.getGlobalConfig()))
                .packageInfo(GeneratorUtil.packageConfig(generatorConfiguration.getPackageInfo()));
        gc.injection(GeneratorUtil.injectionConfig(generatorConfiguration.getPackageInfo(),gc.getPackageInfo()));
        gc.execute();
        log.info("生成完成");
    }


}
