<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>earnify-task</artifactId>
    <version>0.0.2-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>earnify-task</name>
    <description>定时任务服务</description>

    <parent>
        <groupId>com.letu.solutions</groupId>
        <artifactId>novel</artifactId>
        <version>0.0.2-SNAPSHOT</version>
    </parent>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--服务基础模块-->
        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-core</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-feign</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <!--公共模块-->
        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-model</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-util</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl-job-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>

                    <mainClass>com.letu.solutions.task.TaskApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
</project>
