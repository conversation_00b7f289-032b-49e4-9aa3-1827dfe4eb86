server:
  port: 11803
  servlet:
    context-path: /earnify-task
xxl.job.port: 31403
dubbo:
  protocol:
    port: 21803
  cloud:
    subscribed-services: 'earnify-order,earnify-customer'
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: earnify-task
    type: task
  cloud:
    nacos:
      server-addr: http://192.168.77.101:28848
      discovery:
        namespace: ${discoverNameSpace:server}
        username: earnify
        password: 6sqGL8nj4oIt5P8n
      config:
        file-extension: yml
        namespace: earnify
        username: earnify
        password: 6sqGL8nj4oIt5P8n
  config:
    import:
      - nacos:redis.yaml
      - nacos:mybatis.yaml
      - nacos:base.yaml
      - nacos:nos.yaml
      - nacos:actuator.yaml
      - nacos:dubbo${discoverDubboEnv:}.yaml
      - nacos:account.yaml
      - nacos:oss.yaml?refresh=true
      - nacos:refresh.yaml?refresh=true
      - nacos:xxl-job.yaml
      - nacos:pay.yaml
      - nacos:mysql.yaml
