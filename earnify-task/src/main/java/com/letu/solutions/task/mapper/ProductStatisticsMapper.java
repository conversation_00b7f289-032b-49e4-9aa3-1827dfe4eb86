package com.letu.solutions.task.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 产品统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface ProductStatisticsMapper {

    /**
     * 统计指定日期新增产品数
     * @param date 日期，格式：yyyy-MM-dd
     * @return 新增产品数
     */
    @Select("SELECT COUNT(*) FROM product WHERE DATE(create_time) = #{date}")
    int countNewProductsByDate(@Param("date") String date);
}
