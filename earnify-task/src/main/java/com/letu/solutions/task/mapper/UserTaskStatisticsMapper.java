package com.letu.solutions.task.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户任务统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface UserTaskStatisticsMapper {

    /**
     * 统计指定日期乙方领取任务数
     * @param date 日期，格式：yyyy-MM-dd
     * @return 领取任务数
     */
    @Select("SELECT COUNT(*) FROM user_task WHERE DATE(create_time) = #{date}")
    int countReceivedTasksByDate(@Param("date") String date);
    
    /**
     * 统计指定日期乙方完成任务数
     * @param date 日期，格式：yyyy-MM-dd
     * @return 完成任务数
     */
    @Select("SELECT COUNT(*) FROM user_task WHERE state = 'completed' AND DATE(create_time) = #{date}")
    int countCompletedTasksByDate(@Param("date") String date);
}
