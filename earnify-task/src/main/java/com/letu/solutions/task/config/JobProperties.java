package com.letu.solutions.task.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *定时任务参数
 *
 * <AUTHOR>
 * @date 2020/3/13
 **/
@Component
@ConfigurationProperties(prefix = "xxl.job")
@Data
public class JobProperties {

    private String adminAddresses;

    private String appname;

    private int port;

    private String accessToken;

    private String logPath;

    private int logRetentionDays;
}
