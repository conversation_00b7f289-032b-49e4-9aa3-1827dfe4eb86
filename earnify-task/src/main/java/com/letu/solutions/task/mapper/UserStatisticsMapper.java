package com.letu.solutions.task.mapper;

import com.letu.solutions.model.dto.UserStatisticsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface UserStatisticsMapper {

    /**
     * 统计指定日期新增用户数（按账户类型分组）
     * @param date 日期，格式：yyyy-MM-dd
     * @return 用户统计结果
     */
    @Select("""
            SELECT
              COUNT(*) AS totalUsers,
                  IFNULL(SUM(CASE WHEN account_role = 'client' THEN 1 ELSE 0 END), 0) AS client,
                  IFNULL(SUM(CASE WHEN account_role = 'provider' THEN 1 ELSE 0 END), 0) AS provider     
            FROM user
            WHERE DATE(create_time) = #{date}
            """)
    UserStatisticsDto countNewUsersByDate(@Param("date") String date);
}
