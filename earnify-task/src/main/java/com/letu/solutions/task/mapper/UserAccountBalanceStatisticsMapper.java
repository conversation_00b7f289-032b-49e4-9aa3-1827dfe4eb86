package com.letu.solutions.task.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 用户账户余额统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface UserAccountBalanceStatisticsMapper {

    /**
     * 统计指定日期的冻结奖励金额总和
     * @param date 日期，格式：yyyy-MM-dd
     * @return 冻结奖励金额总和（BigDecimal）
     */
    @Select("SELECT COALESCE(SUM(frozen_amount), 0) FROM user_account_balance WHERE DATE(create_time) = #{date}")
    BigDecimal sumFrozenAmountByDate(@Param("date") String date);
}
