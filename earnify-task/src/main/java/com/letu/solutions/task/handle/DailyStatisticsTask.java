package com.letu.solutions.task.handle;

import com.letu.solutions.model.entity.cms.DataReportStatistics;
import com.letu.solutions.model.dto.UserStatisticsDto;
import com.letu.solutions.task.mapper.*;
import com.letu.solutions.util.util.TimeUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.letu.solutions.core.constant.DateConstants;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class DailyStatisticsTask {


    private final DataReportStatisticsMapper dataReportStatisticsMapper;
    private final UserStatisticsMapper userStatisticsMapper;
    private final ProductStatisticsMapper productStatisticsMapper;
    private final TaskStatisticsMapper taskStatisticsMapper;
    private final UserTaskStatisticsMapper userTaskStatisticsMapper;
    private final DepositRecordStatisticsMapper depositRecordStatisticsMapper;
    private final WithdrawRecordStatisticsMapper withdrawRecordStatisticsMapper;
    private final UserAccountBalanceStatisticsMapper userAccountBalanceStatisticsMapper;
    private final PlatformTransactionBillStatisticsMapper platformTransactionBillStatisticsMapper;

    /**
     * 昨日数据统计任务
     * 由xxl-job调度执行，统计昨天的完整数据
     */
    @XxlJob("yesterdayDataStatistics")
    public void yesterdayDataStatistics() {
        try {
            // 获取昨天的日期
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String dateStr = yesterday.format(DateConstants.DATE_FORMATTER);
            Date statDate = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());

            log.info("开始执行昨日数据统计任务，统计日期：{}", dateStr);

            // 执行统计并保存
            executeStatistics(dateStr, statDate, "昨日数据统计");

        } catch (Exception e) {
            log.error("昨日数据统计任务执行异常", e);
            throw new RuntimeException("昨日数据统计任务执行失败", e);
        }
    }

    /**
     * 今日数据统计任务
     * 由xxl-job调度执行，统计今天的实时数据
     */
    @XxlJob("todayDataStatistics")
    public void todayDataStatistics() {
        try {
            // 获取今天的日期
            LocalDate today = LocalDate.now();
            String dateStr = today.format(DateConstants.DATE_FORMATTER);
            Date statDate = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());

            log.info("开始执行今日数据统计任务，统计日期：{}", dateStr);

            // 执行统计并保存
            executeStatistics(dateStr, statDate, "今日数据统计");

        } catch (Exception e) {
            log.error("今日数据统计任务执行异常", e);
            throw new RuntimeException("今日数据统计任务执行失败", e);
        }
    }

    /**
     * 执行统计的通用方法
     * @param dateStr 日期字符串
     * @param statDate 统计日期
     * @param taskName 任务名称
     */
    private void executeStatistics(String dateStr, Date statDate, String taskName) {
        try {
            // 1. 统计各项数据
            UserStatisticsDto userStats = countNewUser(dateStr);
            int newUserCountA = userStats != null ? userStats.getClient() : 0;
            int newUserCountB = userStats != null ? userStats.getProvider() : 0;
            int newUserCount = userStats != null ? userStats.getTotalUsers() : 0;
            int newProductCount = countNewProduct(dateStr);
            int newTaskCount = countNewTask(dateStr);
            int taskClaimCount = countTaskClaim(dateStr);
            int taskCompleteCount = countTaskComplete(dateStr);
            BigDecimal pointsRechargeSuccess = sumPointsRechargeSuccess(dateStr);
            BigDecimal pointsWithdrawSuccess = sumPointsWithdrawSuccess(dateStr);
            BigDecimal pointsFrozen = sumPointsFrozen(dateStr);
            BigDecimal pointsGranted = sumPointsGranted(dateStr);
            BigDecimal pointsReturned = sumPointsReturned(dateStr);

            // 2. 组装实体
            Date currentTime = new Date();
            DataReportStatistics entity = DataReportStatistics.builder()
                    .day(TimeUtil.today(statDate))
                    .newUserCountA(newUserCountA)
                    .newUserCountB(newUserCountB)
                    .newUserCount(newUserCount)
                    .newProductCount(newProductCount)
                    .newTaskCount(newTaskCount)
                    .taskClaimCount(taskClaimCount)
                    .taskCompleteCount(taskCompleteCount)
                    .pointsRechargeSuccess(pointsRechargeSuccess)
                    .pointsWithdrawSuccess(pointsWithdrawSuccess)
                    .pointsFrozen(pointsFrozen)
                    .pointsGranted(pointsGranted)
                    .pointsReturned(pointsReturned)
                    .createTime(currentTime)
                    .updateTime(currentTime)
                    .build();

            // 3. 使用 INSERT ON DUPLICATE KEY UPDATE 入库
            int result = dataReportStatisticsMapper.insertOrUpdateByStatDate(entity);

            if (result > 0) {
                log.info("{}任务完成，数据已入库。统计结果：新增用户{}人(甲方{}人,乙方{}人)，新增产品{}个，发布任务{}个，领取任务{}个，完成任务{}个，充值成功{}，提现成功{}，冻结金额{}，发放奖励{}，解冻返还{}",
                        taskName, newUserCount, newUserCountA, newUserCountB, newProductCount, newTaskCount, taskClaimCount, taskCompleteCount,
                        pointsRechargeSuccess, pointsWithdrawSuccess, pointsFrozen, pointsGranted, pointsReturned);
            } else {
                log.error("{}任务执行失败，数据入库失败", taskName);
                throw new RuntimeException(taskName + "任务执行失败，数据入库失败");
            }

        } catch (Exception e) {
            log.error("{}任务执行异常，统计日期：{}", taskName, dateStr, e);
            throw new RuntimeException(taskName + "任务执行异常", e);
        }
    }

    /**
     * 统计新增用户数（按账户类型分组）
     */
    private UserStatisticsDto countNewUser(String date) {
        try {
            return userStatisticsMapper.countNewUsersByDate(date);
        } catch (Exception e) {
            log.error("统计新增用户数失败，日期：{}", date, e);
            return new UserStatisticsDto();
        }
    }

    /**
     * 统计新增产品数
     */
    private int countNewProduct(String date) {
        try {
            return productStatisticsMapper.countNewProductsByDate(date);
        } catch (Exception e) {
            log.error("统计新增产品数失败，日期：{}", date, e);
            return 0;
        }
    }

    /**
     * 统计甲方发布任务数
     */
    private int countNewTask(String date) {
        try {
            return taskStatisticsMapper.countPublishedTasksByDate(date);
        } catch (Exception e) {
            log.error("统计发布任务数失败，日期：{}", date, e);
            return 0;
        }
    }

    /**
     * 统计乙方领取任务数
     */
    private int countTaskClaim(String date) {
        try {
            return userTaskStatisticsMapper.countReceivedTasksByDate(date);
        } catch (Exception e) {
            log.error("统计领取任务数失败，日期：{}", date, e);
            return 0;
        }
    }

    /**
     * 统计乙方完成任务数
     */
    private int countTaskComplete(String date) {
        try {
            return userTaskStatisticsMapper.countCompletedTasksByDate(date);
        } catch (Exception e) {
            log.error("统计完成任务数失败，日期：{}", date, e);
            return 0;
        }
    }

    /**
     * 统计充值成功金额
     */
    private BigDecimal sumPointsRechargeSuccess(String date) {
        try {
            return depositRecordStatisticsMapper.sumDepositAmountByDate(date);
        } catch (Exception e) {
            log.error("统计充值成功金额失败，日期：{}", date, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 统计提现成功金额
     */
    private BigDecimal sumPointsWithdrawSuccess(String date) {
        try {
            return withdrawRecordStatisticsMapper.sumWithdrawAmountByDate(date);
        } catch (Exception e) {
            log.error("统计提现成功金额失败，日期：{}", date, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 统计冻结奖励金额
     */
    private BigDecimal sumPointsFrozen(String date) {
        try {
            return userAccountBalanceStatisticsMapper.sumFrozenAmountByDate(date);
        } catch (Exception e) {
            log.error("统计冻结奖励金额失败，日期：{}", date, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 统计发放奖励金额
     */
    private BigDecimal sumPointsGranted(String date) {
        try {
            return platformTransactionBillStatisticsMapper.sumProviderRewardAmountByDate(date);
        } catch (Exception e) {
            log.error("统计发放奖励金额失败，日期：{}", date, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 统计解冻返还金额
     */
    private BigDecimal sumPointsReturned(String date) {
        try {
            return platformTransactionBillStatisticsMapper.sumClientUnfreezeRefundAmountByDate(date);
        } catch (Exception e) {
            log.error("统计解冻返还金额失败，日期：{}", date, e);
            return BigDecimal.ZERO;
        }
    }
} 