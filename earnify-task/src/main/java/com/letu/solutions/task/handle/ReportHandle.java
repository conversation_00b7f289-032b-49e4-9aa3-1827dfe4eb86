package com.letu.solutions.task.handle;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version Id: ReportHandle.java, v 0.1 2025/4/29 15:42 lai_kd Exp $$
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ReportHandle {

    @XxlJob("test")
    public ReturnT<String> statisticsClick() {

        return ReturnT.SUCCESS;
    }
}
