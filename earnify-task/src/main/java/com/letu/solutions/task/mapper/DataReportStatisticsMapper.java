package com.letu.solutions.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.letu.solutions.model.entity.cms.DataReportStatistics;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 数据简报统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper
public interface DataReportStatisticsMapper extends BaseMapper<DataReportStatistics> {

    /**
     * 插入或更新统计数据（基于stat_date唯一索引）
     * @param record 统计数据
     * @return 影响行数
     */
    @Insert("""
            INSERT INTO data_report_statistics (
                day, new_user_count, new_user_count_a, new_user_count_b,
                new_product_count, new_task_count, task_claim_count, task_complete_count,
                points_recharge_success, points_withdraw_success, points_frozen,
                points_granted, points_returned, create_time, update_time
            ) VALUES (
                #{record.day}, #{record.newUserCount}, #{record.newUserCountA}, #{record.newUserCountB},
                #{record.newProductCount}, #{record.newTaskCount}, #{record.taskClaimCount}, #{record.taskCompleteCount},
                #{record.pointsRechargeSuccess}, #{record.pointsWithdrawSuccess}, #{record.pointsFrozen},
                #{record.pointsGranted}, #{record.pointsReturned}, #{record.createTime}, #{record.updateTime}
            )
            ON DUPLICATE KEY UPDATE
                new_user_count = VALUES(new_user_count),
                new_user_count_a = VALUES(new_user_count_a),
                new_user_count_b = VALUES(new_user_count_b),
                new_product_count = VALUES(new_product_count),
                new_task_count = VALUES(new_task_count),
                task_claim_count = VALUES(task_claim_count),
                task_complete_count = VALUES(task_complete_count),
                points_recharge_success = VALUES(points_recharge_success),
                points_withdraw_success = VALUES(points_withdraw_success),
                points_frozen = VALUES(points_frozen),
                points_granted = VALUES(points_granted),
                points_returned = VALUES(points_returned),
                update_time = VALUES(update_time)
            """)
    int insertOrUpdateByStatDate(@Param("record") DataReportStatistics record);
}
