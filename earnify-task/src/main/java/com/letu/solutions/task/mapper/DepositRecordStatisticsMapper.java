package com.letu.solutions.task.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 充值记录统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface DepositRecordStatisticsMapper {

    /**
     * 统计指定日期充值成功金额
     * @param date 日期，格式：yyyy-MM-dd
     * @return 充值成功金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM deposit_record WHERE DATE(create_time) = #{date}")
    BigDecimal sumDepositAmountByDate(@Param("date") String date);
}
