package com.letu.solutions.task.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 平台交易流水统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface PlatformTransactionBillStatisticsMapper {

    /**
     * 统计指定日期发放奖励金总金额
     * @param date 日期，格式：yyyy-MM-dd
     * @return 奖励金总金额（BigDecimal）
     */
    @Select("""
            SELECT COALESCE(SUM(amount), 0)
            FROM platform_transaction_bill
            WHERE account_type = 'provider'
              AND side = 'in'
              AND fund_type IN ('task_reward_to_account', 'task_reward_unfreeze_send')
              AND DATE(create_time) = #{date}
            """)
    BigDecimal sumProviderRewardAmountByDate(@Param("date") String date);

    /**
     * 统计所有历史发放奖励金总金额
     * @return 奖励金总金额（BigDecimal）
     */
    @Select("""
            SELECT COALESCE(SUM(amount), 0)
            FROM platform_transaction_bill
            WHERE account_type = 'provider'
              AND side = 'in'
              AND fund_type IN ('task_reward_to_account', 'task_reward_unfreeze_send')
            """)
    BigDecimal sumAllProviderRewardAmount();

    /**
     * 统计指定日期解冻返还金额总金额
     * @param date 日期，格式：yyyy-MM-dd
     * @return 解冻返还金额总金额（BigDecimal）
     */
    @Select("""
            SELECT COALESCE(SUM(amount), 0)
            FROM platform_transaction_bill
            WHERE account_type = 'client'
              AND side = 'in'
              AND fund_type IN ('task_reward_unfreeze_refund', 'deposit_unfreeze_refund')
              AND DATE(create_time) = #{date}
            """)
    BigDecimal sumClientUnfreezeRefundAmountByDate(@Param("date") String date);
}
