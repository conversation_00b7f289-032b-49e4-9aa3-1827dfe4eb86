package com.letu.solutions.task.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 任务统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface TaskStatisticsMapper {

    /**
     * 统计指定日期甲方发布任务数
     * @param date 日期，格式：yyyy-MM-dd
     * @return 发布任务数
     */
    @Select("SELECT COUNT(*) FROM task WHERE DATE(create_time) = #{date}")
    int countPublishedTasksByDate(@Param("date") String date);
}
