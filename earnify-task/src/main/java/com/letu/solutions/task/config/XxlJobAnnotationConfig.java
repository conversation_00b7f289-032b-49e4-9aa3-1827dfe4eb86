package com.letu.solutions.task.config;

import brave.Tracer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 木鱼
 * @date 2019/11/15.
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class XxlJobAnnotationConfig {
    private final Tracer tracer;
    private final Environment environment;

    @Pointcut(value = "@annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public void pointCut() {
    }

    @Before(value = "pointCut()")
    public void before() {
        tracer.startScopedSpan(environment.getProperty("spring.application.name"));
    }
}
