package com.letu.solutions.task.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 提现记录统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface WithdrawRecordStatisticsMapper {

    /**
     * 统计指定日期提现成功金额
     * @param date 日期，格式：yyyy-MM-dd
     * @return 提现成功金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM withdraw_record WHERE withdraw_status = 'completed' AND DATE(create_time) = #{date}")
    BigDecimal sumWithdrawAmountByDate(@Param("date") String date);
}
