package com.letu.solutions.order.service;

import com.letu.solutions.core.enums.mq.MqEnum;
import com.letu.solutions.util.core.MqSender;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.aop.Advisor;
import org.springframework.aop.framework.Advised;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @version Id: MqTest.java, v 0.1 2025/5/21 10:43 lai_kd Exp $$
 */
@SpringBootTest
@Slf4j
public class MqTest {
    @Autowired
    private MqSender mqSender;

    @Test
    void test(){
        printInterceptors(mqSender);
        mqSender.asyncSendDelay("123", MqEnum.ORDER_CANCEL_DELAY, 1000 * 3600 * 2L);
    }
    public void printInterceptors(MqSender mqSender) {
        if (mqSender instanceof Advised) {
            Advised advised = (Advised) mqSender;
            try {
                Advisor[] advisors = advised.getAdvisors();
                System.out.println("Interceptors for MqSender:");
                for (Advisor advisor : advisors) {
                    log.info("advisor===>[{}]", advisor);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
