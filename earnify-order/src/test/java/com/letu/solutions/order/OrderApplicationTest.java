package com.letu.solutions.order;

import com.alibaba.fastjson2.JSONObject;
import com.letu.solutions.core.enums.OsTypeEnum;
import com.letu.solutions.dubbo.middle.customer.WechatBusinessFacade;
import com.letu.solutions.dubbo.middle.customer.dto.wechat.WechatBusinessCreateUrlLinkReq;
import com.letu.solutions.order.service.OrdersLogic;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class OrderApplicationTest {

    @Autowired
    private OrdersLogic ordersLogic;

    @DubboReference
    private WechatBusinessFacade wechatBusinessFacade;

    @Test
    void uploadImg() {

        //快手上传图片
//        String access_token = ordersLogic.getGlobalAccessToken("ks700591217414182669", OsTypeEnum.ks_mini);
//        HttpRequest request = HttpUtil.createPost("https://open.kuaishou.com/openapi/mp/developer/file/img/uploadWithUrl");
//        request.form("app_id", "ks700591217414182669");
//        request.form("access_token", access_token);
//        request.form("url", "https://cdn.shenyantuling.com/dev/image/business/1920014256167534592.png");
//        try (HttpResponse execute = request.execute()) {
//            System.out.println(execute.body());
//        } catch (Exception e) {
//            throw new ThrowException("调用快手服务器失败");
//        }
        String access_token = ordersLogic.getGlobalAccessToken("wx875abc8a30acd611", OsTypeEnum.wx_mini);
        WechatBusinessCreateUrlLinkReq wechatBusinessCreateUrlLinkReq = new WechatBusinessCreateUrlLinkReq();
        wechatBusinessCreateUrlLinkReq.setAppId("wx875abc8a30acd611");
        wechatBusinessCreateUrlLinkReq.setPath("pages/index/index");
        wechatBusinessCreateUrlLinkReq.setEnvVersion("release");
        wechatBusinessCreateUrlLinkReq.setQuery("bookid=1");
        wechatBusinessCreateUrlLinkReq.setAccessToken(access_token);

        String urlLink = wechatBusinessFacade.createUrlLink(wechatBusinessCreateUrlLinkReq);
        System.out.println(urlLink);
        JSONObject jsonObject = JSONObject.parseObject(urlLink);
        System.out.println(jsonObject.getString("url_link"));
    }
}
