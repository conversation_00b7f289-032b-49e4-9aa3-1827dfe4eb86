package com.letu.solutions.order;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 订单模块启动类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/26
 */
@SpringBootApplication(scanBasePackages = "com.letu")
@EnableDiscoveryClient
@EnableDubbo
public class OrderApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderApplication.class, args);
    }
}

