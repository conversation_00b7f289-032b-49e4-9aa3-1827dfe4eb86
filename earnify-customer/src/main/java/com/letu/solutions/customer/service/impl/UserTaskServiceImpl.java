package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.constant.SysParamConstants;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.core.utils.LanguageUtil;
import com.letu.solutions.customer.mapper.AppealRecordMapper;
import com.letu.solutions.customer.mapper.TaskMapper;
import com.letu.solutions.customer.mapper.TaskStepMapper;
import com.letu.solutions.customer.mapper.UserTaskMapper;
import com.letu.solutions.customer.mapper.UserTaskStepMapper;
import com.letu.solutions.customer.service.PartyUserInfoService;
import com.letu.solutions.customer.service.SysParamService;
import com.letu.solutions.customer.service.UserTaskService;
import com.letu.solutions.customer.utils.CountdownUtil;
import com.letu.solutions.model.entity.cms.AppealRecord;
import com.letu.solutions.model.entity.cms.Task;
import com.letu.solutions.model.entity.cms.TaskStep;
import com.letu.solutions.model.entity.cms.UserTask;
import com.letu.solutions.model.entity.cms.UserTaskStep;
import com.letu.solutions.model.enums.cms.AppealStatusEnum;
import com.letu.solutions.model.enums.cms.TaskStatusEnum;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import com.letu.solutions.model.request.task.FinishStepRequest;
import com.letu.solutions.model.request.task.UserTaskListQueryRequest;
import com.letu.solutions.model.request.user.CompletedUserTaskQueryRequest;
import com.letu.solutions.model.response.customer.PartyUserInfoRes;
import com.letu.solutions.model.response.customer.TaskPageRes;
import com.letu.solutions.model.response.customer.UserTaskPageRes;
import com.letu.solutions.model.cms.response.cms.TaskStepPageRes;
import com.letu.solutions.model.response.user.CompletedUserTaskResponse;
import com.letu.solutions.model.response.task.TaskReceiveResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户任务服务实现类
 * User Task Service Implementation
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserTaskServiceImpl extends ServiceImpl<UserTaskMapper, UserTask> implements UserTaskService {
    @Autowired
    private PartyUserInfoService partyUserInfoService;
    
    @Autowired
    private SysParamService sysParamService;
    
    @Autowired
    private TaskMapper taskMapper;
    
    @Autowired
    private UserTaskMapper userTaskMapper;
    
    @Autowired
    private TaskStepMapper taskStepMapper;
    
    @Autowired
    private UserTaskStepMapper userTaskStepMapper;
    
    @Autowired
    private AppealRecordMapper appealRecordMapper;
    
    @Autowired
    private TaskServiceImpl taskServiceImpl;
    
    private final TransactionTemplate transactionTemplate;

    /**
     * 我的任务分页查询
     * 支持多条件筛选、排序、倒计时计算
     * 
     * @param page 分页对象
     * @param queryRequest 查询参数
     * @param extendData 用户扩展信息
     * @return 分页结果
     */
    @Override
    public Page<UserTaskPageRes> page(Page<UserTaskPageRes> page, UserTaskListQueryRequest queryRequest, ExtendData extendData) {
        // 预处理查询参数：过滤null元素
        preprocessQueryRequest(queryRequest);
        
        // 执行分页查询
        Page<UserTaskPageRes> result = userTaskMapper.selectUserTaskPageResPage(page, queryRequest, extendData.getUserId());
        
        // 补充甲方信息
        enrichPartyUserInfo(result);
        
        return result;
    }
    
    /**
     * 预处理查询参数：过滤null元素
     * 
     * @param queryRequest 查询请求
     */
    private void preprocessQueryRequest(UserTaskListQueryRequest queryRequest) {
        // 过滤typeList中的null元素
        if (queryRequest.getTypeList() != null) {
            queryRequest.setTypeList(
                queryRequest.getTypeList().stream()
                    .filter(java.util.Objects::nonNull)
                    .collect(java.util.stream.Collectors.toList())
            );
            if (queryRequest.getTypeList().isEmpty()) {
                queryRequest.setTypeList(null);
            }
        }
        
        // 过滤statusList中的null元素
        if (queryRequest.getStatusList() != null) {
            queryRequest.setStatusList(
                queryRequest.getStatusList().stream()
                    .filter(java.util.Objects::nonNull)
                    .collect(java.util.stream.Collectors.toList())
            );
            if (queryRequest.getStatusList().isEmpty()) {
                queryRequest.setStatusList(null);
            }
        }
    }
    
    /**
     * 补充甲方信息和任务进度
     * 
     * @param result 分页结果
     */
    private void enrichPartyUserInfo(Page<UserTaskPageRes> result) {
        if (result != null && result.getRecords() != null) {
            for (UserTaskPageRes res : result.getRecords()) {
                if (res.getTaskId() != null) {
                    // 补充甲方信息
                    PartyUserInfoRes partyUserInfo = partyUserInfoService.getPartyUserInfoByTaskId(res.getTaskId());
                    res.setPartyUserInfo(partyUserInfo);
                    
                    // 计算任务进度
                    calculateUserTaskProgressForPage(res);
                }
            }
        }
    }

    /**
     * 我的任务详情
     * 包含倒计时计算、甲方信息、任务步骤等
     * 
     * @param userTaskId 用户任务ID
     * @param extendData 用户扩展信息
     * @return 任务详情，如果不存在或无权限则返回null
     */
    @Override
    public TaskPageRes detail(Long userTaskId, ExtendData extendData) {
        // 直接调用TaskServiceImpl的通用方法获取TaskPageRes
        return taskServiceImpl.getTaskDetailByUserTaskId(userTaskId, extendData.getUserId());
    }
    
    /**
     * 将UserTaskStep列表转换为TaskStepPageRes列表
     * 
     * @param userStepList UserTaskStep列表
     * @return TaskStepPageRes列表
     */
    private List<TaskStepPageRes> convertUserTaskStepsToTaskStepPageRes(List<UserTaskStep> userStepList) {
        if (userStepList == null || userStepList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<TaskStepPageRes> result = new ArrayList<>();
        for (UserTaskStep userStep : userStepList) {
            TaskStepPageRes stepRes = new TaskStepPageRes();
            
            // 复制基本信息
            stepRes.setId(userStep.getId());
            stepRes.setTaskId(userStep.getTaskId());
            stepRes.setTaskDescribe(userStep.getTaskDescribe());
            stepRes.setSrot(userStep.getSrot());
            stepRes.setStepType(convertStringToStepTypeList(userStep.getStepType()));

            // 设置用户步骤特有字段
            stepRes.setUserTaskStepId(userStep.getId());
            stepRes.setUserTaskId(userStep.getUserTaskId());
            stepRes.setTaskTextOperate(userStep.getTaskTextOperate());
            stepRes.setTaskImageOperate(convertStringToImageList(userStep.getTaskImageOperate()));
            stepRes.setIsSaved(userStep.getIsSaved());
            
            result.add(stepRes);
        }
        
        return result;
    }
    
    /**
     * 将字符串转换为步骤类型字符串列表
     * 
     * @param stepTypeStr 步骤类型字符串，如 "text,image"
     * @return 步骤类型字符串列表
     */
    private List<String> convertStringToStepTypeList(String stepTypeStr) {
        if (stepTypeStr == null || stepTypeStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        String[] types = stepTypeStr.split(",");
        
        for (String type : types) {
            String trimmedType = type.trim();
            if (!trimmedType.isEmpty()) {
                result.add(trimmedType);
            }
        }
        
        return result;
    }
    
    /**
     * 将字符串转换为图片URL列表
     * 
     * @param imageStr 图片字符串，如 "url1,url2,url3"
     * @return 图片URL列表
     */
    private List<String> convertStringToImageList(String imageStr) {
        if (imageStr == null || imageStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        String[] images = imageStr.split(",");
        
        for (String image : images) {
            String trimmedImage = image.trim();
            if (!trimmedImage.isEmpty()) {
                result.add(trimmedImage);
            }
        }
        
        return result;
    }
    
    /**
     * 将图片URL列表转换为字符串
     * 
     * @param imageList 图片URL列表
     * @return 逗号分隔的字符串
     */
    private String convertImageListToString(List<String> imageList) {
        if (imageList == null || imageList.isEmpty()) {
            return null;
        }
        
        return String.join(",", imageList);
    }
    
    /**
     * 计算用户任务进度
     * 
     * @param res 响应对象
     * @param stepList 步骤列表
     */
    private void calculateUserTaskProgress(UserTaskPageRes res, List<UserTaskStep> stepList) {
        if (stepList == null || stepList.isEmpty()) {
            res.setUserTaskProgress("0/0");
            return;
        }
        
        int completedSteps = 0;
        int totalSteps = stepList.size();
        
        for (UserTaskStep step : stepList) {
            if (Boolean.TRUE.equals(step.getIsSaved())) {
                completedSteps++;
            }
        }
        
        res.setUserTaskProgress(completedSteps + "/" + totalSteps);
    }
    
    /**
     * 计算分页查询中的用户任务进度
     * 
     * @param res 响应对象
     */
    private void calculateUserTaskProgressForPage(UserTaskPageRes res) {
        if (res.getTaskId() == null || res.getId() == null) {
            res.setUserTaskProgress("0/0");
            return;
        }
        
        // 查询该用户任务的步骤列表
        List<UserTaskStep> stepList = userTaskStepMapper.selectList(
            new LambdaQueryWrapper<UserTaskStep>()
                .eq(UserTaskStep::getUserTaskId, res.getId())
                .orderByAsc(UserTaskStep::getSrot)
        );
        
        if (stepList == null || stepList.isEmpty()) {
            res.setUserTaskProgress("0/0");
            return;
        }
        
        int completedSteps = 0;
        int totalSteps = stepList.size();
        
        for (UserTaskStep step : stepList) {
            if (Boolean.TRUE.equals(step.getIsSaved())) {
                completedSteps++;
            }
        }
        
        res.setUserTaskProgress(completedSteps + "/" + totalSteps);
    }
    /**
     * 任务领取
     * 包含任务验证、并发控制、步骤复制等
     * 
     * @param taskId 任务ID
     * @param extendData 用户扩展信息
     * @return 操作结果
     */
    @Override
    public R<TaskReceiveResponse> receive(Long taskId, ExtendData extendData) {
        log.info("[UserTaskServiceImpl] 用户{}请求领取任务, taskId={}", extendData.getUserId(), taskId);
        
        // 1. 验证任务是否存在且状态正确
        Task task = validateTaskForReceive(taskId);
        if (task == null) {
            return R.fail(LanguageUtil.trans(I18nConstants.TASK_NOT_FOUND));
        }
        
        // 2. 检查任务状态
        if (!isTaskStatusValidForReceive(task.getState())) {
            log.warn("[UserTaskServiceImpl] 任务状态不可领取, taskId={}, state={}", taskId, task.getState());
            return R.fail(LanguageUtil.trans(I18nConstants.TASK_NOT_IN_PROGRESS));
        }
        
        // 3. 检查是否已领取
        if (hasUserReceivedTask(taskId, extendData.getUserId())) {
            log.warn("[UserTaskServiceImpl] 用户{}已领取过任务, taskId={}", extendData.getUserId(), taskId);
            return R.fail(LanguageUtil.trans(I18nConstants.TASK_ALREADY_RECEIVED));
        }
        
        // 4. 执行领取逻辑（事务）
        return executeReceiveTransaction(taskId, task, extendData);
    }
    
    /**
     * 验证任务是否可以领取
     * 
     * @param taskId 任务ID
     * @return 任务对象，如果不存在则返回null
     */
    private Task validateTaskForReceive(Long taskId) {
        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            log.warn("[UserTaskServiceImpl] 任务不存在, taskId={}", taskId);
        }
        return task;
    }
    
    /**
     * 检查任务状态是否允许领取
     * 
     * @param state 任务状态
     * @return 是否允许领取
     */
    private boolean isTaskStatusValidForReceive(TaskStatusEnum state) {
        return state != null && (TaskStatusEnum.inProgress.equals(state)
                || TaskStatusEnum.pendingOrders.equals(state));
    }
    
    /**
     * 检查用户是否已领取任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否已领取
     */
    private boolean hasUserReceivedTask(Long taskId, Long userId) {
        Long count = userTaskMapper.selectCount(
            new LambdaQueryWrapper<UserTask>()
                .eq(UserTask::getTaskId, taskId)
                .eq(UserTask::getUserId, userId)
        );
        return count > 0;
    }
    
    /**
     * 执行领取任务的事务逻辑
     * 
     * @param taskId 任务ID
     * @param task 任务对象
     * @param extendData 用户扩展信息
     * @return 操作结果
     */
    private R<TaskReceiveResponse> executeReceiveTransaction(Long taskId, Task task, ExtendData extendData) {
        return transactionTemplate.execute(transactionStatus -> {
            try {
                // 1. 原子性更新任务领取数量
                int updated = taskMapper.updateTaskReceiveNum(taskId);
                if (updated != 1) {
                    log.warn("[UserTaskServiceImpl] 任务已被领完, taskId={}", taskId);
                    transactionStatus.setRollbackOnly();
                    return R.fail(LanguageUtil.trans(I18nConstants.TASK_RECEIVE_LIMIT_EXCEEDED));
                }
                
                // 2. 创建用户任务记录
                UserTask userTask = createUserTask(taskId, task, extendData);
                int insert = userTaskMapper.insert(userTask);
                if (insert != 1) {
                    log.error("[UserTaskServiceImpl] 插入user_task失败, userId={}, taskId={}", extendData.getUserId(), taskId);
                    transactionStatus.setRollbackOnly();
                    return R.fail(LanguageUtil.trans(I18nConstants.TASK_RECEIVE_FAILED));
                }
                
                // 3. 复制任务步骤
                if (!copyTaskSteps(taskId, userTask.getId(), extendData.getUserId())) {
                    transactionStatus.setRollbackOnly();
                    return R.fail(LanguageUtil.trans(I18nConstants.TASK_RECEIVE_FAILED));
                }
                
                log.info("[UserTaskServiceImpl] 用户{}领取任务成功, taskId={}, userTaskId={}", extendData.getUserId(), taskId, userTask.getId());
                return R.success(TaskReceiveResponse.success(userTask.getId(), taskId));
            } catch (Exception e) {
                log.error("[UserTaskServiceImpl] 领取任务事务异常, userId={}, taskId={}, e={}", 
                    extendData.getUserId(), taskId, e.getMessage(), e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });
    }
    
    /**
     * 创建用户任务对象
     * 
     * @param taskId 任务ID
     * @param task 任务对象
     * @param extendData 用户扩展信息
     * @return 用户任务对象
     */
    private UserTask createUserTask(Long taskId, Task task, ExtendData extendData) {
        UserTask userTask = new UserTask();
        userTask.setTaskId(taskId);
        userTask.setUserId(extendData.getUserId());
        userTask.setState(UserTaskStatusEnum.incomplete);
        userTask.setPrice(task.getPrice());
        
        // 计算到期时间
        if (task.getTime() != null) {
            long expireMillis = System.currentTimeMillis() + task.getTime() * 60L * 60 * 1000;
            userTask.setTime(new Date(expireMillis));
        } else {
            userTask.setTime(null);
        }
        
        userTask.setCreateTime(new Date());
        userTask.setUpdateTime(new Date());
        return userTask;
    }
    
    /**
     * 复制任务步骤到用户任务步骤表
     * 
     * @param taskId 任务ID
     * @param userTaskId 用户任务ID
     * @param userId 用户ID
     * @return 是否复制成功
     */
    private boolean copyTaskSteps(Long taskId, Long userTaskId, Long userId) {
        List<TaskStep> stepList = taskStepMapper.selectList(
            new LambdaQueryWrapper<TaskStep>().eq(TaskStep::getTaskId, taskId)
        );
        
        if (stepList != null && !stepList.isEmpty()) {
            List<UserTaskStep> userTaskStepList = new ArrayList<>();
            for (TaskStep step : stepList) {
                UserTaskStep uts = new UserTaskStep();
                uts.setUserTaskId(userTaskId);
                uts.setTaskId(taskId);
                uts.setUserId(userId);
                uts.setTaskDescribe(step.getTaskDescribe());
                uts.setSrot(step.getSrot());
                uts.setIsSaved(false);
                uts.setTaskTextOperate(null);
                uts.setTaskImageOperate(null);
                uts.setStepType(step.getStepType()); // 复制步骤类型
                userTaskStepList.add(uts);
            }
            
            int insert = userTaskStepMapper.batchInsert(userTaskStepList);
            if (insert != stepList.size()) {
                log.error("[UserTaskServiceImpl] 插入user_task_step失败, userId={}, taskId={}", userId, taskId);
                return false;
            }
        }
        return true;
    }

    /**
     * 申诉接口
     * 仅允许已完成状态的任务进行申诉
     * 
     * @param userTaskId 用户任务ID
     * @param extendData 用户扩展信息
     * @return 操作结果
     */
    @Override
    public R<String> appeal(Long userTaskId, ExtendData extendData) {
        log.info("[UserTaskServiceImpl] 用户{}请求申诉任务, userTaskId={}", extendData.getUserId(), userTaskId);
        
        // 1. 验证用户任务
        UserTask userTask = validateUserTaskForAppeal(userTaskId, extendData.getUserId());
        if (userTask == null) {
            return R.fail(LanguageUtil.trans(I18nConstants.APPEAL_FAILED_NO_PERMISSION));
        }
        
        // 2. 检查任务状态
        if (!UserTaskStatusEnum.completed.equals(userTask.getState())) {
            log.warn("[UserTaskServiceImpl] 申诉失败, 状态不允许, userId={}, userTaskId={}, state={}", 
                extendData.getUserId(), userTaskId, userTask.getState());
            return R.fail(LanguageUtil.trans(I18nConstants.APPEAL_FAILED_STATUS_NOT_ALLOWED));
        }
        
        // 3. 执行申诉逻辑
        return executeAppealLogic(userTask, extendData);
    }
    
    /**
     * 验证用户任务是否可以申诉
     * 
     * @param userTaskId 用户任务ID
     * @param userId 用户ID
     * @return 用户任务对象，如果不存在或无权限则返回null
     */
    private UserTask validateUserTaskForAppeal(Long userTaskId, Long userId) {
        UserTask userTask = userTaskMapper.selectById(userTaskId);
        if (userTask == null || !userTask.getUserId().equals(userId)) {
            log.warn("[UserTaskServiceImpl] 申诉失败, 任务不存在或无权限, userId={}, userTaskId={}", userId, userTaskId);
            return null;
        }
        return userTask;
    }
    
    /**
     * 执行申诉逻辑
     * 
     * @param userTask 用户任务
     * @param extendData 用户扩展信息
     * @return 操作结果
     */
    private R<String> executeAppealLogic(UserTask userTask, ExtendData extendData) {
        // 更新任务状态和申诉时间
        userTask.setState(UserTaskStatusEnum.appealInProgress);
        userTask.setUpdateTime(new Date());
        
        // 从系统参数获取申诉有效期小时数，默认为24小时
        Integer appealValidHours = sysParamService.getIntValueByKey(SysParamConstants.APPEAL_VALID_HOURS, 24);
        long appealTimeMillis = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(appealValidHours);
        userTask.setAppealTime(new Date(appealTimeMillis));
        
        int updated = userTaskMapper.updateById(userTask);
        if (updated == 1) {
            log.info("[UserTaskServiceImpl] 用户{}申诉成功, userTaskId={}", extendData.getUserId(), userTask.getId());
            
            // 创建申诉记录
            createAppealRecord(userTask, extendData);
            
            return R.success(LanguageUtil.trans(I18nConstants.APPEAL_SUCCESS));
        } else {
            log.error("[UserTaskServiceImpl] 用户{}申诉失败, userTaskId={}", extendData.getUserId(), userTask.getId());
            return R.fail(LanguageUtil.trans(I18nConstants.APPEAL_FAILED));
        }
    }
    
    /**
     * 创建申诉记录
     * 
     * @param userTask 用户任务
     * @param extendData 用户扩展信息
     */
    private void createAppealRecord(UserTask userTask, ExtendData extendData) {
        AppealRecord appealRecord = new AppealRecord();
        appealRecord.setTaskId(userTask.getTaskId());
        
        // 查询甲方信息
        PartyUserInfoRes partyUserInfo = partyUserInfoService.getPartyUserInfoByTaskId(userTask.getTaskId());
        if (partyUserInfo != null) {
            appealRecord.setPartyAUserId(partyUserInfo.getUserId());
            appealRecord.setPartyAUsername(partyUserInfo.getName());
        }
        
        // 查询任务，补充产品信息
        Task task = taskMapper.selectById(userTask.getTaskId());
        if (task != null) {
            appealRecord.setProductId(task.getProductId());
            appealRecord.setProductName(task.getName());
        }
        
        appealRecord.setPartyBUserId(extendData.getUserId());
        appealRecord.setAppealStatus(AppealStatusEnum.PENDING.getCode());
        appealRecord.setCreateTime(new Date());
        
        appealRecordMapper.insert(appealRecord);
    }

    /**
     * 提交任务接口
     * 检查所有步骤是否已完成，然后提交任务
     * 
     * @param userTaskId 用户任务ID
     * @param extendData 用户扩展信息
     * @return 操作结果
     */
    @Override
    public R<String> submit(Long userTaskId, ExtendData extendData) {
        // 1. 验证用户任务
        UserTask userTask = validateUserTaskForSubmit(userTaskId, extendData.getUserId());
        if (userTask == null) {
            return R.fail(LanguageUtil.trans(I18nConstants.TASK_NOT_EXIST_OR_NO_PERMISSION));
        }
        
        // 2. 检查所有步骤是否已完成
        if (!areAllStepsCompleted(userTask.getTaskId(), extendData.getUserId())) {
            return R.fail(LanguageUtil.trans(I18nConstants.PLEASE_SAVE_ALL_STEPS_BEFORE_SUBMITTING));
        }
        
        // 3. 更新任务状态为待审核
        userTask.setState(UserTaskStatusEnum.pendingApproval);
        userTask.setUpdateTime(new Date());
        userTaskMapper.updateById(userTask);
        
        return R.success(LanguageUtil.trans(I18nConstants.SUBMIT_SUCCESS));
    }

    /**
     * 完成步骤接口
     * 支持自动领取任务和步骤完成
     * 
     * @param request 完成步骤请求
     * @param extendData 用户扩展信息
     * @return 操作结果
     */
    @Override
    public R<String> finishStep(FinishStepRequest request, ExtendData extendData) {
        // 1. 参数验证
        if (!validateFinishStepRequest(request)) {
            return R.fail(LanguageUtil.trans(I18nConstants.PARAMETERS_INCOMPLETE));
        }
        
        // 2. 获取或创建用户任务
        UserTask userTask = getOrCreateUserTask(request, extendData);
        if (userTask == null) {
            return R.fail(LanguageUtil.trans(I18nConstants.TASK_NOT_EXIST_OR_NO_PERMISSION));
        }
        
        // 3. 检查任务状态
        if (!UserTaskStatusEnum.incomplete.equals(userTask.getState())) {
            return R.fail(LanguageUtil.trans(I18nConstants.TASK_NOT_IN_PROGRESS_NO_MORE_OPERATIONS));
        }
        
        // 4. 查找并完成步骤
        return findAndCompleteStep(request, userTask, extendData);
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 验证提交任务的用户任务
     * 
     * @param userTaskId 用户任务ID
     * @param userId 用户ID
     * @return 用户任务对象，如果不存在或无权限则返回null
     */
    private UserTask validateUserTaskForSubmit(Long userTaskId, Long userId) {
        UserTask userTask = userTaskMapper.selectById(userTaskId);
        if (userTask == null || !userTask.getUserId().equals(userId)) {
            return null;
        }
        return userTask;
    }
    
    /**
     * 检查所有步骤是否已完成
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否所有步骤都已完成
     */
    private boolean areAllStepsCompleted(Long taskId, Long userId) {
        List<UserTaskStep> steps = userTaskStepMapper.selectList(
            new LambdaQueryWrapper<UserTaskStep>()
                .eq(UserTaskStep::getTaskId, taskId)
                .eq(UserTaskStep::getUserId, userId)
        );
        return !steps.isEmpty() && steps.stream().allMatch(s -> Boolean.TRUE.equals(s.getIsSaved()));
    }
    
    /**
     * 验证完成步骤请求参数
     * 
     * @param request 请求对象
     * @return 是否有效
     */
    private boolean validateFinishStepRequest(FinishStepRequest request) {
        if (request == null || (request.getTaskTextOperate() == null && 
            (request.getTaskImageOperate() == null || request.getTaskImageOperate().isEmpty()))) {
            return false;
        }
        
        // 检查步骤标识参数
        if (request.getSrot() == null && request.getUserTaskStepId() == null) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取或创建用户任务
     * 
     * @param request 请求对象
     * @param extendData 用户扩展信息
     * @return 用户任务对象
     */
    private UserTask getOrCreateUserTask(FinishStepRequest request, ExtendData extendData) {
        // 如果提供了userTaskId，直接使用
        if (request.getUserTaskId() != null) {
            UserTask userTask = userTaskMapper.selectById(request.getUserTaskId());
            if (userTask == null || !userTask.getUserId().equals(extendData.getUserId())) {
                return null;
            }
            return userTask;
        }
        
        // 如果提供了taskId，尝试自动领取任务
        if (request.getTaskId() != null) {
            log.info("[UserTaskServiceImpl] 用户{}尝试自动领取任务, taskId={}, srot={}", 
                extendData.getUserId(), request.getTaskId(), request.getSrot());
            
            // 先检查是否已经领取过这个任务
            UserTask existingUserTask = userTaskMapper.selectOne(
                new LambdaQueryWrapper<UserTask>()
                    .eq(UserTask::getTaskId, request.getTaskId())
                    .eq(UserTask::getUserId, extendData.getUserId())
            );
            
            if (existingUserTask != null) {
                log.info("[UserTaskServiceImpl] 用户{}已领取过任务, 使用现有userTaskId={}", 
                    extendData.getUserId(), existingUserTask.getId());
                return existingUserTask;
            } else {
                // 没有领取过，自动领取任务
                R<TaskReceiveResponse> receiveResult = receive(request.getTaskId(), extendData);
                if (!receiveResult.isOk()) {
                    log.warn("[UserTaskServiceImpl] 用户{}自动领取任务失败, taskId={}, error={}",
                        extendData.getUserId(), request.getTaskId(), receiveResult.getMessage());
                    return null;
                }
                
                // 领取成功后，直接使用返回的userTaskId
                Long userTaskId = receiveResult.getData().getUserTaskId();
                UserTask userTask = userTaskMapper.selectById(userTaskId);
                if (userTask == null) {
                    log.error("[UserTaskServiceImpl] 用户{}自动领取任务成功但查询失败, taskId={}, userTaskId={}", 
                        extendData.getUserId(), request.getTaskId(), userTaskId);
                    return null;
                }
                log.info("[UserTaskServiceImpl] 用户{}自动领取任务成功, userTaskId={}", 
                    extendData.getUserId(), userTask.getId());
                return userTask;
            }
        }
        
        return null;
    }
    
    /**
     * 查找并完成步骤
     * 
     * @param request 请求对象
     * @param userTask 用户任务
     * @param extendData 用户扩展信息
     * @return 操作结果
     */
    private R<String> findAndCompleteStep(FinishStepRequest request, UserTask userTask, ExtendData extendData) {
        // 查找对应的步骤
        UserTaskStep step = findUserTaskStep(request, userTask, extendData.getUserId());
        
        if (step == null) {
            log.warn("[UserTaskServiceImpl] 用户{}步骤不存在, userTaskId={}, srot={}", 
                extendData.getUserId(), userTask.getId(), request.getSrot());
            return R.fail(LanguageUtil.trans(I18nConstants.STEP_NOT_EXIST_OR_NO_PERMISSION));
        }
        
        // 检查步骤是否已经完成
        if (Boolean.TRUE.equals(step.getIsSaved())) {
            log.warn("[UserTaskServiceImpl] 用户{}步骤已完成, userTaskId={}, srot={}", 
                extendData.getUserId(), userTask.getId(), request.getSrot());
            return R.fail(LanguageUtil.trans(I18nConstants.STEP_ALREADY_COMPLETED));
        }
        
        // 更新步骤状态
        step.setIsSaved(true);
        step.setUpdateTime(new Date());
        step.setTaskTextOperate(request.getTaskTextOperate());
        step.setTaskImageOperate(convertImageListToString(request.getTaskImageOperate()));
        userTaskStepMapper.updateById(step);
        
        log.info("[UserTaskServiceImpl] 用户{}完成步骤成功, userTaskId={}, srot={}, stepId={}", 
            extendData.getUserId(), userTask.getId(), request.getSrot(), step.getId());
        return R.success(LanguageUtil.trans(I18nConstants.STEP_COMPLETED));
    }
    
    /**
     * 查找用户任务步骤
     * 
     * @param request 请求对象
     * @param userTask 用户任务
     * @param userId 用户ID
     * @return 用户任务步骤
     */
    private UserTaskStep findUserTaskStep(FinishStepRequest request, UserTask userTask, Long userId) {
        // 如果提供了userTaskStepId，直接查询
        if (request.getUserTaskStepId() != null) {
            return userTaskStepMapper.selectOne(
                new LambdaQueryWrapper<UserTaskStep>()
                    .eq(UserTaskStep::getId, request.getUserTaskStepId())
                    .eq(UserTaskStep::getUserId, userId)
                    .eq(UserTaskStep::getUserTaskId, userTask.getId())
            );
        }
        
        // 如果提供了srot，根据排序查找
        if (request.getSrot() != null) {
            return userTaskStepMapper.selectOne(
                new LambdaQueryWrapper<UserTaskStep>()
                    .eq(UserTaskStep::getTaskId, userTask.getTaskId())
                    .eq(UserTaskStep::getUserId, userId)
                    .eq(UserTaskStep::getUserTaskId, userTask.getId())
                    .eq(UserTaskStep::getSrot, request.getSrot())
            );
        }
        
        return null;
    }

    @Override
    public Page<CompletedUserTaskResponse> getCompletedUserTaskPage(CompletedUserTaskQueryRequest request, ExtendData extendData) {
        log.info("用户{}查询已完成且发布者为甲方账户的用户任务，请求参数：{}", extendData.getUserId(), request);
        
        // 如果未指定状态列表，默认查询待审核和已完成状态
        if (CollectionUtil.isNotEmpty(request.getStateList())) {
            request.setStateList(Arrays.asList("appealPass", "completed"));
        }

        // 创建分页对象
        Page<CompletedUserTaskResponse> page = new Page<>(request.getCurrent(), request.getSize());
        
        // 直接调用Mapper查询
        Page<CompletedUserTaskResponse> result = userTaskMapper.selectCompletedUserTaskPage(page, request);
        
        log.info("用户{}查询已完成且发布者为甲方账户的用户任务成功，总数：{}", extendData.getUserId(), result.getTotal());
        return result;
    }
} 