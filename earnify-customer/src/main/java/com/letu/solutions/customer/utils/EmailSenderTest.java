package com.letu.solutions.customer.utils;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * EmailSender 测试类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
public class EmailSenderTest {

    public static void main(String[] args) {
        log.info("=== EmailSender 工具类测试 ===");
        
        // 创建邮件配置
        EmailSender.EmailConfig config = new EmailSender.EmailConfig(
            "smtp.qq.com",           // host
            465,                     // port
            "<EMAIL>",      // user
            "ageknjcetlqebjhc",      // pass
            "<EMAIL>"       // from
        );
        config.setDebug(true); // 开启调试模式
        
        // 测试1: 发送验证码邮件
        testSendVerificationCode(config);
        
        // 测试2: 发送测试邮件
        testSendTestEmail(config);
        
        // 测试3: 发送自定义邮件
        testSendCustomEmail(config);
        
        // 测试4: 配置验证测试
        testConfigValidation();
    }

    /**
     * 测试发送验证码邮件
     */
    private static void testSendVerificationCode(EmailSender.EmailConfig config) {
        log.info("=== 测试1: 发送验证码邮件 ===");
        
        String toEmail = "<EMAIL>";
        String code = RandomUtil.randomNumbers(6);
        
        EmailSender.EmailResult result = EmailSender.sendVerificationCode(config, toEmail, code);
        
        if (result.isSuccess()) {
            log.info("验证码邮件发送成功！验证码: {}", code);
        } else {
            log.error("验证码邮件发送失败: {}", result.getMessage());
        }
    }

    /**
     * 测试发送测试邮件
     */
    private static void testSendTestEmail(EmailSender.EmailConfig config) {
        log.info("=== 测试2: 发送测试邮件 ===");
        
        String toEmail = "<EMAIL>";
        
        EmailSender.EmailResult result = EmailSender.sendTestEmail(config, toEmail);
        
        if (result.isSuccess()) {
            log.info("测试邮件发送成功！");
        } else {
            log.error("测试邮件发送失败: {}", result.getMessage());
        }
    }

    /**
     * 测试发送自定义邮件
     */
    private static void testSendCustomEmail(EmailSender.EmailConfig config) {
        log.info("=== 测试3: 发送自定义邮件 ===");
        
        EmailSender.EmailContent content = new EmailSender.EmailContent(
            "<EMAIL>",
            "自定义邮件测试",
            "这是一封自定义邮件。\n\n发送时间: " + new java.util.Date() + "\n\n---\nEarnify 系统"
        );
        
        EmailSender.EmailResult result = EmailSender.sendEmail(config, content);
        
        if (result.isSuccess()) {
            log.info("自定义邮件发送成功！");
        } else {
            log.error("自定义邮件发送失败: {}", result.getMessage());
        }
    }

    /**
     * 测试配置验证
     */
    private static void testConfigValidation() {
        log.info("=== 测试4: 配置验证测试 ===");
        
        // 测试空配置
        EmailSender.EmailConfig emptyConfig = new EmailSender.EmailConfig();
        EmailSender.EmailContent content = new EmailSender.EmailContent("<EMAIL>", "Test", "Test");
        
        EmailSender.EmailResult result = EmailSender.sendEmail(emptyConfig, content);
        log.info("空配置测试结果: {}", result.getMessage());
        
        // 测试部分配置
        EmailSender.EmailConfig partialConfig = new EmailSender.EmailConfig();
        partialConfig.setHost("smtp.qq.com");
        partialConfig.setPort(465);
        // 缺少其他配置
        
        result = EmailSender.sendEmail(partialConfig, content);
        log.info("部分配置测试结果: {}", result.getMessage());
    }

    /**
     * 批量测试
     */
    public static void batchTest() {
        log.info("=== 批量测试邮件发送 ===");
        
        EmailSender.EmailConfig config = new EmailSender.EmailConfig(
            "smtp.qq.com",
            465,
            "<EMAIL>",
            "ageknjcetlqebjhc",
            "<EMAIL>"
        );
        config.setDebug(false); // 关闭调试模式避免日志过多
        
        int successCount = 0;
        int failCount = 0;
        int totalCount = 5;
        
        for (int i = 1; i <= totalCount; i++) {
            log.info("发送第 {}/{} 封邮件...", i, totalCount);
            
            String code = RandomUtil.randomNumbers(6);
            EmailSender.EmailResult result = EmailSender.sendVerificationCode(config, "<EMAIL>", code);
            
            if (result.isSuccess()) {
                successCount++;
                log.info("第 {} 封邮件发送成功，验证码: {}", i, code);
            } else {
                failCount++;
                log.error("第 {} 封邮件发送失败: {}", i, result.getMessage());
            }
            
            // 间隔1秒
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("批量测试完成！成功: {}, 失败: {}, 总计: {}", successCount, failCount, totalCount);
    }
} 