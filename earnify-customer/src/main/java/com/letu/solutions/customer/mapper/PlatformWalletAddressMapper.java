package com.letu.solutions.customer.mapper;

import com.letu.solutions.model.entity.cms.PlatformWalletAddress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 平台钱包地址表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Mapper
public interface PlatformWalletAddressMapper extends BaseMapper<PlatformWalletAddress> {

    /**
     * 根据协议网络查询钱包地址列表
     * @param network 协议网络
     * @return 钱包地址列表
     */
    @Select("""
            SELECT address
            FROM platform_wallet_address
            WHERE network = #{network}
            AND address IS NOT NULL
            AND address != ''
            ORDER BY id ASC
            """)
    List<String> getAddressesByNetwork(@Param("network") String network);
}
