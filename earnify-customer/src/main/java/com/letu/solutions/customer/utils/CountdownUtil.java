package com.letu.solutions.customer.utils;

import com.letu.solutions.model.entity.cms.UserTask;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import com.letu.solutions.model.response.customer.TaskPageRes;
import com.letu.solutions.customer.service.SysParamService;
import com.letu.solutions.core.constant.SysParamConstants;

import java.util.concurrent.TimeUnit;

/**
 * 倒计时计算工具类
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
public class CountdownUtil {

    /**
     * 计算用户任务的所有倒计时
     * 
     * @param userTask 用户任务
     * @param res 任务详情响应对象
     * @param sysParamService 系统参数服务
     */
    public static void calculateAllCountdowns(UserTask userTask, TaskPageRes res, SysParamService sysParamService) {
        long currentTime = System.currentTimeMillis();
        
        // 计算任务到期倒计时（秒）
        if (userTask.getTime() != null) {
            long expireTime = userTask.getTime().getTime();
            long diffSeconds = (expireTime - currentTime) / 1000;
            res.setExpireCountdownSeconds(Math.max(diffSeconds, 0));
        } else {
            res.setExpireCountdownSeconds(0L);
        }
        
        // 审核倒计时逻辑 - 当任务状态为pendingApproval且examineTime不为null时计算
        if (UserTaskStatusEnum.pendingApproval.equals(userTask.getState()) && userTask.getExamineTime() != null) {
            Integer auditDays = sysParamService.getIntValueByKey(SysParamConstants.AUDIT_COUNTDOWN_DAYS, 3);
            long auditEnd = userTask.getExamineTime().getTime() + TimeUnit.DAYS.toMillis(auditDays);
            long diffSeconds = (auditEnd - currentTime) / 1000;
            res.setAuditCountdown(Math.max(diffSeconds, 0));
        }
        
        // 申诉倒计时逻辑 - 当任务状态为appealInProgress且appealTime不为null时计算
        if (UserTaskStatusEnum.appealInProgress.equals(userTask.getState()) && userTask.getAppealTime() != null) {
            Integer appealDays = sysParamService.getIntValueByKey(SysParamConstants.APPEAL_COUNTDOWN_DAYS, 3);
            long appealEnd = userTask.getAppealTime().getTime() + TimeUnit.DAYS.toMillis(appealDays);
            long diffSeconds = (appealEnd - currentTime) / 1000;
            res.setAppealCountdown(Math.max(diffSeconds, 0));
        }
    }
} 