package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.customer.mapper.UserWalletMapper;
import com.letu.solutions.customer.service.UserWalletService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.entity.cms.UserWallet;
import com.letu.solutions.model.request.user.UserWalletBindRequest;
import com.letu.solutions.model.response.user.UserWalletResponse;
import com.letu.solutions.model.enums.cms.WalletTypeEnum;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.utils.LanguageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户钱包绑定服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserWalletServiceImpl extends ServiceImpl<UserWalletMapper, UserWallet> implements UserWalletService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bindWallet(UserWalletBindRequest request, ExtendData extendData) {
        log.info("用户{}绑定钱包，类型：{}，地址：{}", extendData.getUserId(), request.getWalletType().getDesc(), request.getWalletAddress());

        // 查询用户是否已有该地址的绑定记录
        UserWallet existingWallet = baseMapper.selectOne(
                new LambdaQueryWrapper<UserWallet>()
                        .eq(UserWallet::getUserId, extendData.getUserId())
                        .eq(UserWallet::getWalletType, request.getWalletType())
                        .eq(UserWallet::getWalletAddress, request.getWalletAddress())
        );

        if (existingWallet != null) {
            // 如果已经绑定，返回已绑定提示
            log.warn("用户{}尝试绑定已存在的钱包地址{}", extendData.getUserId(), request.getWalletAddress());
            return LanguageUtil.trans(I18nConstants.WALLET_ALREADY_BOUND);
        }

        // 如果设为默认，先取消其他默认钱包
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            baseMapper.update(null,
                    new LambdaUpdateWrapper<UserWallet>()
                            .eq(UserWallet::getUserId, extendData.getUserId())
                            .set(UserWallet::getIsDefault, 0)
            );
        }

        // 创建新记录
        UserWallet newWallet = new UserWallet();
        newWallet.setUserId(extendData.getUserId());
        newWallet.setWalletType(request.getWalletType());
        newWallet.setWalletAddress(request.getWalletAddress());
        newWallet.setIsDefault(Boolean.TRUE.equals(request.getIsDefault()) ? 1 : 0);
        newWallet.setCreateTime(new Date());
        newWallet.setUpdateTime(new Date());
        
        boolean result = baseMapper.insert(newWallet) > 0;
        if (result) {
            log.info("用户{}新增钱包{}绑定成功", extendData.getUserId(), request.getWalletType().getDesc());
            return null; // 成功返回null
        } else {
            log.error("用户{}新增钱包{}绑定失败", extendData.getUserId(), request.getWalletType().getDesc());
            return LanguageUtil.trans(I18nConstants.WALLET_BIND_FAILED);
        }
    }

    @Override
    public UserWalletResponse getUserWallet(ExtendData extendData) {
        log.info("查询用户{}的钱包绑定信息", extendData.getUserId());

        // 查询用户的所有钱包绑定记录
        List<UserWallet> userWallets = baseMapper.selectList(
                new LambdaQueryWrapper<UserWallet>()
                        .eq(UserWallet::getUserId, extendData.getUserId())
                        .orderByDesc(UserWallet::getIsDefault)
                        .orderByDesc(UserWallet::getCreateTime)
        );

        UserWalletResponse response = new UserWalletResponse();
        response.setUserId(extendData.getUserId());

        // 转换为响应格式
        List<UserWalletResponse.WalletBinding> walletBindings = userWallets.stream()
                .map(wallet -> {
                    UserWalletResponse.WalletBinding binding = new UserWalletResponse.WalletBinding();
                    binding.setId(wallet.getId());
                    binding.setWalletType(wallet.getWalletType().getCode());
                    binding.setWalletTypeDesc(wallet.getWalletType().getDesc());
                    binding.setWalletAddress(wallet.getWalletAddress());
                    binding.setIsDefault(wallet.getIsDefault() == 1);
                    binding.setBindTime(wallet.getCreateTime());
                    return binding;
                })
                .collect(Collectors.toList());

        response.setWalletBindings(walletBindings);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String setDefaultWallet(Long walletId, ExtendData extendData) {
        log.info("用户{}设置默认钱包，钱包ID：{}", extendData.getUserId(), walletId);

        // 查询钱包是否存在且属于当前用户
        UserWallet wallet = baseMapper.selectOne(
                new LambdaQueryWrapper<UserWallet>()
                        .eq(UserWallet::getId, walletId)
                        .eq(UserWallet::getUserId, extendData.getUserId())
        );

        if (wallet == null) {
            log.warn("用户{}尝试设置不存在的钱包{}为默认", extendData.getUserId(), walletId);
            return LanguageUtil.trans(I18nConstants.WALLET_NOT_FOUND);
        }

        // 先取消其他默认钱包
        baseMapper.update(null,
                new LambdaUpdateWrapper<UserWallet>()
                        .eq(UserWallet::getUserId, extendData.getUserId())
                        .set(UserWallet::getIsDefault, 0)
        );

        // 设置当前钱包为默认
        wallet.setIsDefault(1);
        wallet.setUpdateTime(new Date());
        
        boolean result = baseMapper.updateById(wallet) > 0;
        if (result) {
            log.info("用户{}设置钱包{}为默认成功", extendData.getUserId(), walletId);
            return null;
        } else {
            log.error("用户{}设置钱包{}为默认失败", extendData.getUserId(), walletId);
            return LanguageUtil.trans(I18nConstants.WALLET_SET_DEFAULT_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteWallet(Long walletId, ExtendData extendData) {
        log.info("用户{}删除钱包，钱包ID：{}", extendData.getUserId(), walletId);

        // 查询钱包是否存在且属于当前用户
        UserWallet wallet = baseMapper.selectOne(
                new LambdaQueryWrapper<UserWallet>()
                        .eq(UserWallet::getId, walletId)
                        .eq(UserWallet::getUserId, extendData.getUserId())
        );

        if (wallet == null) {
            log.warn("用户{}尝试删除不存在的钱包{}", extendData.getUserId(), walletId);
            return LanguageUtil.trans(I18nConstants.WALLET_NOT_FOUND);
        }

        // 删除钱包
        boolean result = baseMapper.deleteById(walletId) > 0;
        if (result) {
            log.info("用户{}删除钱包{}成功", extendData.getUserId(), walletId);
            return null;
        } else {
            log.error("用户{}删除钱包{}失败", extendData.getUserId(), walletId);
            return LanguageUtil.trans(I18nConstants.WALLET_DELETE_FAILED);
        }
    }
} 