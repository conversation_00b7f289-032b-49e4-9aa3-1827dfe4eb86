package com.letu.solutions.customer.service;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.request.EmptyRequest;
import com.letu.solutions.model.request.login.CertRegisterRequest;
import com.letu.solutions.model.request.login.LoginRequest;
import com.letu.solutions.model.request.login.RegisterRequest;
import com.letu.solutions.model.response.user.LoginResponse;
import com.letu.solutions.share.model.enums.MessageEnum;

/**
 * 登录/注册
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface LoginService {
    void sms(String phone, MessageEnum businessType, ExtendData extendData);

    void checkSms(String phone, String code, MessageEnum messageEnum, ExtendData extendData);

    void checkPwd(String phone, String pwd, ExtendData extendData);

    LoginResponse login(String phone, ExtendData extendData);

    void refreshToken(ExtendData extendData);

    Long register(RegisterRequest request, ExtendData extendData);

    Long register(EmptyRequest request, ExtendData extendData);

    LoginResponse login(Long userId, ExtendData extendData);

    Long checkPhone(String phone);

    Boolean bindPhone(LoginRequest request, ExtendData extendData);

    void certRegister(CertRegisterRequest request, ExtendData extendData) throws Exception;

    void loginOut(ExtendData extendData);
}
