package com.letu.solutions.customer.upload;

import com.letu.solutions.core.model.R;
import com.letu.solutions.model.enums.OssEnum;
import com.letu.solutions.util.util.upload.UploadUtil;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;



/**
 * 文件上传相关
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@Validated
public class UploadController {
    private final UploadUtil uploadUtil;


    /**
     * 图片上传
     *
     * @param file 文件
     */
    @PostMapping("/upload/image")
    public R<String> imageUpload(@NotNull(message = "文件不能为空") @RequestParam(name = "file") MultipartFile file,
                                 @RequestParam(value = "ossEnum", required = false, defaultValue = "BUSINESS") OssEnum ossEnum) throws Exception {
        return R.success(uploadUtil.uploadImage(file, ossEnum.getPath()));
    }


}
