package com.letu.solutions.customer.service;

import com.letu.solutions.model.customer.response.FinanceConfigDetailRes;

import java.util.List;

/**
 * 财务配置服务类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface FinanceConfigService {

    /**
     * 根据协议网络查询财务配置详情
     * @param network 协议网络
     * @return 财务配置详情
     */
    FinanceConfigDetailRes selectByNetwork(String network);

    /**
     * 根据协议网络查询钱包地址列表
     * @param network 协议网络
     * @return 钱包地址列表
     */
    List<String> getAddressesByNetwork(String network);
}
