package com.letu.solutions.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.UserTask;
import com.letu.solutions.model.response.customer.UserTaskPageRes;
import com.letu.solutions.model.response.user.CompletedUserTaskResponse;
import com.letu.solutions.model.enums.cms.UserTaskStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.letu.solutions.model.request.task.UserTaskListQueryRequest;
import com.letu.solutions.model.request.user.CompletedUserTaskQueryRequest;

@Mapper
public interface UserTaskMapper extends BaseMapper<UserTask> {
    Page<UserTaskPageRes> selectUserTaskPageResPage(Page<UserTaskPageRes> page, @Param("request") UserTaskListQueryRequest request, @Param("userId") Long userId);
    
    /**
     * 分页查询已完成且发布者为甲方账户的用户任务
     */
    Page<CompletedUserTaskResponse> selectCompletedUserTaskPage(
            @Param("page") Page page,
            @Param("req") CompletedUserTaskQueryRequest req);
} 