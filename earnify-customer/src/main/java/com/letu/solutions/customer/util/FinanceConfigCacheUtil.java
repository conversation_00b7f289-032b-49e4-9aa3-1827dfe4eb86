package com.letu.solutions.customer.util;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import com.letu.solutions.model.customer.response.FinanceConfigDetailRes;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 财务配置缓存工具类（客户端版本）
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Component
public class FinanceConfigCacheUtil {
    
    private static final String KEY_PREFIX = "customer:finance:config:";
    private static final long CACHE_EXPIRE_TIME = 30; // 缓存30分钟

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设置财务配置缓存
     * @param network 协议网络
     * @param config 财务配置详情
     */
    public void setConfig(String network, FinanceConfigDetailRes config) {
        String key = KEY_PREFIX + network;
        redisTemplate.opsForValue().set(key, config, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
    }

    /**
     * 获取财务配置缓存
     * @param network 协议网络
     * @return 财务配置详情
     */
    public FinanceConfigDetailRes getConfig(String network) {
        String key = KEY_PREFIX + network;
        Object obj = redisTemplate.opsForValue().get(key);
        if (obj instanceof FinanceConfigDetailRes) {
            return (FinanceConfigDetailRes) obj;
        }
        return null;
    }

    /**
     * 删除财务配置缓存
     * @param network 协议网络
     */
    public void deleteConfig(String network) {
        String key = KEY_PREFIX + network;
        redisTemplate.delete(key);
    }
}
