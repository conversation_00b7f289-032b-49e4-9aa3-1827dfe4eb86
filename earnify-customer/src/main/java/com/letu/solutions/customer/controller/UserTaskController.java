package com.letu.solutions.customer.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.customer.service.UserTaskService;
import com.letu.solutions.model.request.task.UserTaskListQueryRequest;
import com.letu.solutions.core.model.R;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.request.user.CompletedUserTaskQueryRequest;
import com.letu.solutions.model.response.customer.UserTaskPageRes;
import com.letu.solutions.model.response.user.CompletedUserTaskResponse;
import com.letu.solutions.model.response.task.TaskReceiveResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.letu.solutions.customer.mapper.TaskMapper;
import com.letu.solutions.customer.mapper.UserTaskMapper;
import com.letu.solutions.model.request.task.FinishStepRequest;
import org.springframework.validation.annotation.Validated;

/**
 * 我的任务相关接口
 * 提供我的任务分页查询、详情查询等功能
 */
@RestController
@RequestMapping("/customer/userTask")
public class UserTaskController {
    @Autowired
    private UserTaskService userTaskService;

    /**
     * 我的任务分页查询
     */
    @GetMapping("/page")
    public R<Page<UserTaskPageRes>> page(UserTaskListQueryRequest queryRequest, @RequestHeader ExtendData extendData) {
        return R.success(userTaskService.page(queryRequest.getPage(), queryRequest, extendData));
    }

    /**
     * 我的任务详情
     */
    @GetMapping("/detail")
    public R<UserTaskPageRes> detail(@Validated @RequestParam("userTaskId") Long userTaskId, @RequestHeader ExtendData extendData) {
        return R.success(userTaskService.detail(userTaskId, extendData));
    }

    /**
     * 任务领取接口
     */
    @PostMapping("/receive")
    public R<TaskReceiveResponse> receive(@Validated @RequestParam Long taskId, @RequestHeader ExtendData extendData) {
        return userTaskService.receive(taskId, extendData);
    }

    /**
     * 申诉接口
     */
    @PostMapping("/appeal")
    public R<String> appeal(@RequestParam Long userTaskId, @RequestHeader ExtendData extendData) {
        return userTaskService.appeal(userTaskId, extendData);
    }

    /**
     * 提交任务接口
     */
    @PostMapping("/submit")
    public R<String> submit(@RequestParam Long userTaskId, @RequestHeader ExtendData extendData) {
        return userTaskService.submit(userTaskId, extendData);
    }

    /**
     * 完成步骤接口
     */
    @PostMapping("/finishStep")
    public R<String> finishStep(@Validated @RequestBody FinishStepRequest request, @RequestHeader ExtendData extendData) {
        return userTaskService.finishStep(request, extendData);
    }

    /**
     * 我的奖励
     *
     * @param request 查询请求参数
     * @param extendData 扩展数据
     * @return 分页结果
     */
    @GetMapping("/completedPage")
    public R<Page<CompletedUserTaskResponse>> getCompletedUserTaskPage(
            CompletedUserTaskQueryRequest request,
            @RequestHeader ExtendData extendData) {
        Page<CompletedUserTaskResponse> result = userTaskService.getCompletedUserTaskPage(request, extendData);
        return R.success(result);

    }
} 