package com.letu.solutions.customer.service;

import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.letu.solutions.core.configuration.MessageConfiguration;
import com.letu.solutions.core.configuration.WhiteConfiguration;
import com.letu.solutions.core.constant.CacheConstant;
import com.letu.solutions.core.constant.Constants;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.utils.LanguageUtil;
import com.letu.solutions.core.utils.LockUtil;
import com.letu.solutions.customer.utils.CustomerUtil;
import com.letu.solutions.customer.utils.EmailSender;
import com.letu.solutions.model.request.login.EmailLoginRequest;
import com.letu.solutions.model.response.login.LoginRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.Properties;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {

    private final UserService userService;

    private final WhiteConfiguration whiteConfiguration;

    private final StringRedisTemplate stringRedisTemplate;

    private final LockUtil lockUtil;

    private final MessageConfiguration mailProps;
    @Autowired
    @Qualifier("asyncPool")
    private Executor asyncPool;
    /**
     * 邮件登录方法
     *
     * @param request    登录请求对象，包含用户登录所需的信息，如邮箱和设备ID
     * @param extendData 扩展数据对象，包含一些额外的头信息，用于处理登录过程
     * @return 返回登录响应对象，包含用户信息和登录令牌
     */
    public LoginRes emailLogin(EmailLoginRequest request, ExtendData extendData) {

        // 查询数据库中是否存在该邮箱的用户
        Long userId = userService.getUserByEmail(request, extendData);

        return userService.getLoginRes(userId, extendData);
    }

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱地址
     */
    public void emailCode(String email, String ipAddress) {
        log.info("发送邮箱验证码，邮箱: {}, IP地址: {}", email, ipAddress);
        String beginIpStr = Ipv4Util.getBeginIpStr(ipAddress, 16);
//        boolean ipWhite = whiteConfiguration.getWhiteIps().contains(ipAddress) || whiteConfiguration.getWhiteIps().contains(beginIpStr);
//        boolean emailwhite = whiteConfiguration.getWhiteMail().contains(email);

//        boolean white = (ipWhite || emailwhite);
        boolean white = false;
        // 根据配置决定是否进行登录验证，若不验证则使用固定验证码
        String code = (!whiteConfiguration.isLoginCheck()) || white ? Constants.UNIVERSAL_CODE : RandomUtil.randomNumbers(6);

        // 将验证码缓存至Redis，有效期为30分钟
        stringRedisTemplate.opsForValue().set(String.format(CacheConstant.emailCode, email), code, 30, TimeUnit.MINUTES);

        if (!white) {
            try {
                lockUtil.checkLock(CacheConstant.EMAIL_CODE_CER, ipAddress);
                lockUtil.checkLock(CacheConstant.EMAIL_CODE_CER, email);
                // 发送验证码邮件
                this.sendCode(email, code);
                log.info("邮箱验证码发送成功，邮箱: {}", email);
            } catch (Exception e) {
                log.error("邮箱验证码发送失败，邮箱: {}, 错误: {}", email, e.getMessage());
                throw new ThrowException(LanguageUtil.trans(I18nConstants.EMAIL_FREQUENCY_LIMIT));
            }
        }
    }

    /**
     * 检查用户输入的邮箱验证码是否正确
     *
     * @param email     用户的邮箱地址，用于查找对应的验证码
     * @param emailCode 用户输入的验证码
     * @throws ThrowException 如果用户输入的验证码与缓存中的验证码不匹配，则抛出异常
     */
    public void checkEmailCode(String email, String emailCode) {
        log.info("检查邮箱验证码，邮箱: {}", email);
        // 根据邮箱地址生成Redis缓存的键值
        String redisKey = String.format(String.format(CacheConstant.emailCode, email));
        // 从缓存中获取验证码
        String cacheCode = stringRedisTemplate.opsForValue().get(redisKey);
        
        // 检查验证码是否存在
        if (StrUtil.isEmpty(cacheCode)) {
            log.warn("邮箱验证码不存在或已过期，邮箱: {}", email);
            throw new ThrowException(LanguageUtil.trans(I18nConstants.EMAIL_CODE_EXPIRED));
        }
        
        // 比较用户输入的验证码和缓存中的验证码是否一致
        if (!StrUtil.equals(emailCode, cacheCode)) {
            log.warn("邮箱验证码错误，邮箱: {}, 输入验证码: {}", email, emailCode);
            throw new ThrowException(LanguageUtil.trans(I18nConstants.EMAIL_CODE_ERROR));
        }
        
        // 验证码匹配后，删除缓存中的验证码
        stringRedisTemplate.delete(redisKey);
        log.info("邮箱验证码验证成功，邮箱: {}", email);
    }


    /**
     * 发送验证码
     *
     * @param toMail 邮箱地址
     * @param code 验证码
     */
    private void sendCode(String toMail, String code) {
        try {
            // 获取邮件配置
            String emailHost = mailProps.getEmailHost();
            String emailUser = mailProps.getEmailUser();
            String emailPass = mailProps.getEmailPass();
            String emailFrom = mailProps.getEmailFrom();
            Integer emailPort = mailProps.getEmailPort();
            
            // 验证配置是否完整
            if (StrUtil.isEmpty(emailHost) || StrUtil.isEmpty(emailUser) || 
                StrUtil.isEmpty(emailPass) || StrUtil.isEmpty(emailFrom)) {
                log.error("邮件配置不完整，无法发送邮件");
                throw new ThrowException(LanguageUtil.trans(I18nConstants.EMAIL_CONFIG_INCOMPLETE));
            }
            
            // 使用默认端口465如果配置中没有指定
            if (emailPort == null) {
                emailPort = 465;
            }
            
            // 创建邮件配置
            EmailSender.EmailConfig config = new EmailSender.EmailConfig(
                emailHost, emailPort, emailUser, emailPass, emailFrom
            );
            config.setDebug(true); // 开启调试模式
            
            // 使用EmailSender发送验证码邮件
            EmailSender.EmailResult result = EmailSender.sendVerificationCode(config, toMail, code);
            
            if (result.isSuccess()) {
                log.info("邮件发送成功！收件人: {}", toMail);
            } else {
                log.error("邮件发送失败！收件人: {}, 错误: {}", toMail, result.getMessage());
                if (result.getException() != null) {
                    log.error("异常详情: ", result.getException());
                }
                throw new ThrowException(LanguageUtil.trans(I18nConstants.EMAIL_SEND_FAILED));
            }
            
        } catch (ThrowException e) {
            throw e; // 重新抛出国际化异常
        } catch (Exception e) {
            log.error("邮件发送配置错误: {}", e.getMessage(), e);
            throw new ThrowException(LanguageUtil.trans(I18nConstants.EMAIL_SEND_FAILED));
        }
    }
}
