package com.letu.solutions.customer.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.customer.mapper.InviteRecordMapper;
import com.letu.solutions.customer.mapper.UserMapper;
import com.letu.solutions.customer.service.InviteService;
import com.letu.solutions.customer.service.SysParamService;
import com.letu.solutions.model.entity.cms.InviteRecord;
import com.letu.solutions.model.entity.user.User;
import com.letu.solutions.model.enums.cms.InviteStatusEnum;
import com.letu.solutions.model.enums.cms.RewardStatusEnum;
import com.letu.solutions.model.request.invite.InviteListQueryRequest;
import com.letu.solutions.model.response.customer.InviteRecordRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 邀请服务实现类
 * Invite Service Implementation
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class InviteServiceImpl extends ServiceImpl<InviteRecordMapper, InviteRecord> implements InviteService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SysParamService sysParamService;



    /**
     * 分页查询邀请记录
     * Pagination query invite records
     *
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @param extendData 用户扩展信息
     * @return 分页结果
     */
    @Override
    public Page<InviteRecordRes> page(Page<InviteRecordRes> page,
                                                    InviteListQueryRequest queryRequest, 
                                                    ExtendData extendData) {
        log.info("[InviteServiceImpl] 用户{}查询邀请记录, page={}, size={}", 
            extendData.getUserId(), page.getCurrent(), page.getSize());

        Page<InviteRecordRes> result = baseMapper.selectInviteRecordPage(page, queryRequest, extendData.getUserId());
        
        // 处理邀请方式显示逻辑
        if (result.getRecords() != null) {
            for (InviteRecordRes record : result.getRecords()) {
                // 将邀请方式显示为"邀请吗"
                record.setInviteMethod("邀请码");
            }
        }
        
        log.info("[InviteServiceImpl] 用户{}查询邀请记录成功, 总数={}", extendData.getUserId(), result.getTotal());
        return result;
    }


    /**
     * 处理用户注册时的邀请逻辑
     * Handle invite logic when user registers
     *
     * @param inviteCode 邀请码
     * @param inviteeId 被邀请人ID
     * @return 是否处理成功
     */
    @Override
    public boolean handleUserRegistration(String inviteCode, Long inviteeId) {
        if (StrUtil.isBlank(inviteCode) || inviteeId == null) {
            log.warn("[InviteServiceImpl] 处理用户注册邀请逻辑参数无效, inviteCode={}, inviteeId={}", inviteCode, inviteeId);
            return false;
        }

        log.info("[InviteServiceImpl] 处理用户注册邀请逻辑, inviteCode={}, inviteeId={}", inviteCode, inviteeId);

        try {
            // 1. 根据邀请码查找邀请人
            User inviter = userMapper.selectOne(
                new LambdaQueryWrapper<User>()
                    .eq(User::getRecommendCode, inviteCode)
                    .eq(User::getDel, 0)
                    .eq(User::getEnable, 1)
            );
            
            if (inviter == null) {
                log.warn("[InviteServiceImpl] 邀请码{}不存在或对应的用户已被禁用", inviteCode);
                return false;
            }
            
            Long inviterId = inviter.getId();

            // 3. 检查被邀请人是否已被其他邀请记录使用
            InviteRecord existingInvite = baseMapper.selectByInviteeId(inviteeId);
            if (existingInvite != null) {
                log.warn("[InviteServiceImpl] 用户{}已被其他邀请记录使用, existingInviteId={}", inviteeId, existingInvite.getId());
                return false;
            }

            // 4. 检查是否已经存在相同的邀请记录（邀请人和被邀请人）
            InviteRecord duplicateInvite = this.lambdaQuery()
                .eq(InviteRecord::getInviterId, inviterId)
                .eq(InviteRecord::getInviteeId, inviteeId)
                .eq(InviteRecord::getDelFlag, 0)
                .one();
            if (duplicateInvite != null) {
                log.warn("[InviteServiceImpl] 邀请人{}和被邀请人{}之间已存在邀请记录", inviterId, inviteeId);
                return false;
            }

            // 5. 从系统参数获取邀请有效期天数，默认为7天
            Integer inviteValidDays = sysParamService.getIntValueByKey("invite_valid_days", 7);
            Date expireTime = new Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(inviteValidDays));

            // 6. 从系统参数获取邀请奖励金额，默认为10.00
            BigDecimal rewardAmount = new BigDecimal(sysParamService.getValueByKey("invite_reward_amount", "0.00"));

            // 7. 计算顶级邀请人ID（root_inviter_id）
            Long rootInviterId = calculateRootInviterId(inviterId);

            // 8. 创建邀请记录
            InviteRecord inviteRecord = new InviteRecord();
            inviteRecord.setInviterId(inviterId);
            inviteRecord.setRootInviterId(rootInviterId);
            inviteRecord.setInviteeId(inviteeId);
            inviteRecord.setInviteCode(inviteCode);
            inviteRecord.setStatus(InviteStatusEnum.REGISTERED.getCode());
            inviteRecord.setRewardAmount(rewardAmount);
            inviteRecord.setRewardStatus(RewardStatusEnum.GRANTED.getCode());
            inviteRecord.setRegisterTime(new Date());
            inviteRecord.setExpireTime(expireTime);
            inviteRecord.setRemark("用户更新基础信息时填写邀请码");
            inviteRecord.setDelFlag(0);
            inviteRecord.setCreateTime(new Date());
            inviteRecord.setUpdateTime(new Date());

            boolean success = this.save(inviteRecord);
            if (!success) {
                log.error("[InviteServiceImpl] 保存邀请记录失败, inviterId={}, inviteeId={}", inviterId, inviteeId);
                return false;
            }

            log.info("[InviteServiceImpl] 处理用户注册邀请逻辑成功, inviteRecordId={}, inviterId={}, inviteeId={}", 
                inviteRecord.getId(), inviterId, inviteeId);
            return true;

        } catch (Exception e) {
            log.error("[InviteServiceImpl] 处理用户注册邀请逻辑异常, inviteCode={}, inviteeId={}, error={}", 
                inviteCode, inviteeId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 计算顶级邀请人ID（一级邀请人）
     * 通过递归查找，直到找到没有上级邀请人的用户
     *
     * @param inviterId 当前邀请人ID
     * @return 顶级邀请人ID
     */
    private Long calculateRootInviterId(Long inviterId) {
        if (inviterId == null) {
            return null;
        }

        // 查找当前邀请人的邀请记录
        InviteRecord inviterRecord = this.lambdaQuery()
            .eq(InviteRecord::getInviteeId, inviterId)
            .eq(InviteRecord::getDelFlag, 0)
            .one();

        if (inviterRecord == null) {
            // 如果没有找到邀请记录，说明当前用户就是顶级邀请人
            log.debug("[InviteServiceImpl] 用户{}是顶级邀请人", inviterId);
            return inviterId;
        }

        // 如果找到了邀请记录，继续向上查找
        log.debug("[InviteServiceImpl] 用户{}的上级邀请人是{}", inviterId, inviterRecord.getInviterId());
        return calculateRootInviterId(inviterRecord.getInviterId());
    }
}