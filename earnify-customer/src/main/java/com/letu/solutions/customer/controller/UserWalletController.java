package com.letu.solutions.customer.controller;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.UserWalletService;
import com.letu.solutions.model.request.user.UserWalletBindRequest;
import com.letu.solutions.model.response.user.UserWalletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户钱包相关
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class UserWalletController {

    private final UserWalletService userWalletService;

    /**
     * 绑定钱包
     */
    @PostMapping("/user/bindWallet")
    public R<Boolean> bindWallet(@Validated @RequestBody UserWalletBindRequest request, @RequestHeader ExtendData extendData) {
        log.info("用户{}请求绑定钱包，类型：{}", extendData.getUserId(), request.getWalletType().getDesc());
        
        String errorMessage = userWalletService.bindWallet(request, extendData);
        if (errorMessage == null) {
            return R.success(true);
        } else {
            return R.fail(errorMessage);
        }
    }

    /**
     * 查询用户钱包绑定信息
     */
    @GetMapping("/user/getWallet")
    public R<UserWalletResponse> getUserWallet(@RequestHeader ExtendData extendData) {
        log.info("用户{}请求查询钱包绑定信息", extendData.getUserId());
        
        UserWalletResponse response = userWalletService.getUserWallet(extendData);
        return R.success(response);
    }

    /**
     * 设置默认钱包
     */
    @PostMapping("/user/setDefaultWallet")
    public R<Boolean> setDefaultWallet(@RequestParam Long walletId, @RequestHeader ExtendData extendData) {
        log.info("用户{}请求设置默认钱包，钱包ID：{}", extendData.getUserId(), walletId);
        
        String errorMessage = userWalletService.setDefaultWallet(walletId, extendData);
        if (errorMessage == null) {
            return R.success(true);
        } else {
            return R.fail(errorMessage);
        }
    }

    /**
     * 删除钱包绑定
     */
    @GetMapping("/user/deleteWallet")
    public R<Boolean> deleteWallet(@RequestParam Long walletId, @RequestHeader ExtendData extendData) {
        log.info("用户{}请求删除钱包，钱包ID：{}", extendData.getUserId(), walletId);
        
        String errorMessage = userWalletService.deleteWallet(walletId, extendData);
        if (errorMessage == null) {
            return R.success(true);
        } else {
            return R.fail(errorMessage);
        }
    }
} 