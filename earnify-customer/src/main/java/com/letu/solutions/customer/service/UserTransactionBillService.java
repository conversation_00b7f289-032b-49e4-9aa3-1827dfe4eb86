package com.letu.solutions.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.customer.request.UserAssetListReq;
import com.letu.solutions.model.customer.response.UserAssetPageRes;

/**
 * 用户账户流水表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface UserTransactionBillService {

    /**
     * 分页展示当前用户资产
     * @param request 查询请求参数
     * @param userId 当前用户ID
     * @return 用户资产分页数据
     */
    Page<UserAssetPageRes> getUserAssetPage(UserAssetListReq request, Long userId);
}
