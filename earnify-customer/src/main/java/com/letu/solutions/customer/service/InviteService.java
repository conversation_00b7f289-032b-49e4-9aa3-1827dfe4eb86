package com.letu.solutions.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.request.invite.InviteListQueryRequest;
import com.letu.solutions.model.response.customer.InviteRecordRes;

/**
 * 邀请服务接口
 * Invite Service Interface
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface InviteService {



    /**
     * 分页查询邀请记录
     * Pagination query invite records
     *
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @param extendData 用户扩展信息
     * @return 分页结果
     */
    Page<InviteRecordRes> page(Page<InviteRecordRes> page,
                                             InviteListQueryRequest queryRequest, 
                                             ExtendData extendData);


    /**
     * 处理用户注册时的邀请逻辑
     * Handle invite logic when user registers
     *
     * @param inviteCode 邀请码
     * @param inviteeId 被邀请人ID
     * @return 是否处理成功
     */
    boolean handleUserRegistration(String inviteCode, Long inviteeId);


} 