package com.letu.solutions.customer.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.InviteService;
import com.letu.solutions.model.request.invite.InviteListQueryRequest;
import com.letu.solutions.model.response.customer.InviteRecordRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 邀请相关
 * Invite Controller
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@RestController
@RequestMapping("/invite")
public class InviteController {
    @Autowired
    private  InviteService inviteService;
    /**
     * 分页查询邀请记录
     * Pagination query invite records
     *
     * @param queryRequest 查询条件
     * @param extendData 用户扩展信息
     * @return 分页结果
     */
    @GetMapping("/page")
    public R<Page<InviteRecordRes>> page(InviteListQueryRequest queryRequest,
                                                       @RequestHeader ExtendData extendData) {
        Page<InviteRecordRes> result = inviteService.page(queryRequest.getPage(), queryRequest, extendData);
        return R.success(result);
    }
} 