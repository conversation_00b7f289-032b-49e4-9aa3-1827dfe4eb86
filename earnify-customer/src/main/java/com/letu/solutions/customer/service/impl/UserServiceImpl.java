package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.core.configuration.RegisterConfiguration;
import com.letu.solutions.core.enums.BusinessTypeEnum;
import com.letu.solutions.core.enums.OsEnum;
import com.letu.solutions.core.enums.OsTypeEnum;
import com.letu.solutions.core.model.Authentication;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.UserInfo;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.core.utils.LanguageUtil;
import com.letu.solutions.core.utils.LockUtil;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.utils.NickNameUtils;
import com.letu.solutions.customer.mapper.InviteRecordMapper;
import com.letu.solutions.customer.mapper.UserMapper;
import com.letu.solutions.customer.service.InviteService;
import com.letu.solutions.customer.service.LoginService;
import com.letu.solutions.customer.service.UserService;
import com.letu.solutions.customer.utils.CustomerUtil;
import com.letu.solutions.dubbo.middle.customer.UserFacade;
import com.letu.solutions.dubbo.middle.customer.UtilFacade;
import com.letu.solutions.dubbo.earnify.util.ExtendCovert;
import com.letu.solutions.model.entity.cms.InviteRecord;
import com.letu.solutions.model.entity.user.User;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.model.request.login.BaseLoginRequest;
import com.letu.solutions.model.request.login.EmailLoginRequest;
import com.letu.solutions.model.request.login.GoogleLoginRequest;
import com.letu.solutions.model.request.login.RegisterRequest;
import com.letu.solutions.model.request.user.PwdUpdateReq;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.model.request.user.UserUpdateHeadReq;
import com.letu.solutions.model.request.user.UserUpdateInfoReq;
import com.letu.solutions.model.request.user.UserUpdateNameReq;
import com.letu.solutions.model.response.login.LoginRes;
import com.letu.solutions.model.response.login.LoginUserRes;
import com.letu.solutions.model.response.login.UserResponse;
import com.letu.solutions.model.response.upload.UploadResponse;
import com.letu.solutions.model.response.user.UserUpdateResult;
import com.letu.solutions.share.model.enums.ClientEnum;
import com.letu.solutions.share.model.enums.user.UserStatusEnum;
import com.letu.solutions.share.model.request.user.*;
import com.letu.solutions.util.util.CodeUtil;
import com.letu.solutions.util.util.TimeUtil;
import com.letu.solutions.util.util.TokenUtil;
import com.letu.solutions.util.util.upload.UploadUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.lang.Assert.isFalse;

/**
 * 用户 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    private final TransactionalManage transactionalManage;

    private final TransactionTemplate transactionTemplate;

    private final TokenUtil tokenUtil;

    private final LockUtil lockUtil;

    private final RegisterConfiguration registerConfiguration;
    @Resource
    @Lazy
    private LoginService loginService;

    @DubboReference(check = false)
    private UtilFacade utilFacade;

    @DubboReference(check = false)
    private UserFacade userFacade;

    private final String nameTemplate = "MAX%s";

    private final UploadUtil uploadUtil;

    private final RedisTemplate<String, String> stringRedisTemplate;
    @Resource
    private UserService userService;

    @Resource
    private InviteService inviteService;
    
    @Resource
    private InviteRecordMapper inviteRecordMapper;

    @Override
    public Long createUser(RegisterRequest request, ExtendData extendData) {
        String salt = RandomUtil.randomString(4);
        User user = User.builder()
                .userPhone(request.getPhone())
                .salt(salt)
                .nickName(NickNameUtils.getNickName())
                .deviceId(extendData.getHeaderDto().getDeviceId())
                .userImage(registerConfiguration.getUserImgUrls().get(0))
                .pwd(SecureUtil.md5(request.getPwd() + salt))
                .day(TimeUtil.today())
                .accountRole(AccountTypeEnum.provider)
                .email(request.getEmail())
                .enable(1)
                .status(UserStatusEnum.common)
                .build();
        log.info("createUser saveUser:{} ", JSONObject.toJSONString(user));
        Collections.shuffle(registerConfiguration.getUserImgUrls());
        
         transactionalManage.execute(() -> {
            boolean res = baseMapper.insert(user) > 0;
            Assert.isTrue(res, "新增用户失败，请稍后重试");
            
            // 生成邀请码并更新用户记录
            String inviteCode = CodeUtil.inviteCode(user.getId());
            // 更新用户的推荐码
            boolean updateRes = baseMapper.update(null,
                    new LambdaUpdateWrapper<User>()
                            .set(User::getRecommendCode, inviteCode)
                            .eq(User::getId, user.getId())
            ) > 0;
            
            if (updateRes) {
                log.info("用户{}邀请码生成成功: {}", user.getId(), inviteCode);
            } else {
                log.warn("用户{}邀请码生成失败", user.getId());
            }
            
            noticeMiddle(user, extendData, DateUtil.date());
            return true;
        });
        return user.getId();
    }

    @Override
    public void loginOut(com.letu.solutions.core.model.ExtendData extendData) {
        // 删除token
        tokenUtil.removeToken(extendData.getHeaderDto().getToken());
    }

    @Override
    public UserResponse baseUserInfo(Long userId) {
        User user = baseMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getId, userId).eq(User::getDel, 0));
        Assert.notNull(user, "当前用户信息不存在");
        if (user.getEnable() == 0) {
            log.error("用户被禁用[{}]", user.getId());
            throw new RuntimeException("用户被禁用");
        }
        return BeanUtil.copyProperties(user, UserResponse.class);
    }

    @Override
    public UserResponse getUserBaseUser(Long userId) {
        User user = baseMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getId, userId));
        Assert.notNull(user, "当前用户信息不存在");
        return BeanUtil.copyProperties(user, UserResponse.class);
    }


        @Override
    public boolean updateName(UserUpdateNameReq req, ExtendData extendData) {
        log.info("用户更新昵称，用户ID: {}, 新昵称: {}", extendData.getUserId(), req.getNickName());
        Pair<Boolean, List<String>> pair = utilFacade.checkWord(req.getNickName());
        isFalse(pair.getKey(), LanguageUtil.trans(I18nConstants.NICKNAME_CONTAINS_VIOLATION_WORDS));
        boolean res = baseMapper.update(null, Wrappers.<User>lambdaUpdate().set(User::getNickName, req.getNickName())
                .eq(User::getId, extendData.getUserId())) > 0;
        if (res) {
            log.info("用户昵称更新成功，用户ID: {}", extendData.getUserId());
            loginService.refreshToken(extendData);
        } else {
            log.warn("用户昵称更新失败，用户ID: {}", extendData.getUserId());
        }
        return res;
    }

    @Override
    public boolean updateHead(UserUpdateHeadReq req, ExtendData extendData) {
        log.info("用户更新头像，用户ID: {}, 头像地址: {}", extendData.getUserId(), req.getImgUrl());
        boolean res = baseMapper.update(null, Wrappers.<User>lambdaUpdate().set(User::getUserImage, req.getImgUrl())
                .eq(User::getId, extendData.getUserId())) > 0;
        if (res) {
            log.info("用户头像更新成功，用户ID: {}", extendData.getUserId());
            loginService.refreshToken(extendData);
        } else {
            log.warn("用户头像更新失败，用户ID: {}", extendData.getUserId());
            throw new RuntimeException(LanguageUtil.trans(I18nConstants.USER_HEAD_UPDATE_FAILED));
        }
        return res;
    }

    @Override
    public Authentication loadAuthentication(Long userId, String openId, UserResponse userResponse, ExtendData extendData) {
        Authentication authentication = Authentication.builder()
                .userId(userId)
                .userInfo(UserInfo.builder()
                        .id(userId)
                        .userPhone(userResponse.getUserPhone())
                        .name(userResponse.getNickName())
                        .sex(userResponse.getSex())
                        .imgUrl(userResponse.getUserImage())
                        .createTime(userResponse.getCreateTime())
                        .openId(openId)
                        .build())
                .build();
        extendData.setUserId(userId);
        extendData.setAuthentication(authentication);
        return authentication;
    }

    @Override
    public UserUpdateResult updateBase(UserUpdateInfoReq req, ExtendData extendData) {
        log.info("用户更新基础信息，用户ID: {}, 昵称: {}, 头像: {}", extendData.getUserId(), req.getNickName(), req.getImgUrl());
        
        // ========== 第一步：前置校验 ==========
        UserUpdateResult validationResult = validateUpdateBaseRequest(req, extendData);
        if (!validationResult.isSuccess()) {
            log.warn("用户{}更新基础信息校验失败: {}", extendData.getUserId(), validationResult.getErrorMessage());
            return validationResult;
        }
        
        // ========== 第二步和第三步：执行更新和后置处理（需要事务） ==========
        UserUpdateResult result = transactionTemplate.execute(transactionStatus -> {
            try {
                // 执行用户基础信息更新
                UserUpdateResult updateResult = executeUserBaseUpdate(req, extendData);
                if (!updateResult.isSuccess()) {
                    log.warn("用户{}更新基础信息执行失败: {}", extendData.getUserId(), updateResult.getErrorMessage());
                    transactionStatus.setRollbackOnly();
                    return updateResult;
                }
                
                // 执行后置处理（在同一事务中）
                UserUpdateResult postResult = handlePostUpdateActions(req, extendData);
                if (!postResult.isSuccess()) {
                    log.warn("用户{}更新基础信息后置处理失败: {}", extendData.getUserId(), postResult.getErrorMessage());
                    transactionStatus.setRollbackOnly();
                    return postResult;
                }
                
                log.info("用户{}更新基础信息成功", extendData.getUserId());
                return UserUpdateResult.success();
            } catch (Exception e) {
                log.error("用户{}更新基础信息时发生异常", extendData.getUserId(), e);
                transactionStatus.setRollbackOnly();
                return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.USER_BASE_UPDATE_FAILED));
            }
        });
        
        return result;
    }


    @Override
    public void updateLastLoginTime(Long userId) {
        baseMapper.update(Wrappers.<User>lambdaUpdate().eq(User::getId, userId).set(User::getLastLoginTime, DateUtil.date()));
    }

    @Override
    public void delete(ExtendData extendData) {
        //查询当前用户
        User user = baseMapper.selectById(extendData.getUserId());
        if (user == null || user.getDel() == 1) {
            return;
        }
        //逻辑删除
        baseMapper.update(Wrappers.<User>lambdaUpdate().eq(User::getId, user.getId()).set(User::getDel, 1));
        //删除token
        loginOut(extendData);
        //获取当前用户所有token
        for (OsTypeEnum osType : OsTypeEnum.values()) {
            for (OsEnum osEnum : OsEnum.values()) {
                String tokenKey = TokenUtil.createRedisKey(BusinessTypeEnum.earnify_customer, osType, osEnum, user.getId());
                stringRedisTemplate.delete(tokenKey);
            }
        }
    }

    @Override
    public String getInviteCode(Long userId) {
        log.info("获取或生成用户{}的邀请码", userId);
        
        try {
            // 查询用户信息
            User user = baseMapper.selectById(userId);
            if (user == null) {
                log.error("用户{}不存在", userId);
                return null;
            }
            
            // 检查用户是否被禁用
            if (user.getEnable() == 0) {
                log.error("用户{}已被禁用", userId);
                return null;
            }
            
            String inviteCode = user.getRecommendCode();
            log.info("用户{}邀请码: {}", userId, inviteCode);
            return inviteCode;
        } catch (Exception e) {
            log.error("获取用户{}邀请码时发生异常", userId, e);
            return null;
        }
    }

    /**
     * 校验更新基础信息请求
     * Validate update base info request
     *
     * @param req 请求参数
     * @param extendData 扩展数据
     * @return 校验结果
     */
    private UserUpdateResult validateUpdateBaseRequest(UserUpdateInfoReq req, ExtendData extendData) {
        log.info("开始校验用户{}的更新基础信息请求", extendData.getUserId());
        
        try {
            // 1. 校验昵称是否包含违规词汇
            Pair<Boolean, List<String>> pair = utilFacade.checkWord(req.getNickName());
            if (pair.getKey()) {
                log.warn("用户{}的昵称包含违规词汇", extendData.getUserId());
                return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.NICKNAME_CONTAINS_VIOLATION_WORDS));
            }
            
            // 2. 如果填写了邀请码，进行邀请码校验
            if (StrUtil.isNotBlank(req.getInviteCode())) {
                UserUpdateResult inviteValidationResult = validateInviteCode(req.getInviteCode(), extendData.getUserId());
                if (!inviteValidationResult.isSuccess()) {
                    return inviteValidationResult;
                }
            }
            
            log.info("用户{}的更新基础信息请求校验通过", extendData.getUserId());
            return UserUpdateResult.success();
        } catch (Exception e) {
            log.error("用户{}校验更新基础信息请求时发生异常", extendData.getUserId(), e);
            return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.USER_BASE_UPDATE_FAILED));
        }
    }

    /**
     * 校验邀请码
     * Validate invite code
     *
     * @param inviteCode 邀请码
     * @param userId 用户ID
     * @return 校验结果
     */
    private UserUpdateResult validateInviteCode(String inviteCode, Long userId) {
        log.info("校验用户{}的邀请码: {}", userId, inviteCode);
        
        try {
            // 1. 校验邀请码是否有效
            User inviter = baseMapper.selectOne(
                new LambdaQueryWrapper<User>()
                    .eq(User::getRecommendCode, inviteCode)
                    .eq(User::getDel, 0)
                    .eq(User::getEnable, 1)
            );

            if (inviter == null) {
                log.warn("用户{}填写的邀请码{}无效或对应的用户已被禁用", userId, inviteCode);
                return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.INVITE_CODE_INVALID));
            }

            // 2. 检查是否邀请自己
            if (inviter.getId().equals(userId)) {
                log.warn("用户{}不能邀请自己", userId);
                return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.CANNOT_INVITE_SELF));
            }
            
            // 3. 检查当前用户是否已经被邀请过
            InviteRecord existingInvite = inviteRecordMapper.selectByInviteeId(userId);
            
            if (existingInvite != null) {
                log.warn("用户{}已经被邀请过，不能再次使用邀请码", userId);
                return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.ALREADY_INVITED));
            }
            
            log.info("用户{}的邀请码{}校验通过", userId, inviteCode);
            return UserUpdateResult.success();
        } catch (Exception e) {
            log.error("用户{}校验邀请码{}时发生异常", userId, inviteCode, e);
            return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.USER_BASE_UPDATE_FAILED));
        }
    }

    /**
     * 执行用户基础信息更新
     * Execute user base info update
     *
     * @param req 请求参数
     * @param extendData 扩展数据
     * @return 更新结果
     */
    private UserUpdateResult executeUserBaseUpdate(UserUpdateInfoReq req, ExtendData extendData) {
        log.info("开始执行用户{}的基础信息更新", extendData.getUserId());
        
        try {
            boolean updateResult = baseMapper.update(null, 
                Wrappers.<User>lambdaUpdate()
                    .set(User::getNickName, req.getNickName())
                    .set(User::getUserImage, req.getImgUrl())
                    .set(User::getUserDescription, req.getUserDescription())
                    .set(User::getCountryName, req.getCountryName())
                    .eq(User::getId, extendData.getUserId())
            ) > 0;

            if (updateResult) {
                log.info("用户{}基础信息更新成功", extendData.getUserId());
                return UserUpdateResult.success();
            } else {
                log.warn("用户{}基础信息更新失败", extendData.getUserId());
                return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.USER_BASE_UPDATE_FAILED));
            }
        } catch (Exception e) {
            log.error("用户{}执行基础信息更新时发生异常", extendData.getUserId(), e);
            return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.USER_BASE_UPDATE_FAILED));
        }
    }

    /**
     * 处理更新后的操作
     * Handle post-update actions
     *
     * @param req 请求参数
     * @param extendData 扩展数据
     * @return 处理结果
     */
    private UserUpdateResult handlePostUpdateActions(UserUpdateInfoReq req, ExtendData extendData) {
        log.info("开始处理用户{}更新后的操作", extendData.getUserId());
        
        try {
            // 1. 刷新token
            loginService.refreshToken(extendData);
            
            // 2. 处理邀请码逻辑
            if (StrUtil.isNotBlank(req.getInviteCode())) {
                UserUpdateResult inviteResult = handleInviteCodeProcessing(req.getInviteCode(), extendData.getUserId());
                if (!inviteResult.isSuccess()) {
                    return inviteResult;
                }
            }
            
            log.info("用户{}更新后的操作处理完成", extendData.getUserId());
            return UserUpdateResult.success();
        } catch (Exception e) {
            log.error("用户{}处理更新后操作时发生异常", extendData.getUserId(), e);
            return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.USER_BASE_UPDATE_FAILED));
        }
    }

    /**
     * 处理邀请码逻辑
     * Handle invite code processing
     *
     * @param inviteCode 邀请码
     * @param userId 用户ID
     * @return 处理结果
     */
    private UserUpdateResult handleInviteCodeProcessing(String inviteCode, Long userId) {
        log.info("开始处理用户{}的邀请码{}", userId, inviteCode);
        
        try {
            // 处理邀请逻辑
            boolean inviteSuccess = inviteService.handleUserRegistration(inviteCode, userId);
            
            if (inviteSuccess) {
                log.info("用户{}邀请码{}处理成功", userId, inviteCode);
                return UserUpdateResult.success();
            } else {
                log.warn("用户{}邀请码{}处理失败", userId, inviteCode);
                return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.INVITE_CREATE_FAILED));
            }
        } catch (Exception e) {
            log.error("用户{}处理邀请码{}时发生异常", userId, inviteCode, e);
            return UserUpdateResult.fail(LanguageUtil.trans(I18nConstants.INVITE_CREATE_FAILED));
        }
    }

    private void noticeMiddle(User user, ExtendData extendData, Date now) {
        if (StrUtil.isNotEmpty(user.getPwd())) {
            userFacade.registerPwd(UserRegisterPwdReq.builder()
                            .clientUserId(user.getId())
                            .phone(user.getUserPhone())
                            .pwd(user.getPwd())
                            .salt(user.getSalt())
                            .registerDate(now)
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .build()
                    , ExtendCovert.covert(extendData));
        } else if (StrUtil.isNotEmpty(user.getUserPhone())) {
            userFacade.registerMobile(UserRegisterMobileReq.builder()
                            .clientUserId(user.getId())
                            .phone(user.getUserPhone())
                            .registerDate(now)
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .build()
                    , ExtendCovert.covert(extendData));
        } else {
            userFacade.registerDeviceId(UserRegisterDeviceIdReq.builder()
                            .clientUserId(user.getId())
                            .registerDate(now)
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .build()
                    , ExtendCovert.covert(extendData));
        }
    }
    /**
     * 根据邮箱获取用户ID如果用户不存在，则创建新用户
     *
     * @param request    包含登录信息的请求对象，用于新用户注册时的数据传递
     * @param extendData 扩展数据，可能包含额外的用户信息或上下文数据
     * @return 用户ID，现有用户或新注册用户的唯一标识
     */
    public Long getUserByEmail(EmailLoginRequest request, ExtendData extendData) {
        // 查询数据库中是否存在该邮箱的用户
        User user = baseMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getEmail, request.getEmail()));
        //获取DB中的用户ID
        Long userId = CustomerUtil.getUserId(user);
        if (userId == null) {
            return this.createUserEmail(request, extendData);
        }
        return userId;
    }
    private Long createUserEmail(EmailLoginRequest request, ExtendData extendData) {
        // 临时保存 uId
        Long[] uIdHolder = new Long[1];

        boolean success = transactionalManage.execute(() -> {
            RegisterRequest registerRequest = RegisterRequest.builder().email(request.getEmail()).build();
            Long uId = this.createUser(registerRequest, extendData);
            uIdHolder[0] = uId;

            UserRegisterEmailReq registerEmailReq = UserRegisterEmailReq.builder()
                    .email(request.getEmail())
                    .clientUserId(uId)
                    .deviceId(request.getDeviceId())
                    .codeChannel(request.getCodeChannel())
                    .pageId(request.getPageId())
                    .registerDate(new Date())
                    .build();
            userFacade.registerEmail(registerEmailReq, ExtendCovert.covert(extendData));

            // 返回 boolean 表示是否成功
            return uId != null;  // 或 true 表示成功
        });

        if (success) {
            return uIdHolder[0];
        } else {
            return null;  // 或抛异常
        }
    }
    /**
     * 获取登录响应信息
     *
     * @param userId     用户ID，用于获取用户信息和生成登录令牌
     * @param extendData 扩展数据，包含设备和操作系统信息，用于生成令牌和记录登录信息
     * @return 登录响应对象，包含用户信息和登录令牌
     */
    public LoginRes getLoginRes(Long userId, ExtendData extendData) {
        // 获取操作系统信息
        OsEnum os = extendData.getHeaderDto().getOs();
        // 获取用户的基本信息
        LoginUserRes userRes = this.getLoginUser(userId);

        // 生成登录令牌
        String token = tokenUtil.createToken(
                userId, BusinessTypeEnum.earnify_customer,
                extendData.getHeaderDto().getOsType().getCode(), os.getServerType(),
                this.loadAuthentication(userId, userRes, extendData), 30);

        // 创建登录响应对象
        LoginRes loginRes = new LoginRes();
        loginRes.setToken(token);
        loginRes.setUserDetail(userRes);

        // 记录用户登录信息
        userFacade.login(LoginDeviceReq.builder()
                .clientUserId(userId)
                .loginDate(new Date())
                .build(), ExtendCovert.covert(extendData));

        // 设置其他必要的登录信息
        return loginRes;
    }
    /**
     * 根据用户ID获取登录用户信息
     * 此方法首先从UserDetail服务中获取用户详细信息，然后将这些信息转换为LoginUserRes对象，
     * 并根据用户的VIP状态和VIP时间设置相应的VIP信息，最后返回这个处理后的LoginUserRes对象
     *
     * @param userId 用户ID，用于查询用户详细信息
     * @return LoginUserRes 包含用户登录信息的响应对象
     */
    public LoginUserRes getLoginUser(Long userId) {
        // 通过用户ID获取用户详细信息
        UserResponse userDetail = userService.getUserBaseUser(userId);

        // 将用户详细信息复制到登录用户响应对象中
        LoginUserRes userRes = BeanUtil.copyProperties(userDetail, LoginUserRes.class);
        // 返回处理后的登录用户响应对象
        return userRes;
    }
    /**
     * 通过Google ID获取用户信息
     * 如果用户已存在，则直接返回用户ID；如果用户不存在，则创建新用户并返回新用户的ID
     *
     * @param googleSub  Google用户的唯一标识符
     * @param request    Google登录请求对象，包含登录所需的信息
     * @param extendData 扩展数据对象，用于传递额外的请求参数
     * @return 用户ID
     */
    public Long getUserByGoogle(String googleSub, GoogleLoginRequest request, ExtendData extendData) {
        // 查询数据库中是否存在与Google ID关联的用户
        User user = baseMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getGoogleSub, googleSub));

        //获取DB中的用户ID
        Long userId = CustomerUtil.getUserId(user);
        if (userId == null) {
            // 如果用户不存在，则创建新用户
            RegisterRequest registerRequest = RegisterRequest.builder().googleSub(googleSub).build();
            return this.registerDeviceId(registerRequest, request, extendData);
        }
        return userId;
    }
    /**
     * 注册设备ID并返回用户ID
     * <p>
     * 该方法首先检查用户是否存在，如果不存在则创建新用户随后，它会使用用户的ID和设备信息
     * 注册设备ID，以便在系统中记录设备信息
     *
     * @param registerRequest 包含用户注册信息的请求对象
     * @param request         包含设备ID等登录信息的请求对象
     * @param extendData      扩展数据，用于传递额外的请求参数
     * @return 返回用户的ID
     */
    private Long registerDeviceId(RegisterRequest registerRequest, BaseLoginRequest request, ExtendData extendData) {

        // 如果用户不存在，则创建新用户
        Long userId = this.createUser(registerRequest, extendData);

        // 为新用户注册设备ID
        Boolean success = transactionalManage.execute(() -> {
            UserRegisterDeviceIdReq userRegisterDeviceIdReq = UserRegisterDeviceIdReq
                    .builder()
                    .clientUserId(userId)
                    .deviceId(request.getDeviceId())
                    .codeChannel(request.getCodeChannel())
                    .pageId(request.getPageId())
                    .registerDate(new Date())
                    .build();
            userFacade.registerDeviceId(userRegisterDeviceIdReq, ExtendCovert.covert(extendData));
            return true; // 返回 boolean 表示事务执行成功
        });

        return Boolean.TRUE.equals(success) ? userId : null;
    }
    public Authentication loadAuthentication(Long userId, LoginUserRes loginUserRes, ExtendData extendData) {
        Authentication authentication = Authentication.builder()
                .userId(userId)
                .userInfo(UserInfo.builder()
                        .id(userId)
                        .build())
                .build();
        extendData.setUserId(userId);
        extendData.setAuthentication(authentication);
        return authentication;
    }
}