package com.letu.solutions.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.InviteRecord;
import com.letu.solutions.model.request.invite.InviteListQueryRequest;
import com.letu.solutions.model.response.customer.InviteRecordRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 邀请记录Mapper
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Mapper
public interface InviteRecordMapper extends BaseMapper<InviteRecord> {

    /**
     * 分页查询邀请记录
     *
     * @param page 分页对象
     * @param queryRequest 查询条件
     * @param userId 用户ID
     * @return 分页结果
     */
    Page<InviteRecordRes> selectInviteRecordPage(Page<InviteRecordRes> page, 
                                                @Param("queryRequest") InviteListQueryRequest queryRequest,
                                                @Param("userId") Long userId);

    /**
     * 根据邀请码查询邀请记录
     *
     * @param inviteCode 邀请码
     * @return 邀请记录
     */
    InviteRecord selectByInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 根据被邀请人ID查询邀请记录
     *
     * @param inviteeId 被邀请人ID
     * @return 邀请记录
     */
    InviteRecord selectByInviteeId(@Param("inviteeId") Long inviteeId);

    /**
     * 根据顶级邀请人ID查询所有下级用户数量
     *
     * @param rootInviterId 顶级邀请人ID
     * @return 下级用户数量
     */
    Long countByRootInviterId(@Param("rootInviterId") Long rootInviterId);
} 