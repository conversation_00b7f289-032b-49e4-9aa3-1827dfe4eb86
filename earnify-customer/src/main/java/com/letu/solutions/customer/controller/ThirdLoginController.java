package com.letu.solutions.customer.controller;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.GoogleService;
import com.letu.solutions.model.request.login.GoogleLoginRequest;
import com.letu.solutions.model.response.login.LoginRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 登录/三方登录
 *
 * @Author: jack ma
 * @CreateTime: 2025-04-28  11:43
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class ThirdLoginController {
    private final GoogleService googleService;

    /**
     * 谷歌登录
     */
    @PostMapping("/login/googleLogin")
    public R<LoginRes> googleLogin(
            @Validated @RequestBody GoogleLoginRequest request,
            @RequestHeader ExtendData extendData) {
        LoginRes loginRes = googleService.googleLogin(request, extendData);
        return R.success(loginRes);
    }

}
