package com.letu.solutions.customer.service;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.enums.cms.SocialTypeEnum;
import com.letu.solutions.model.request.user.UserSocialBindRequest;
import com.letu.solutions.model.response.user.UserSocialResponse;

/**
 * 用户社交媒体绑定服务接口
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface UserSocialService {

    /**
     * 绑定社交媒体
     *
     * @param request 绑定请求
     * @param extendData 扩展数据
     * @return 成功返回null，失败返回错误消息
     */
    String bindSocial(UserSocialBindRequest request, ExtendData extendData);

    /**
     * 查询用户社交媒体绑定信息
     *
     * @param extendData 扩展数据
     * @return 社交媒体绑定信息
     */
    UserSocialResponse getUserSocial(ExtendData extendData);

    /**
     * 验证社交媒体绑定状态
     *
     * @param socialType 社交媒体类型
     * @param extendData 扩展数据
     * @return 是否验证成功
     */
    boolean verifySocial(SocialTypeEnum socialType, ExtendData extendData);
} 