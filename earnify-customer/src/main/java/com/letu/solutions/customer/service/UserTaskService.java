package com.letu.solutions.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.model.request.task.UserTaskListQueryRequest;
import com.letu.solutions.model.request.task.FinishStepRequest;
import com.letu.solutions.model.response.customer.UserTaskPageRes;
import com.letu.solutions.model.response.customer.TaskPageRes;
import com.letu.solutions.model.request.user.CompletedUserTaskQueryRequest;
import com.letu.solutions.model.response.user.CompletedUserTaskResponse;
import com.letu.solutions.model.response.task.TaskReceiveResponse;

/**
 * 用户任务服务接口
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface UserTaskService {
    /**
     * 我的任务分页查询
     * @param page 分页对象
     * @param queryRequest 查询参数
     * @param extendData 用户扩展信息（含用户ID）
     */
    Page<UserTaskPageRes> page(Page<UserTaskPageRes> page, UserTaskListQueryRequest queryRequest, ExtendData extendData);

    /**
     * 我的任务详情
     * @param userTaskId 用户任务ID
     * @param extendData 用户扩展信息（含用户ID）
     */
    TaskPageRes detail(Long userTaskId, ExtendData extendData);

    /**
     * 任务领取
     */
    R<TaskReceiveResponse> receive(Long taskId, ExtendData extendData);

    /**
     * 申诉接口
     */
    R<String> appeal(Long userTaskId, ExtendData extendData);

    /**
     * 提交任务接口
     */
    R<String> submit(Long userTaskId, ExtendData extendData);

    /**
     * 完成步骤接口
     */
    R<String> finishStep(FinishStepRequest request, ExtendData extendData);

    /**
     * 分页查询已完成且发布者为甲方账户的用户任务
     *
     * @param request 查询请求参数
     * @param extendData 扩展数据
     * @return 分页结果
     */
    Page<CompletedUserTaskResponse> getCompletedUserTaskPage(CompletedUserTaskQueryRequest request, ExtendData extendData);
} 