package com.letu.solutions.customer.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.Properties;

/**
 * 邮件发送工具类
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Data
public class EmailSender {

    /**
     * 邮件发送配置
     */
    @Data
    public static class EmailConfig {
        private String host;
        private Integer port;
        private String user;
        private String pass;
        private String from;
        private boolean debug = false;
        private int timeout = 10000;
        private int connectionTimeout = 10000;
        private int writeTimeout = 10000;

        public EmailConfig() {}

        public EmailConfig(String host, Integer port, String user, String pass, String from) {
            this.host = host;
            this.port = port;
            this.user = user;
            this.pass = pass;
            this.from = from;
        }
    }

    /**
     * 邮件内容
     */
    public static class EmailContent {
        private String to;
        private String subject;
        private String content;
        private Date sentDate;

        public EmailContent() {
            this.sentDate = new Date();
        }

        public EmailContent(String to, String subject, String content) {
            this.to = to;
            this.subject = subject;
            this.content = content;
            this.sentDate = new Date();
        }

        // Getters and Setters
        public String getTo() { return to; }
        public void setTo(String to) { this.to = to; }
        
        public String getSubject() { return subject; }
        public void setSubject(String subject) { this.subject = subject; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public Date getSentDate() { return sentDate; }
        public void setSentDate(Date sentDate) { this.sentDate = sentDate; }
    }

    /**
     * 邮件发送结果
     */
    @Data
    public static class EmailResult {
        private boolean success;
        private String message;
        private Exception exception;

        public EmailResult() {}

        public EmailResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public EmailResult(boolean success, String message, Exception exception) {
            this.success = success;
            this.message = message;
            this.exception = exception;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public Exception getException() { return exception; }
        public void setException(Exception exception) { this.exception = exception; }

        public static EmailResult success(String message) {
            return new EmailResult(true, message);
        }

        public static EmailResult failure(String message) {
            return new EmailResult(false, message);
        }

        public static EmailResult failure(String message, Exception exception) {
            return new EmailResult(false, message, exception);
        }
    }

    /**
     * 发送邮件
     * 
     * @param config 邮件配置
     * @param content 邮件内容
     * @return 发送结果
     */
    public static EmailResult sendEmail(EmailConfig config, EmailContent content) {
        // 验证配置
        EmailResult validationResult = validateConfig(config);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 验证内容
        EmailResult contentValidationResult = validateContent(content);
        if (!contentValidationResult.isSuccess()) {
            return contentValidationResult;
        }

        log.info("开始发送邮件，收件人: {}, SMTP配置: {}:{}, 发件人: {}", 
                content.getTo(), config.getHost(), config.getPort(), config.getFrom());
        log.info("邮件主题: {}, 内容: {}", content.getSubject(), content.getContent());

        try {
            // 创建邮件会话
            Session session = createSession(config);
            
            // 创建邮件消息
            Message message = createMessage(session, config, content);
            
            // 发送邮件
            Transport.send(message);
            
            log.info("邮件发送成功！收件人: {}", content.getTo());
            return EmailResult.success("邮件发送成功");

        } catch (AuthenticationFailedException e) {
            String errorMsg = "邮件认证失败，请检查邮箱地址和授权码是否正确";
            log.error("邮件认证失败: {}", e.getMessage());
            return EmailResult.failure(errorMsg, e);
        } catch (SendFailedException e) {
            String errorMsg = "邮件发送失败，请检查收件人邮箱地址是否正确";
            log.error("邮件发送失败: {}", e.getMessage());
            return EmailResult.failure(errorMsg, e);
        } catch (MessagingException e) {
            String errorMsg = "邮件发送异常";
            log.error("邮件发送异常: {}", e.getMessage(), e);
            return EmailResult.failure(errorMsg, e);
        } catch (Exception e) {
            String errorMsg = "邮件发送系统异常";
            log.error("邮件发送系统异常: {}", e.getMessage(), e);
            return EmailResult.failure(errorMsg, e);
        }
    }

    /**
     * 验证邮件配置
     */
    private static EmailResult validateConfig(EmailConfig config) {
        if (config == null) {
            return EmailResult.failure("邮件配置不能为空");
        }
        
        if (config.getHost() == null || config.getHost().trim().isEmpty()) {
            return EmailResult.failure("SMTP服务器地址不能为空");
        }
        
        if (config.getPort() == null || config.getPort() <= 0) {
            return EmailResult.failure("SMTP端口不能为空或无效");
        }
        
        if (config.getUser() == null || config.getUser().trim().isEmpty()) {
            return EmailResult.failure("邮箱用户名不能为空");
        }
        
        if (config.getPass() == null || config.getPass().trim().isEmpty()) {
            return EmailResult.failure("邮箱密码/授权码不能为空");
        }
        
        if (config.getFrom() == null || config.getFrom().trim().isEmpty()) {
            return EmailResult.failure("发件人邮箱不能为空");
        }
        
        return EmailResult.success("配置验证通过");
    }

    /**
     * 验证邮件内容
     */
    private static EmailResult validateContent(EmailContent content) {
        if (content == null) {
            return EmailResult.failure("邮件内容不能为空");
        }
        
        if (content.getTo() == null || content.getTo().trim().isEmpty()) {
            return EmailResult.failure("收件人邮箱不能为空");
        }
        
        if (content.getSubject() == null || content.getSubject().trim().isEmpty()) {
            return EmailResult.failure("邮件主题不能为空");
        }
        
        if (content.getContent() == null || content.getContent().trim().isEmpty()) {
            return EmailResult.failure("邮件内容不能为空");
        }
        
        return EmailResult.success("内容验证通过");
    }

    /**
     * 创建邮件会话
     */
    private static Session createSession(EmailConfig config) {
        Properties props = new Properties();
        props.put("mail.smtp.host", config.getHost());
        props.put("mail.smtp.port", config.getPort().toString());
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "false");
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.put("mail.smtp.socketFactory.port", config.getPort().toString());
        props.put("mail.smtp.socketFactory.fallback", "false");
        props.put("mail.smtp.timeout", String.valueOf(config.getTimeout()));
        props.put("mail.smtp.connectiontimeout", String.valueOf(config.getConnectionTimeout()));
        props.put("mail.smtp.writetimeout", String.valueOf(config.getWriteTimeout()));
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");

        Session session = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(config.getUser(), config.getPass());
            }
        });

        session.setDebug(config.isDebug());
        return session;
    }

    /**
     * 创建邮件消息
     */
    private static Message createMessage(Session session, EmailConfig config, EmailContent content) throws MessagingException {
        Message message = new MimeMessage(session);
        message.setFrom(new InternetAddress(config.getFrom()));
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(content.getTo()));
        message.setSubject(content.getSubject());
        message.setText(content.getContent());
        message.setSentDate(content.getSentDate());
        return message;
    }

    /**
     * 快速发送验证码邮件
     * 
     * @param config 邮件配置
     * @param toEmail 收件人邮箱
     * @param code 验证码
     * @return 发送结果
     */
    public static EmailResult sendVerificationCode(EmailConfig config, String toEmail, String code) {
        EmailContent content = new EmailContent(
            toEmail,
            CustomerUtil.SUBJECT,
            String.format(CustomerUtil.CONTENT, code)
        );
        return sendEmail(config, content);
    }

    /**
     * 快速发送测试邮件
     * 
     * @param config 邮件配置
     * @param toEmail 收件人邮箱
     * @return 发送结果
     */
    public static EmailResult sendTestEmail(EmailConfig config, String toEmail) {
        EmailContent content = new EmailContent(
            toEmail,
            "Earnify - Email Test",
            String.format("""
                Dear User,

                This is a test email to verify that the email sending functionality is working properly.

                Send Time: %s
                From: %s
                To: %s

                If you receive this email, it means the email sending function is working correctly.

                Thank you for choosing Earnify.

                Best regards,

                Earnify Team""", 
                new Date(), config.getFrom(), toEmail)
        );
        return sendEmail(config, content);
    }
} 