package com.letu.solutions.customer.service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.letu.solutions.core.config.ThirdloginConfiguration;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.enums.OsEnum;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.HeaderDto;
import com.letu.solutions.model.request.login.GoogleLoginRequest;
import com.letu.solutions.model.response.login.LoginRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @Author: jack ma
 * @CreateTime: 2025-04-28  18:33
 * @Version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleService {

    private final ThirdloginConfiguration thirdloginConfiguration;

    private final UserService userService;

    /**
     * 处理谷歌登录
     *
     * @param request    谷歌登录请求参数
     * @param extendData 扩展数据
     * @return LoginResponse
     */
    public LoginRes googleLogin(GoogleLoginRequest request, ExtendData extendData) {
        HeaderDto headerDto = extendData.getHeaderDto();
        OsEnum os = headerDto.getOs();
        if (os == null) {
            throw new ThrowException(I18nConstants.PLATFORM_NOT_SUPPORT);
        }
        if (os == OsEnum.web) {
            GoogleIdTokenVerifier verifier = this.getGoogleIdTokenVerifier(os);
            String subject = this.getSubject(verifier, request.getToken());
            Long userId = userService.getUserByGoogle(subject, request, extendData);
            return userService.getLoginRes(userId, extendData);
        } else {
            throw new ThrowException(I18nConstants.PLATFORM_NOT_SUPPORT);
        }

    }

    public GoogleIdTokenVerifier getGoogleIdTokenVerifier(OsEnum os) {
        if (os == OsEnum.h5) {
            return new GoogleIdTokenVerifier.Builder(new NetHttpTransport(), new GsonFactory())
                    .setAudience(Collections.singletonList(thirdloginConfiguration.getGoogleClientH5()))
                    .build();
        }
        return new GoogleIdTokenVerifier.Builder(new NetHttpTransport(), new GsonFactory())
                .setAudience(Collections.singletonList(thirdloginConfiguration.getGoogleClientAndroid()))
                .build();
    }

    public String getSubject(GoogleIdTokenVerifier verifier, String token) {
        try {
            GoogleIdToken idToken = verifier.verify(token);
            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();
                return payload.getSubject();
            } else {
                throw new ThrowException(I18nConstants.INVALID_GOOGLE_TOKEN);
            }
        } catch (Exception e) {
            log.error("谷歌登录验证失败", e);
            throw new ThrowException(I18nConstants.GOOGLE_VERIFICATION_FAIL);
        }
    }
}
