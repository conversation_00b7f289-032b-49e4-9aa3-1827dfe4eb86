package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.customer.mapper.TaskMapper;
import com.letu.solutions.customer.mapper.TaskStepMapper;
import com.letu.solutions.customer.mapper.UserTaskMapper;
import com.letu.solutions.customer.mapper.UserTaskStepMapper;
import com.letu.solutions.customer.service.PartyUserInfoService;
import com.letu.solutions.customer.service.SysParamService;
import com.letu.solutions.customer.service.TaskService;
import com.letu.solutions.customer.utils.CountdownUtil;
import com.letu.solutions.model.cms.response.cms.TaskStepPageRes;
import com.letu.solutions.model.entity.cms.Task;
import com.letu.solutions.model.entity.cms.TaskStep;
import com.letu.solutions.model.entity.cms.UserTask;
import com.letu.solutions.model.entity.cms.UserTaskStep;
import com.letu.solutions.model.request.task.TaskListQueryRequest;
import com.letu.solutions.model.response.customer.PartyUserInfoRes;
import com.letu.solutions.model.response.customer.TaskPageRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, Task> implements TaskService {
    
    /**
     * 时间转换常量
     */
    private static final int HOURS_PER_DAY = 24;
    private static final int SEVEN_DAYS_HOURS = 7 * HOURS_PER_DAY; // 168小时

    @Autowired
    private TaskStepMapper taskStepMapper;

    @Autowired
    private PartyUserInfoService partyUserInfoService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private UserTaskMapper userTaskMapper;

    @Autowired
    private UserTaskStepMapper userTaskStepMapper;
    
    @Autowired
    private SysParamService sysParamService;

    @Override
    public Page<TaskPageRes> page(Page<TaskPageRes> page, TaskListQueryRequest queryRequest) {
        log.info("[TaskServiceImpl] 任务分页查询, 参数: {}", queryRequest);
        
        // 转换前端传递的天数为小时数
        convertTimeFromDaysToHours(queryRequest);
        
        // 直接查询VO分页
        Page<TaskPageRes> taskPage = taskMapper.selectTaskPageResPage(page, queryRequest);
        log.info("[TaskServiceImpl] 查询到任务数量: {}", taskPage.getRecords().size());
        // 补充甲方信息
        if (taskPage != null && taskPage.getRecords() != null) {
            for (TaskPageRes res : taskPage.getRecords()) {
                if (res.getId() != null) {
                    PartyUserInfoRes partyUserInfo = partyUserInfoService.getPartyUserInfoByTaskId(res.getId());
                    res.setPartyUserInfo(partyUserInfo);
                }
            }
        }
        return taskPage;
    }

    @Override
    public TaskPageRes getTaskDetail(Long id, ExtendData extendData) {
        Long userId = extendData != null ? extendData.getUserId() : null;
        return getTaskDetailByTaskId(id, userId);
    }
    
    /**
     * 通用详情查询方法
     * 根据taskId和userId查询任务详情，如果用户已领取则返回用户任务详情
     * 
     * @param taskId 任务ID
     * @param userId 用户ID（可为null，表示匿名用户）
     * @return TaskPageRes 任务详情响应
     */
    public TaskPageRes getTaskDetailByTaskId(Long taskId, Long userId) {
        log.info("[TaskServiceImpl] 通用详情查询, taskId={}, userId={}", taskId, userId);
        
        // 1. 参数校验并获取任务信息
        Task task = validateAndGetTask(taskId);
        if (task == null) {
            return null;
        }
        
        // 2. 构建基础响应对象
        TaskPageRes res = buildBaseTaskResponse(task);
        
        // 3. 设置原始任务步骤列表
        setOriginalTaskSteps(res, task.getId());
        
        // 4. 如果用户已登录，处理用户任务信息
        if (userId != null) {
            processUserTaskInfo(res, task, userId);
        } else {
            // 匿名用户
            res.setHasReceived(false);
            log.info("[TaskServiceImpl] 匿名用户访问，返回原始任务步骤, taskId={}", task.getId());
        }
        
        log.info("[TaskServiceImpl] 返回任务详情, taskId={}, userId={}", taskId, userId);
        return res;
    }
    
    /**
     * 根据userTaskId查询用户任务详情
     * 
     * @param userTaskId 用户任务ID
     * @param userId 用户ID
     * @return TaskPageRes 任务详情响应
     */
    public TaskPageRes getTaskDetailByUserTaskId(Long userTaskId, Long userId) {
        log.info("[TaskServiceImpl] 根据用户任务ID查询详情, userTaskId={}, userId={}", userTaskId, userId);
        
        // 1. 查询用户任务
        UserTask userTask = userTaskMapper.selectById(userTaskId);
        if (userTask == null || !userTask.getUserId().equals(userId)) {
            log.warn("[TaskServiceImpl] 用户任务不存在或权限不足, userTaskId={}, userId={}", userTaskId, userId);
            return null;
        }
        
        // 2. 获取原始任务信息
        Task task = this.getById(userTask.getTaskId());
        if (task == null) {
            log.warn("[TaskServiceImpl] 原始任务不存在, taskId={}", userTask.getTaskId());
            return null;
        }
        
        // 3. 构建基础响应对象（包含任务基本信息）
        TaskPageRes res = buildBaseTaskResponse(task);
        
        log.info("[TaskServiceImpl] 构建基础响应对象后, res.name={}, res.attribute={}, res.taskType={}, res.number={}, res.price={}, res.state={}", 
            res.getName(), res.getAttribute(), res.getTaskType(), res.getNumber(), res.getPrice(), res.getState());
        
        // 4. 设置原始任务步骤列表（作为参考）
        setOriginalTaskSteps(res, task.getId());
        
        // 5. 设置用户任务信息
        setUserTaskInfo(res, userTask);
        
        // 6. 设置用户任务步骤（覆盖原始步骤）
        setUserTaskSteps(res, task.getId(), userId);
        
        log.info("[TaskServiceImpl] 最终返回前, res.name={}, res.attribute={}, res.taskType={}, res.number={}, res.price={}, res.state={}", 
            res.getName(), res.getAttribute(), res.getTaskType(), res.getNumber(), res.getPrice(), res.getState());
        
        log.info("[TaskServiceImpl] 返回用户任务详情, userTaskId={}, userId={}", userTaskId, userId);
        return res;
    }
    
    /**
     * 校验参数并获取任务信息
     */
    private Task validateAndGetTask(Long id) {
        if (id == null) {
            log.warn("[TaskServiceImpl] 任务ID为空");
            return null;
        }
        
        Task task = this.getById(id);
        if (task == null) {
            log.warn("[TaskServiceImpl] 任务不存在, id={}", id);
            return null;
        }
        
        return task;
    }
    
    /**
     * 将前端传递的天数转换为小时数
     * 前端传递：1=一天内，3=三天内，7=七天内，other=其它
     * 转换为：24=一天内，72=三天内，168=七天内，168=其它（超过7天）
     * 
     * @param queryRequest 查询请求对象
     */
    private void convertTimeFromDaysToHours(TaskListQueryRequest queryRequest) {
        if (queryRequest.getTime() != null && !queryRequest.getTime().isEmpty()) {
            if ("other".equals(queryRequest.getTime())) {
                // other 表示超过7天，转换为168小时
                queryRequest.setTime(String.valueOf(SEVEN_DAYS_HOURS));
                log.info("[TaskServiceImpl] 时间转换: other -> {}小时", SEVEN_DAYS_HOURS);
            } else {
                try {
                    int days = Integer.parseInt(queryRequest.getTime());
                    int hours = days * HOURS_PER_DAY;
                    queryRequest.setTime(String.valueOf(hours));
                    log.info("[TaskServiceImpl] 时间转换: {}天 -> {}小时", days, hours);
                } catch (NumberFormatException e) {
                    log.warn("[TaskServiceImpl] 时间参数转换失败: {}", queryRequest.getTime());
                }
            }
        }
    }
    
    /**
     * 构建基础任务响应对象
     */
    private TaskPageRes buildBaseTaskResponse(Task task) {
        TaskPageRes res = new TaskPageRes();
        
        // 手动设置字段，避免复制 userId
        res.setId(task.getId());
        res.setAttribute(task.getAttribute());
        res.setName(task.getName());
        res.setProductId(task.getProductId());
        res.setWeightSorting(task.getWeightSorting());
        res.setTaskType(task.getTaskType());
        res.setNumber(task.getNumber());
        res.setPrice(task.getPrice());
        res.setTime(task.getTime());
        res.setState(task.getState());
        res.setStepNumber(task.getStepNumber());
        res.setUrl(task.getUrl());
        res.setCreateTime(task.getCreateTime());
        res.setUpdateTime(task.getUpdateTime());
        res.setTaskFinishNum(task.getTaskFinishNum());
        res.setTaskReceiveNum(task.getTaskReceiveNum());
        res.setBond(task.getBond());
        
        // 查询并设置甲方信息
        PartyUserInfoRes partyUserInfo = partyUserInfoService.getPartyUserInfoByTaskId(task.getId());
        res.setPartyUserInfo(partyUserInfo);
        
        log.info("[TaskServiceImpl] 构建基础任务响应对象, taskId={}, name={}, attribute={}, taskType={}, number={}, price={}, state={}", 
            task.getId(), task.getName(), task.getAttribute(), task.getTaskType(), task.getNumber(), task.getPrice(), task.getState());
        
        return res;
    }
    
    /**
     * 设置原始任务步骤列表
     */
    private void setOriginalTaskSteps(TaskPageRes res, Long taskId) {
        List<TaskStep> stepList = taskStepMapper.selectList(
            new LambdaQueryWrapper<TaskStep>()
                .eq(TaskStep::getTaskId, taskId)
                .orderByAsc(TaskStep::getSrot)
        );
        
        List<TaskStepPageRes> stepResList = stepList.stream()
            .map(this::convertToTaskStepPageRes)
            .collect(Collectors.toList());
        
        res.setStepList(stepResList);
    }
    
    /**
     * 处理用户任务信息
     */
    private void processUserTaskInfo(TaskPageRes res, Task task, ExtendData extendData) {
        if (extendData == null || extendData.getUserId() == null) {
            // 匿名用户
            res.setHasReceived(false);
            log.info("[TaskServiceImpl] 匿名用户访问，返回原始任务步骤, taskId={}", task.getId());
            return;
        }
        
        processUserTaskInfo(res, task, extendData.getUserId());
    }
    
    /**
     * 处理用户任务信息（重载方法）
     */
    private void processUserTaskInfo(TaskPageRes res, Task task, Long userId) {
        try {
            UserTask userTask = findUserTask(task.getId(), userId);
            
            if (userTask != null) {
                // 用户已领取任务
                setUserTaskInfo(res, userTask);
                setUserTaskSteps(res, task.getId(), userId);
                log.info("[TaskServiceImpl] 返回用户任务详情, taskId={}, userId={}, progress={}", 
                    task.getId(), userId, res.getUserTaskProgress());
            } else {
                // 用户未领取任务
                res.setHasReceived(false);
                log.info("[TaskServiceImpl] 用户未领取任务，返回原始任务步骤, taskId={}, userId={}", task.getId(), userId);
            }
        } catch (Exception e) {
            log.warn("[TaskServiceImpl] 查询用户任务状态时发生异常, taskId={}, userId={}, error={}", 
                task.getId(), userId, e.getMessage());
            res.setHasReceived(false);
        }
    }
    
    /**
     * 查找用户任务
     */
    private UserTask findUserTask(Long taskId, Long userId) {
        return userTaskMapper.selectOne(
            new LambdaQueryWrapper<UserTask>()
                .eq(UserTask::getTaskId, taskId)
                .eq(UserTask::getUserId, userId)
        );
    }
    
    /**
     * 设置用户任务信息
     */
    private void setUserTaskInfo(TaskPageRes res, UserTask userTask) {
        log.info("[TaskServiceImpl] 用户已领取任务，返回用户任务详情, userTaskId={}", userTask.getId());
        
        res.setUserTaskId(userTask.getId());
        res.setUserTaskState(userTask.getState());
        res.setUserTaskCreateTime(userTask.getCreateTime());
        res.setUserTaskUpdateTime(userTask.getUpdateTime());
        res.setUserTaskExamineTime(userTask.getExamineTime());
        res.setUserTaskAppealTime(userTask.getAppealTime());
        res.setHasReceived(true);
        
        // 计算倒计时
        calculateCountdowns(userTask, res);
    }
    
    /**
     * 计算倒计时
     */
    private void calculateCountdowns(UserTask userTask, TaskPageRes res) {
        CountdownUtil.calculateAllCountdowns(userTask, res, sysParamService);
    }
    
    /**
     * 设置用户任务步骤
     */
    private void setUserTaskSteps(TaskPageRes res, Long taskId, Long userId) {
        List<UserTaskStep> userStepList = userTaskStepMapper.selectList(
            new LambdaQueryWrapper<UserTaskStep>()
                .eq(UserTaskStep::getTaskId, taskId)
                .eq(UserTaskStep::getUserId, userId)
                .orderByAsc(UserTaskStep::getSrot)
        );
        
        List<TaskStepPageRes> userStepResList = new ArrayList<>();
        int completedSteps = 0;
        int totalSteps = userStepList.size();
        
        for (UserTaskStep userStep : userStepList) {
            TaskStepPageRes stepRes = convertToUserTaskStepPageRes(userStep);
            userStepResList.add(stepRes);
            
            if (Boolean.TRUE.equals(userStep.getIsSaved())) {
                completedSteps++;
            }
        }
        
        // 使用用户任务步骤列表替换原始步骤列表
        res.setStepList(userStepResList);
        res.setUserTaskProgress(completedSteps + "/" + totalSteps);
    }
    
    /**
     * 转换为任务步骤响应对象
     */
    private TaskStepPageRes convertToTaskStepPageRes(TaskStep step) {
        TaskStepPageRes stepRes = new TaskStepPageRes();
        stepRes.setId(step.getId());
        stepRes.setTaskId(step.getTaskId());
        stepRes.setTaskDescribe(step.getTaskDescribe());
        stepRes.setPrice(step.getPrice());
        stepRes.setSrot(step.getSrot());
        stepRes.setStepType(convertStringToStepTypeList(step.getStepType()));
        return stepRes;
    }
    
    /**
     * 转换为用户任务步骤响应对象
     */
    private TaskStepPageRes convertToUserTaskStepPageRes(UserTaskStep userStep) {
        TaskStepPageRes stepRes = new TaskStepPageRes();
        
        // 复制用户步骤信息（来自user_task_step表）
        stepRes.setId(userStep.getId()); // 使用用户步骤ID
        stepRes.setTaskId(userStep.getTaskId());
        stepRes.setTaskDescribe(userStep.getTaskDescribe());
        stepRes.setSrot(userStep.getSrot());
        stepRes.setStepType(convertStringToStepTypeList(userStep.getStepType()));
        
        // 设置用户步骤特有字段
        stepRes.setUserTaskStepId(userStep.getId());
        stepRes.setUserTaskId(userStep.getUserTaskId());
        stepRes.setTaskTextOperate(userStep.getTaskTextOperate());
        stepRes.setTaskImageOperate(convertStringToImageList(userStep.getTaskImageOperate()));
        stepRes.setIsSaved(userStep.getIsSaved());
        
        return stepRes;
    }
    
    /**
     * 将字符串转换为步骤类型字符串列表
     * 
     * @param stepTypeStr 步骤类型字符串，如 "text,image"
     * @return 步骤类型字符串列表
     */
    private List<String> convertStringToStepTypeList(String stepTypeStr) {
        if (stepTypeStr == null || stepTypeStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        String[] types = stepTypeStr.split(",");
        
        for (String type : types) {
            String trimmedType = type.trim();
            if (!trimmedType.isEmpty()) {
                result.add(trimmedType);
            }
        }
        
        return result;
    }
    
    /**
     * 将字符串转换为图片URL列表
     * 
     * @param imageStr 图片字符串，如 "url1,url2,url3"
     * @return 图片URL列表
     */
    private List<String> convertStringToImageList(String imageStr) {
        if (imageStr == null || imageStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        String[] images = imageStr.split(",");
        
        for (String image : images) {
            String trimmedImage = image.trim();
            if (!trimmedImage.isEmpty()) {
                result.add(trimmedImage);
            }
        }
        
        return result;
    }
} 