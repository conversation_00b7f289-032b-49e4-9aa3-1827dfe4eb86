package com.letu.solutions.customer.controller;

import com.letu.solutions.core.model.R;
import com.letu.solutions.customer.service.FinanceConfigService;
import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 财务配置
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/customer")
public class FinanceConfigController {

    private final FinanceConfigService financeConfigService;

    /**
     * 财务配置 根据network查询
     * 返回财务配置详情，包含钱包地址列表
     *
     * @param network 协议网络（如 ERC20, TRC20, BEP20）
     * @return 财务配置详情
     */
    @GetMapping("/financeConfig/selectByNetwork")
    public R<FinanceConfigDetailRes> selectByNetwork(@RequestParam("network") String network) {
        return R.success(financeConfigService.selectByNetwork(network));
    }
}
