package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.letu.solutions.customer.mapper.UserSocialMapper;
import com.letu.solutions.customer.service.UserSocialService;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.entity.cms.UserSocial;
import com.letu.solutions.model.request.user.UserSocialBindRequest;
import com.letu.solutions.model.response.user.UserSocialResponse;
import com.letu.solutions.model.enums.cms.SocialBindStatusEnum;
import com.letu.solutions.model.enums.cms.SocialTypeEnum;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.utils.LanguageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户社交媒体绑定服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserSocialServiceImpl extends ServiceImpl<UserSocialMapper, UserSocial> implements UserSocialService {

    @Override
    public String bindSocial(UserSocialBindRequest request, ExtendData extendData) {
        log.info("用户{}绑定社交媒体，类型：{}，链接：{}", extendData.getUserId(), request.getSocialType().getDesc(), request.getSocialUrl());

        // 查询用户是否已有该类型的绑定记录
        UserSocial existingSocial = baseMapper.selectOne(
                new LambdaQueryWrapper<UserSocial>()
                        .eq(UserSocial::getUserId, extendData.getUserId())
                        .eq(UserSocial::getSocialType, request.getSocialType())
        );

        if (existingSocial != null) {
            // 如果已经绑定，返回已绑定提示
            log.warn("用户{}尝试绑定已存在的社交媒体{}", extendData.getUserId(), request.getSocialType().getDesc());
            return LanguageUtil.trans(I18nConstants.SOCIAL_ALREADY_BOUND);
        }

        // 创建新记录，状态为VERIFIED
        UserSocial newSocial = UserSocial.builder()
                .userId(extendData.getUserId())
                .socialType(request.getSocialType())
                .socialUrl(request.getSocialUrl())
                .status(SocialBindStatusEnum.VERIFIED)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        
        boolean result = baseMapper.insert(newSocial) > 0;
        if (result) {
            log.info("用户{}新增社交媒体{}绑定成功", extendData.getUserId(), request.getSocialType().getDesc());
            return null; // 成功返回null
        } else {
            log.error("用户{}新增社交媒体{}绑定失败", extendData.getUserId(), request.getSocialType().getDesc());
            return LanguageUtil.trans(I18nConstants.SOCIAL_BIND_FAILED);
        }
    }

    @Override
    public UserSocialResponse getUserSocial(ExtendData extendData) {
        log.info("查询用户{}的社交媒体绑定信息", extendData.getUserId());

        // 查询用户的所有社交媒体绑定记录
        List<UserSocial> userSocials = baseMapper.selectList(
                new LambdaQueryWrapper<UserSocial>()
                        .eq(UserSocial::getUserId, extendData.getUserId())
                        .orderByDesc(UserSocial::getCreateTime)
        );

        UserSocialResponse response = new UserSocialResponse();
        response.setUserId(extendData.getUserId());

        // 转换为响应格式
        List<UserSocialResponse.SocialBinding> socialBindings = userSocials.stream()
                .map(social -> {
                    UserSocialResponse.SocialBinding binding = new UserSocialResponse.SocialBinding();
                    binding.setSocialType(social.getSocialType().getCode());
                    binding.setSocialTypeDesc(social.getSocialType().getDesc());
                    binding.setSocialUrl(social.getSocialUrl());
                    binding.setStatus(social.getStatus().getCode());
                    binding.setStatusDesc(social.getStatus().getDesc());
                    binding.setBindTime(social.getCreateTime());
                    binding.setVerifyTime(social.getVerifyTime());
                    return binding;
                })
                .collect(Collectors.toList());

        response.setSocialBindings(socialBindings);
        return response;
    }

    @Override
    public boolean verifySocial(SocialTypeEnum socialType, ExtendData extendData) {
        log.info("用户{}验证社交媒体{}绑定状态", extendData.getUserId(), socialType.getDesc());

        // 查询用户的该类型社交媒体绑定记录
        UserSocial userSocial = baseMapper.selectOne(
                new LambdaQueryWrapper<UserSocial>()
                        .eq(UserSocial::getUserId, extendData.getUserId())
                        .eq(UserSocial::getSocialType, socialType)
        );

        if (userSocial == null) {
            log.warn("用户{}没有找到{}类型的社交媒体绑定记录", extendData.getUserId(), socialType.getDesc());
            return false;
        }

        // 这里可以添加具体的验证逻辑
        // 例如：调用第三方API验证链接的有效性
        boolean isValid = validateSocialUrl(userSocial.getSocialUrl(), socialType.getCode());

        // 更新验证状态
        userSocial.setStatus(isValid ? SocialBindStatusEnum.VERIFIED : SocialBindStatusEnum.FAILED);
        userSocial.setVerifyTime(new Date());
        userSocial.setUpdateTime(new Date());

        boolean result = baseMapper.updateById(userSocial) > 0;
        log.info("用户{}验证社交媒体{}绑定状态，结果：{}", extendData.getUserId(), socialType.getDesc(), result);
        return result && isValid;
    }

    /**
     * 验证社交媒体链接有效性
     * 这里可以根据不同的社交媒体类型实现不同的验证逻辑
     */
    private boolean validateSocialUrl(String socialUrl, String socialType) {
        // TODO: 实现具体的验证逻辑
        // 例如：
        // 1. 检查URL格式是否正确
        // 2. 调用第三方API验证账号是否存在
        // 3. 检查账号是否公开可见
        
        // 暂时返回true，表示验证通过
        log.info("验证社交媒体链接：{}，类型：{}", socialUrl, socialType);
        return true;
    }
} 