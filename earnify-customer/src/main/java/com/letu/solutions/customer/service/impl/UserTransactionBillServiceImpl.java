package com.letu.solutions.customer.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.customer.mapper.UserTransactionBillMapper;
import com.letu.solutions.customer.service.UserTransactionBillService;
import com.letu.solutions.model.customer.request.UserAssetListReq;
import com.letu.solutions.model.customer.response.UserAssetPageRes;
import com.letu.solutions.model.entity.cms.UserTransactionBill;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * 用户账户流水表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserTransactionBillServiceImpl implements UserTransactionBillService {

    private final UserTransactionBillMapper userTransactionBillMapper;

    @Override
    public Page<UserAssetPageRes> getUserAssetPage(UserAssetListReq request, Long userId) {
        log.info("分页查询用户资产，用户ID：{}，请求参数：{}", userId, request);

        // 构建查询条件
        LambdaQueryWrapper<UserTransactionBill> queryWrapper = Wrappers.<UserTransactionBill>lambdaQuery()
                .eq(UserTransactionBill::getUserId, userId)
                // 按创建时间倒序排列
                .orderByDesc(UserTransactionBill::getCreateTime);

        // 分页查询
        Page<UserTransactionBill> page = userTransactionBillMapper.selectPage(request.getPage(), queryWrapper);

        // 转换为响应对象
        Page<UserAssetPageRes> resultPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        resultPage.setRecords(page.getRecords().stream()
                .map(this::convertToUserAssetPageRes)
                .collect(Collectors.toList()));

        log.info("查询完成，共{}条记录", resultPage.getTotal());
        return resultPage;
    }

    /**
     * 转换为用户资产响应对象
     */
    private UserAssetPageRes convertToUserAssetPageRes(UserTransactionBill bill) {
        return UserAssetPageRes.builder()
                .createTime(bill.getCreateTime())
                .initialAmount(bill.getBalanceBefore())
                .changeAmount(bill.getAmount())
                .side(bill.getSide())
                .balance(bill.getBalanceAfter())
                .fundTypeDesc(bill.getFundType() != null ? bill.getFundType().getDesc() : "")
                .build();
    }
}
