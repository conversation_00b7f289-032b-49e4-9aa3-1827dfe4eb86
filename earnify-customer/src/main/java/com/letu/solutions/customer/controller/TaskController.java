package com.letu.solutions.customer.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.customer.service.TaskService;
import com.letu.solutions.core.model.R;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.request.task.TaskListQueryRequest;
import com.letu.solutions.model.response.customer.TaskPageRes;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 首页任务相关接口
 * 提供任务分页查询、详情查询等功能，支持多条件筛选、排序和步骤明细返回。
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customer/task")
public class TaskController {
    @Autowired
    private TaskService taskService;

    /**
     * 分页查询任务列表（支持多条件筛选、排序）
     * @param queryRequest 查询参数，包含分页、类型、状态、名称、排序等
     * @return 分页结果，包含任务基本信息及分页元数据
     */
    @GetMapping("/page.e")
    public R<Page<TaskPageRes>> page(TaskListQueryRequest queryRequest) {
        return R.success(taskService.page(queryRequest.getPage(), queryRequest));
    }

    /**
     * 查询任务详情（包含任务步骤列表）
     * 支持匿名访问，如果用户已登录且已领取该任务，会返回用户的任务状态
     * @param id 任务ID
     * @param extendData 用户扩展信息（可选，支持匿名访问）
     * @return 任务详情及步骤明细，若无对应任务返回null
     */
    @GetMapping("/detail.e")
    public R<TaskPageRes> detail(@RequestParam("id") Long id, @RequestHeader(required = false) ExtendData extendData) {
        return R.success(taskService.getTaskDetail(id, extendData));
    }
} 