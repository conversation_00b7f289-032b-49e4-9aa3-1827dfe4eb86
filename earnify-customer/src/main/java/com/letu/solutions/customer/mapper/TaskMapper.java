package com.letu.solutions.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.model.entity.cms.Task;
import com.letu.solutions.model.request.task.TaskListQueryRequest;
import com.letu.solutions.model.response.customer.TaskPageRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TaskMapper extends BaseMapper<Task> {
    
    /**
     * 自定义分页查询任务列表
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<TaskPageRes> selectTaskPageResPage(@Param("page") Page page, @Param("request") TaskListQueryRequest request);

    int updateTaskReceiveNum(@Param("taskId") Long taskId);
} 