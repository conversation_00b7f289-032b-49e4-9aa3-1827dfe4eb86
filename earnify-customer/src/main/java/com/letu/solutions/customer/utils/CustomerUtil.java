package com.letu.solutions.customer.utils;

import cn.hutool.core.lang.Assert;
import com.letu.solutions.core.constant.Constants;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.model.entity.user.User;

import java.util.Objects;

public class CustomerUtil {
    /**
     * 获取用户ID
     * 此方法用于获取经过验证的用户对象的ID确保用户是活跃且未删除的
     *
     * @param user 用户对象，应包含用户的相关信息，如用户ID、启用状态和删除状态
     * @return 用户ID，如果用户状态不符合条件，则返回特定异常信息
     * <p>
     * 注意：此方法主要关注于确保用户是处于启用状态且未被删除的情况下获取用户ID
     * 它通过检查用户对象的enable和del字段来确保用户状态的有效性
     * 这种验证是必要的，因为它可以防止对不应被访问或不存在的用户数据进行操作
     */
    public static Long getUserId(User user) {
        if (Objects.nonNull(user)) {
            // 确保用户是启用状态且未被删除，否则抛出异常
            Assert.isFalse(user.getEnable() == Constants.ZERO, I18nConstants.ACCOUNT_DISABLE);
            Assert.isFalse(user.getDel() == Constants.NUMBER_1, I18nConstants.ACCOUNT_CANCEL);
            // 返回验证通过的用户ID
            return user.getId();
        }
        return null;
    }
    public static final String CONTENT = """
        Dear User,

        We kindly request you to use the following code for email verification purposes.

        Verification Code: %s

        Please note that this code will expire in 5 minutes and can only be used once. If you did not request an email verification code for Earnify, please disregard this email.

        Thank you for choosing Earnify.

        Best regards,

        Earnify Team""";

    public static final String SUBJECT = "Earnify - Email Verification Code";

}
