package com.letu.solutions.customer.service;

/**
 * 系统参数服务
 */
public interface SysParamService {

    /**
     * 根据key获取参数值
     * @param key 参数key
     * @return 参数值
     */
    String getValueByKey(String key);

    /**
     * 根据key获取参数值，如果不存在则返回默认值
     * @param key 参数key
     * @param defaultValue 默认值
     * @return 参数值
     */
    String getValueByKey(String key, String defaultValue);

    /**
     * 根据key获取整型参数值
     * @param key 参数key
     * @param defaultValue 默认值
     * @return 整型参数值
     */
    Integer getIntValueByKey(String key, Integer defaultValue);

    /**
     * 根据key获取长整型参数值
     * @param key 参数key
     * @param defaultValue 默认值
     * @return 长整型参数值
     */
    Long getLongValueByKey(String key, Long defaultValue);
} 