package com.letu.solutions.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.request.task.TaskListQueryRequest;
import com.letu.solutions.model.response.customer.TaskPageRes;

public interface TaskService {
    /**
     * 任务展示列表分页查询（支持多条件筛选、排序、完成时间范围）
     * @param queryRequest 查询参数，支持发布时间、奖励数量、完成时间等
     * @return 分页结果，包含任务基本信息及分页元数据
     */
    Page<TaskPageRes> page(Page<TaskPageRes> page, TaskListQueryRequest queryRequest);

    /**
     * 查询任务详情（包含任务步骤列表）
     * 支持匿名访问，如果用户已登录且已领取该任务，会返回用户的任务状态
     * @param id 任务ID
     * @param extendData 用户扩展信息（可选，支持匿名访问）
     * @return 任务详情及步骤明细，若无对应任务返回null
     */
    TaskPageRes getTaskDetail(Long id, ExtendData extendData);
}