package com.letu.solutions.customer.service;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.model.request.user.UserWalletBindRequest;
import com.letu.solutions.model.response.user.UserWalletResponse;
import com.letu.solutions.model.enums.cms.WalletTypeEnum;

/**
 * 用户钱包绑定服务接口
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface UserWalletService {

    /**
     * 绑定钱包
     *
     * @param request 绑定请求
     * @param extendData 扩展数据
     * @return 成功返回null，失败返回错误消息
     */
    String bindWallet(UserWalletBindRequest request, ExtendData extendData);

    /**
     * 查询用户钱包绑定信息
     *
     * @param extendData 扩展数据
     * @return 钱包绑定信息
     */
    UserWalletResponse getUserWallet(ExtendData extendData);

    /**
     * 设置默认钱包
     *
     * @param walletId 钱包ID
     * @param extendData 扩展数据
     * @return 成功返回null，失败返回错误消息
     */
    String setDefaultWallet(Long walletId, ExtendData extendData);

    /**
     * 删除钱包绑定
     *
     * @param walletId 钱包ID
     * @param extendData 扩展数据
     * @return 成功返回null，失败返回错误消息
     */
    String deleteWallet(Long walletId, ExtendData extendData);
} 