package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.customer.mapper.FinanceConfigMapper;
import com.letu.solutions.customer.mapper.WithdrawRecordMapper;
import com.letu.solutions.customer.mapper.UserAccountBalanceMapper;
import com.letu.solutions.customer.mapper.UserTransactionBillMapper;
import com.letu.solutions.customer.service.FinanceConfigService;
import com.letu.solutions.customer.service.UserService;
import com.letu.solutions.customer.service.WithdrawService;
import com.letu.solutions.model.cms.response.cms.FinanceConfigDetailRes;
import com.letu.solutions.model.customer.request.WithdrawApplyReq;
import com.letu.solutions.model.customer.request.WithdrawEstimateReq;
import com.letu.solutions.model.customer.response.WithdrawEstimateRes;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.model.entity.cms.UserTransactionBill;
import com.letu.solutions.model.entity.user.User;
import com.letu.solutions.model.enums.cms.*;
import com.letu.solutions.model.response.login.UserResponse;
import com.letu.solutions.util.constants.CurrencyConstants;
import com.letu.solutions.util.util.FinanceConfigCacheUtil;
import com.letu.solutions.util.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.RequestHeader;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 提现服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WithdrawServiceImpl implements WithdrawService {

    private final FinanceConfigService financeConfigService;
    private final WithdrawRecordMapper withdrawRecordMapper;
    private final UserAccountBalanceMapper userAccountBalanceMapper;
    private final UserTransactionBillMapper userTransactionBillMapper;
    private final FinanceConfigCacheUtil financeConfigCacheUtil;
    private final TransactionTemplate transactionTemplate;
    private final UserService userService;

    @Override
    public WithdrawEstimateRes estimateWithdraw(WithdrawEstimateReq request) {
        log.info("计算预计到账金额，network：{}，amount：{}", request.getNetwork(), request.getAmount());

        // 先从缓存查询财务配置
        FinanceConfigDetailRes config = financeConfigService.selectByNetwork(request.getNetwork());

        Assert.notNull(config, "该网络暂不支持提现");
//        Assert.isTrue(config.getIsActive(), "该网络已停用");

        // 验证提现金额范围
        validateWithdrawAmount(request.getAmount(), config);

        // 计算手续费和到账金额
        return calculateWithdrawEstimate(request.getAmount(), request.getNetwork(), config);
    }

    @Override
    public Boolean applyWithdraw(WithdrawApplyReq request, ExtendData extendData) {
        // 查询财务配置
        FinanceConfigDetailRes financeConfig = financeConfigService.selectByNetwork(request.getNetwork());
        Assert.notNull(financeConfig, "该网络暂不支持提现");

        Long userId = extendData.getUserId();
        String userName = extendData.getAuthentication().getUserName();
        log.info("用户申请提现，userId：{}，network：{}，amount：{}", userId, request.getNetwork(), request.getAmount());


        UserResponse user = userService.getUserBaseUser(userId);
        // 统计用户当日提现次数
        String today = DateUtil.today();
        int withdrawTimesToday = countUserWithdrawToday(userId, request.getNetwork(), today);


//        Assert.isTrue(financeConfig.getIsActive(), "该网络已停用");

        // 验证提现金额范围
        validateWithdrawAmount(request.getAmount(), financeConfig);

        // 验证每日提现次数限制（假设最多5次）
        Assert.isTrue(withdrawTimesToday < financeConfig.getMaxWithdrawTimesPerDay(), "今日提现次数已达上限");

        // 验证提现地址格式
//        validateWithdrawAddress(request.getWithdrawAddress(), request.getNetwork());

        // 查询用户账户余额
        UserAccountBalance userAccount = getUserAccountBalance(userId);
        Assert.notNull(userAccount, "用户账户不存在");
        Assert.isTrue(userAccount.getAvailableAmount().compareTo(request.getAmount()) >= 0, "账户余额不足");

        // 使用编程式事务处理提现申请
        return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            try {
                // 计算手续费
                WithdrawEstimateRes estimate = calculateWithdrawEstimate(request.getAmount(), request.getNetwork(), financeConfig);

                // 1. 冻结用户账户余额（可用资金减少，冻结资金增加）
                freezeUserAccountBalance(userId, request.getAmount());

                // 2. 生成用户流水记录
                writeUserWithdrawTransactionBill(user, request.getAmount(), userAccount);

                // 3. 保存提现记录
                WithdrawRecord withdrawRecord = createWithdrawRecord(request,user, financeConfig,estimate.getFeeAmount());
                boolean result = withdrawRecordMapper.insert(withdrawRecord) > 0;

                if (result) {
                    log.info("提现申请成功，提现记录ID：{}", withdrawRecord.getId());
                } else {
                    log.error("提现申请失败，用户ID：{}", userId);
                    throw new RuntimeException("保存提现记录失败");
                }

                return result;
            } catch (Exception e) {
                log.error("提现申请事务执行失败，userId：{}，error：{}", userId, e.getMessage(), e);
                status.setRollbackOnly();
                throw new RuntimeException("提现申请失败：" + e.getMessage());
            }
        }));
    }



    /**
     * 统计用户当日提现次数
     */
    private int countUserWithdrawToday(Long userId, String network, String today) {
        LambdaQueryWrapper<WithdrawRecord> queryWrapper = Wrappers.<WithdrawRecord>lambdaQuery()
                .eq(WithdrawRecord::getUserId, userId)
                .eq(WithdrawRecord::getNetwork, network)
                .eq(WithdrawRecord::getDay, Integer.parseInt(today.replace("-", "")))
                .eq(WithdrawRecord::getWithdrawStatus, WithdrawStatusEnum.completed);
        return Math.toIntExact(withdrawRecordMapper.selectCount(queryWrapper));
    }

    /**
     * 验证提现金额范围（FinanceConfigDetailRes版本）
     */
    private void validateWithdrawAmount(BigDecimal amount, FinanceConfigDetailRes config) {
        Assert.isTrue(config.getMinWithdraw() == null || amount.compareTo(config.getMinWithdraw()) >= 0,
                "提现金额不能小于最小提现金额：" + config.getMinWithdraw());
        Assert.isTrue(config.getMaxWithdraw() == null || amount.compareTo(config.getMaxWithdraw()) <= 0,
                "提现金额不能大于最大提现金额：" + config.getMaxWithdraw());
    }


    /**
     * 验证提现地址格式
     */
    private void validateWithdrawAddress(String address, String network) {
        Assert.hasText(address, "提现地址不能为空");
        // 根据不同网络验证地址格式
        switch (network) {
            case "TRC20" -> Assert.isTrue(address.startsWith("T") && address.length() == 34, "TRC20地址格式不正确");
            case "ERC20" -> Assert.isTrue(address.startsWith("0x") && address.length() == 42, "ERC20地址格式不正确");
            case "BEP20" -> Assert.isTrue(address.startsWith("0x") && address.length() == 42, "BEP20地址格式不正确");
            default -> log.warn("未知网络类型，跳过地址格式验证：{}", network);
        }
    }

    /**
     * 查询用户账户余额
     */
    private UserAccountBalance getUserAccountBalance(Long userId) {
        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(UserAccountBalance::getUserId, userId)
                .eq(UserAccountBalance::getCurrency, "USDT");
        return userAccountBalanceMapper.selectOne(queryWrapper);
    }

    /**
     * 冻结用户账户余额（可用资金减少，冻结资金增加）
     */
    private void freezeUserAccountBalance(Long userId, BigDecimal amount) {
        UserAccountBalance userAccount = getUserAccountBalance(userId);
        Assert.notNull(userAccount, "用户账户不存在");
        Assert.isTrue(userAccount.getAvailableAmount().compareTo(amount) >= 0, "账户余额不足");

        // 更新账户余额
        userAccount.setAvailableAmount(userAccount.getAvailableAmount().subtract(amount));
        userAccount.setFrozenAmount(userAccount.getFrozenAmount().add(amount));
        userAccount.setUpdateTime(new Date());

        int updateResult = userAccountBalanceMapper.updateById(userAccount);
        Assert.isTrue(updateResult > 0, "更新用户账户余额失败");
    }

    /**
     * 生成用户提现申请流水（资金冻结）
     */
    private void writeUserWithdrawTransactionBill(UserResponse user, BigDecimal amount, UserAccountBalance userAccount) {
        UserTransactionBill bill = new UserTransactionBill();
        bill.setUserId(user.getId());
        bill.setUserName(user.getNickName());
        bill.setAccountType(AccountTypeEnum.provider);
        bill.setFundType(FundTypeEnum.withdraw_freeze);
        bill.setSide(FundSideTypeEnum.out);
        bill.setAmount(amount);
        bill.setCurrency(CurrencyConstants.COIN_USDT);
        bill.setBalanceBefore(userAccount.getAvailableAmount()); // 冻结前的余额
        bill.setBalanceAfter(userAccount.getAvailableAmount().subtract(amount)); // 冻结后的余额
        bill.setRemark(FundTypeEnum.withdraw_freeze.getDesc());
        bill.setDay(TimeUtil.today(new Date()));

        int insertResult = userTransactionBillMapper.insert(bill);
        Assert.isTrue(insertResult > 0, "生成用户流水记录失败");
    }

    /**
     * 计算预计到账金额
     * 计算公式：到账数量 = 提现金额 - 提现金额 * 手续费率 - 每笔提现费用
     */
    private WithdrawEstimateRes calculateWithdrawEstimate(BigDecimal withdrawAmount, String network, FinanceConfigDetailRes config) {
        // 手续费率（默认为0）
        BigDecimal feeRate = config.getFeeRate()
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        // 每笔提现费用（默认为0）
        BigDecimal perWithdrawFee = ObjectUtil.defaultIfNull(config.getFixedFee(), BigDecimal.ZERO);

        // 计算手续费金额：提现金额 * 手续费率
        BigDecimal feeAmount = withdrawAmount.multiply(feeRate).setScale(2, RoundingMode.HALF_UP);

        // 计算预计到账金额：提现金额 - 手续费金额 - 每笔提现费用
        BigDecimal estimatedAmount = withdrawAmount.subtract(feeAmount).subtract(perWithdrawFee);

        // 确保到账金额不为负数
        if (estimatedAmount.compareTo(BigDecimal.ZERO) < 0) {
            estimatedAmount = BigDecimal.ZERO;
        }

        return WithdrawEstimateRes.builder()
                .withdrawAmount(withdrawAmount)
                .feeRate(feeRate)
                .feeAmount(feeAmount)
                .perWithdrawFee(perWithdrawFee)
                .estimatedAmount(estimatedAmount.setScale(2, RoundingMode.HALF_UP))
                .network(network)
                .build();
    }

    /**
     * 创建提现记录
     */
    private WithdrawRecord createWithdrawRecord(WithdrawApplyReq request,UserResponse user, FinanceConfigDetailRes financeConfig,BigDecimal feeAmount) {
        return WithdrawRecord.builder()
                .userId(user.getId())
                .amount(request.getAmount())
                .username(user.getNickName())
                .withdrawStatus(WithdrawStatusEnum.unprocessed)
                .accountType(AccountTypeEnum.provider)
                .actionType(ActionTypeEnum.user)
                .currency(CurrencyConstants.COIN_USDT)
                .feeRate(financeConfig.getFeeRate())
                .fixedFee(financeConfig.getFixedFee())
                .day(TimeUtil.today(new Date()))
                .network(financeConfig.getNetwork())
                .toAddress(request.getWithdrawAddress())
                .fee(feeAmount).build();
    }
}
