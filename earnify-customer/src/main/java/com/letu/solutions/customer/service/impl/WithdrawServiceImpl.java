package com.letu.solutions.customer.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.customer.mapper.FinanceConfigMapper;
import com.letu.solutions.customer.mapper.WithdrawRecordMapper;
import com.letu.solutions.customer.service.WithdrawService;
import com.letu.solutions.model.customer.request.WithdrawApplyReq;
import com.letu.solutions.model.customer.request.WithdrawEstimateReq;
import com.letu.solutions.model.customer.response.WithdrawEstimateRes;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import com.letu.solutions.model.enums.cms.WithdrawStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 提现服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WithdrawServiceImpl implements WithdrawService {

    private final FinanceConfigMapper financeConfigMapper;
    private final WithdrawRecordMapper withdrawRecordMapper;

    @Override
    public WithdrawEstimateRes estimateWithdraw(WithdrawEstimateReq request) {
        log.info("计算预计到账金额，network：{}，amount：{}", request.getNetwork(), request.getAmount());

        // 查询提现配置
        FinanceConfig config = getFinanceConfigByNetwork(request.getNetwork());
        Assert.notNull(config, "该网络暂不支持提现");

        // 验证提现金额范围
        validateWithdrawAmount(request.getAmount(), config);

        // 计算手续费和到账金额
        return calculateWithdrawEstimate(request.getAmount(), request.getNetwork(), config);
    }

    @Override
    public Boolean applyWithdraw(WithdrawApplyReq request, Long userId) {
        log.info("用户申请提现，userId：{}，network：{}，amount：{}", userId, request.getNetwork(), request.getAmount());

        // 查询提现配置
        FinanceConfig config = getFinanceConfigByNetwork(request.getNetwork());
        Assert.notNull(config, "该网络暂不支持提现");

        // 验证提现金额范围
        validateWithdrawAmount(request.getAmount(), config);

        // TODO: 验证用户余额是否充足
        // TODO: 验证提现地址格式
        // TODO: 检查用户是否有未完成的提现申请

        // 创建提现记录
        WithdrawRecord withdrawRecord = createWithdrawRecord(request, userId, config);
        
        // 保存提现记录
        boolean result = withdrawRecordMapper.insert(withdrawRecord) > 0;
        
        if (result) {
            log.info("提现申请成功，提现记录ID：{}", withdrawRecord.getId());
            // TODO: 发送提现申请通知
            // TODO: 冻结用户余额
        } else {
            log.error("提现申请失败，用户ID：{}", userId);
        }

        return result;
    }

    /**
     * 根据网络查询财务配置
     */
    private FinanceConfig getFinanceConfigByNetwork(String network) {
        LambdaQueryWrapper<FinanceConfig> queryWrapper = Wrappers.<FinanceConfig>lambdaQuery()
                .eq(FinanceConfig::getNetwork, network) ;
        return financeConfigMapper.selectOne(queryWrapper);
    }

    /**
     * 验证提现金额范围
     */
    private void validateWithdrawAmount(BigDecimal amount, FinanceConfig config) {
        if (config.getMinWithdraw() != null && amount.compareTo(config.getMinWithdraw()) < 0) {
            throw new IllegalArgumentException("提现金额不能小于最小提现金额：" + config.getMinWithdraw());
        }
        if (config.getMaxWithdraw() != null && amount.compareTo(config.getMaxWithdraw()) > 0) {
            throw new IllegalArgumentException("提现金额不能大于最大提现金额：" + config.getMaxWithdraw());
        }
    }

    /**
     * 计算预计到账金额
     * 计算公式：到账数量 = 提现金额 - 提现金额 * 手续费率 - 每笔提现费用
     */
    private WithdrawEstimateRes calculateWithdrawEstimate(BigDecimal withdrawAmount, String network, FinanceConfig config) {
        // 手续费率（默认为0）
        BigDecimal feeRate = ObjectUtil.defaultIfNull(config.getFeeRate(), BigDecimal.ZERO);
        
        // 每笔提现费用（默认为0）
        BigDecimal perWithdrawFee = ObjectUtil.defaultIfNull(config.getFixedFee(), BigDecimal.ZERO);
        
        // 计算手续费金额：提现金额 * 手续费率
        BigDecimal feeAmount = withdrawAmount.multiply(feeRate).setScale(8, RoundingMode.DOWN);
        
        // 计算预计到账金额：提现金额 - 手续费金额 - 每笔提现费用
        BigDecimal estimatedAmount = withdrawAmount.subtract(feeAmount).subtract(perWithdrawFee);
        
        // 确保到账金额不为负数
        if (estimatedAmount.compareTo(BigDecimal.ZERO) < 0) {
            estimatedAmount = BigDecimal.ZERO;
        }

        return WithdrawEstimateRes.builder()
                .withdrawAmount(withdrawAmount)
                .feeRate(feeRate)
                .feeAmount(feeAmount)
                .perWithdrawFee(perWithdrawFee)
                .estimatedAmount(estimatedAmount.setScale(8, RoundingMode.DOWN))
                .network(network)
                .build();
    }

    /**
     * 创建提现记录
     */
    private WithdrawRecord createWithdrawRecord(WithdrawApplyReq request, Long userId, FinanceConfig config) {
        WithdrawRecord record = new WithdrawRecord();
        record.setUserId(userId);
        record.setNetwork(request.getNetwork());
        record.setAmount(request.getAmount());
//        record.setWithdrawAddress(request.getWithdrawAddress());
//        record.setStatus(WithdrawStatusEnum.PENDING);
//
//        // 计算手续费信息
//        WithdrawEstimateRes estimate = calculateWithdrawEstimate(request.getAmount(), request.getNetwork(), config);
//        record.setFee(estimate.getFeeAmount().add(estimate.getPerWithdrawFee()));
//        record.setActualAmount(estimate.getEstimatedAmount());
        
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        
        return record;
    }
}
