package com.letu.solutions.customer.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.customer.mapper.FinanceConfigMapper;
import com.letu.solutions.customer.mapper.WithdrawRecordMapper;
import com.letu.solutions.customer.mapper.UserAccountBalanceMapper;
import com.letu.solutions.customer.mapper.UserTransactionBillMapper;
import com.letu.solutions.customer.service.WithdrawService;
import com.letu.solutions.model.customer.request.WithdrawApplyReq;
import com.letu.solutions.model.customer.request.WithdrawEstimateReq;
import com.letu.solutions.model.customer.response.WithdrawEstimateRes;
import com.letu.solutions.model.customer.response.FinanceConfigDetailRes;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import com.letu.solutions.model.entity.cms.WithdrawRecord;
import com.letu.solutions.model.entity.cms.UserAccountBalance;
import com.letu.solutions.model.entity.cms.UserTransactionBill;
import com.letu.solutions.model.enums.cms.WithdrawStatusEnum;
import com.letu.solutions.model.enums.cms.FundTypeEnum;
import com.letu.solutions.model.enums.cms.FundSideTypeEnum;
import com.letu.solutions.model.enums.cms.AccountTypeEnum;
import com.letu.solutions.util.util.FinanceConfigCacheUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 提现服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WithdrawServiceImpl implements WithdrawService {

    private final FinanceConfigMapper financeConfigMapper;
    private final WithdrawRecordMapper withdrawRecordMapper;
    private final UserAccountBalanceMapper userAccountBalanceMapper;
    private final UserTransactionBillMapper userTransactionBillMapper;
    private final FinanceConfigCacheUtil financeConfigCacheUtil;
    private final TransactionTemplate transactionTemplate;

    @Override
    public WithdrawEstimateRes estimateWithdraw(WithdrawEstimateReq request) {
        log.info("计算预计到账金额，network：{}，amount：{}", request.getNetwork(), request.getAmount());

        // 查询提现配置
        FinanceConfig config = getFinanceConfigByNetwork(request.getNetwork());
        Assert.notNull(config, "该网络暂不支持提现");

        // 验证提现金额范围
        validateWithdrawAmount(request.getAmount(), config);

        // 计算手续费和到账金额
        return calculateWithdrawEstimate(request.getAmount(), request.getNetwork(), config);
    }

    @Override
    public Boolean applyWithdraw(WithdrawApplyReq request, Long userId) {
        log.info("用户申请提现，userId：{}，network：{}，amount：{}", userId, request.getNetwork(), request.getAmount());

        // 统计用户当日提现次数
        String today = DateUtil.today();
        int withdrawTimesToday = countUserWithdrawToday(userId, request.getNetwork(), today);

        // 查询财务配置
        FinanceConfigDetailRes financeConfig = financeConfigCacheUtil.getConfig(request.getNetwork());
        Assert.notNull(financeConfig, "该网络暂不支持提现");
        Assert.isTrue(financeConfig.getIsActive(), "该网络已停用");

        // 验证提现金额范围
        validateWithdrawAmount(request.getAmount(), financeConfig);

        // 验证每日提现次数限制（假设最多5次）
        Assert.isTrue(withdrawTimesToday < 5, "今日提现次数已达上限");

        // 验证提现地址格式
        validateWithdrawAddress(request.getWithdrawAddress(), request.getNetwork());

        // 查询用户账户余额
        UserAccountBalance userAccount = getUserAccountBalance(userId);
        Assert.notNull(userAccount, "用户账户不存在");
        Assert.isTrue(userAccount.getAvailableAmount().compareTo(request.getAmount()) >= 0, "账户余额不足");

        // 使用编程式事务处理提现申请
        return Boolean.TRUE.equals(transactionTemplate.execute(status -> {
            try {
                // 计算手续费
                WithdrawEstimateRes estimate = calculateWithdrawEstimate(request.getAmount(), request.getNetwork(), financeConfig);

                // 1. 冻结用户账户余额（可用资金减少，冻结资金增加）
                freezeUserAccountBalance(userId, request.getAmount());

                // 2. 生成用户流水记录
                writeUserWithdrawTransactionBill(userId, request.getAmount(), userAccount);

                // 3. 保存提现记录
                WithdrawRecord withdrawRecord = createWithdrawRecord(request, userId, estimate);
                boolean result = withdrawRecordMapper.insert(withdrawRecord) > 0;

                if (result) {
                    log.info("提现申请成功，提现记录ID：{}", withdrawRecord.getId());
                } else {
                    log.error("提现申请失败，用户ID：{}", userId);
                    throw new RuntimeException("保存提现记录失败");
                }

                return result;
            } catch (Exception e) {
                log.error("提现申请事务执行失败，userId：{}，error：{}", userId, e.getMessage(), e);
                status.setRollbackOnly();
                throw new RuntimeException("提现申请失败：" + e.getMessage());
            }
        }));
    }

    /**
     * 统计用户当日提现次数
     */
    private int countUserWithdrawToday(Long userId, String network, String today) {
        LambdaQueryWrapper<WithdrawRecord> queryWrapper = Wrappers.<WithdrawRecord>lambdaQuery()
                .eq(WithdrawRecord::getUserId, userId)
                .eq(WithdrawRecord::getNetwork, network)
                .eq(WithdrawRecord::getDay, Integer.parseInt(today.replace("-", "")))
                .ne(WithdrawRecord::getStatus, WithdrawStatusEnum.CANCELLED);
        return Math.toIntExact(withdrawRecordMapper.selectCount(queryWrapper));
    }

    /**
     * 验证提现金额范围
     */
    private void validateWithdrawAmount(BigDecimal amount, FinanceConfigDetailRes config) {
        Assert.isTrue(config.getMinWithdrawAmount() == null || amount.compareTo(config.getMinWithdrawAmount()) >= 0,
                "提现金额不能小于最小提现金额：" + config.getMinWithdrawAmount());
        Assert.isTrue(config.getMaxWithdrawAmount() == null || amount.compareTo(config.getMaxWithdrawAmount()) <= 0,
                "提现金额不能大于最大提现金额：" + config.getMaxWithdrawAmount());
    }

    /**
     * 验证提现地址格式
     */
    private void validateWithdrawAddress(String address, String network) {
        Assert.hasText(address, "提现地址不能为空");
        // 根据不同网络验证地址格式
        switch (network) {
            case "TRC20" -> Assert.isTrue(address.startsWith("T") && address.length() == 34, "TRC20地址格式不正确");
            case "ERC20" -> Assert.isTrue(address.startsWith("0x") && address.length() == 42, "ERC20地址格式不正确");
            case "BEP20" -> Assert.isTrue(address.startsWith("0x") && address.length() == 42, "BEP20地址格式不正确");
            default -> log.warn("未知网络类型，跳过地址格式验证：{}", network);
        }
    }

    /**
     * 查询用户账户余额
     */
    private UserAccountBalance getUserAccountBalance(Long userId) {
        LambdaQueryWrapper<UserAccountBalance> queryWrapper = Wrappers.<UserAccountBalance>lambdaQuery()
                .eq(UserAccountBalance::getUserId, userId)
                .eq(UserAccountBalance::getCurrency, "USDT");
        return userAccountBalanceMapper.selectOne(queryWrapper);
    }

    /**
     * 冻结用户账户余额（可用资金减少，冻结资金增加）
     */
    private void freezeUserAccountBalance(Long userId, BigDecimal amount) {
        UserAccountBalance userAccount = getUserAccountBalance(userId);
        Assert.notNull(userAccount, "用户账户不存在");
        Assert.isTrue(userAccount.getAvailableAmount().compareTo(amount) >= 0, "账户余额不足");

        // 更新账户余额
        userAccount.setAvailableAmount(userAccount.getAvailableAmount().subtract(amount));
        userAccount.setFrozenAmount(userAccount.getFrozenAmount().add(amount));
        userAccount.setUpdateTime(new Date());

        int updateResult = userAccountBalanceMapper.updateById(userAccount);
        Assert.isTrue(updateResult > 0, "更新用户账户余额失败");
    }

    /**
     * 生成用户提现申请流水（资金冻结）
     */
    private void writeUserWithdrawTransactionBill(Long userId, BigDecimal amount, UserAccountBalance userAccount) {
        UserTransactionBill bill = new UserTransactionBill();
        bill.setUserId(userId);
        bill.setAccountType(AccountTypeEnum.CLIENT);
        bill.setFundType(FundTypeEnum.WITHDRAW);
        bill.setSide(FundSideTypeEnum.OUT);
        bill.setAmount(amount);
        bill.setBalanceBefore(userAccount.getAvailableAmount().add(amount)); // 冻结前的余额
        bill.setBalanceAfter(userAccount.getAvailableAmount()); // 冻结后的余额
        bill.setCreateTime(new Date());
        bill.setUpdateTime(new Date());
        bill.setDay(Integer.parseInt(DateUtil.today().replace("-", "")));

        int insertResult = userTransactionBillMapper.insert(bill);
        Assert.isTrue(insertResult > 0, "生成用户流水记录失败");
    }

    /**
     * 计算预计到账金额
     * 计算公式：到账数量 = 提现金额 - 提现金额 * 手续费率 - 每笔提现费用
     */
    private WithdrawEstimateRes calculateWithdrawEstimate(BigDecimal withdrawAmount, String network, FinanceConfigDetailRes config) {
        // 手续费率（默认为0）
        BigDecimal feeRate = ObjectUtil.defaultIfNull(config.getWithdrawFeeRate(), BigDecimal.ZERO);

        // 每笔提现费用（默认为0）
        BigDecimal perWithdrawFee = ObjectUtil.defaultIfNull(config.getPerWithdrawFee(), BigDecimal.ZERO);

        // 计算手续费金额：提现金额 * 手续费率
        BigDecimal feeAmount = withdrawAmount.multiply(feeRate).setScale(8, RoundingMode.DOWN);

        // 计算预计到账金额：提现金额 - 手续费金额 - 每笔提现费用
        BigDecimal estimatedAmount = withdrawAmount.subtract(feeAmount).subtract(perWithdrawFee);

        // 确保到账金额不为负数
        if (estimatedAmount.compareTo(BigDecimal.ZERO) < 0) {
            estimatedAmount = BigDecimal.ZERO;
        }

        return WithdrawEstimateRes.builder()
                .withdrawAmount(withdrawAmount)
                .feeRate(feeRate)
                .feeAmount(feeAmount)
                .perWithdrawFee(perWithdrawFee)
                .estimatedAmount(estimatedAmount.setScale(8, RoundingMode.DOWN))
                .network(network)
                .build();
    }

    /**
     * 创建提现记录
     */
    private WithdrawRecord createWithdrawRecord(WithdrawApplyReq request, Long userId, WithdrawEstimateRes estimate) {
        WithdrawRecord record = new WithdrawRecord();
        record.setUserId(userId);
        record.setNetwork(request.getNetwork());
        record.setAmount(request.getAmount());
        record.setWithdrawAddress(request.getWithdrawAddress());
        record.setStatus(WithdrawStatusEnum.PENDING);
        record.setFee(estimate.getFeeAmount().add(estimate.getPerWithdrawFee()));
        record.setActualAmount(estimate.getEstimatedAmount());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDay(Integer.parseInt(DateUtil.today().replace("-", "")));

        return record;
    }
}
