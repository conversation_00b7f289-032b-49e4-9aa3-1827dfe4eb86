package com.letu.solutions.customer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.core.configuration.RegisterConfiguration;
import com.letu.solutions.core.configuration.WhiteConfiguration;
import com.letu.solutions.core.constant.CacheConstant;
import com.letu.solutions.core.enums.BusinessTypeEnum;
import com.letu.solutions.core.enums.OsEnum;
import com.letu.solutions.core.exception.ThrowException;
import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.core.utils.LockUtil;
import com.letu.solutions.core.utils.NickNameUtils;
import com.letu.solutions.customer.mapper.UserMapper;
import com.letu.solutions.customer.service.LoginService;
import com.letu.solutions.customer.service.UserService;
import com.letu.solutions.dubbo.middle.customer.MessageFacade;
import com.letu.solutions.dubbo.middle.customer.UserFacade;
import com.letu.solutions.dubbo.earnify.util.ExtendCovert;
import com.letu.solutions.model.entity.user.User;
import com.letu.solutions.model.request.EmptyRequest;
import com.letu.solutions.model.request.login.CertRegisterRequest;
import com.letu.solutions.model.request.login.LoginRequest;
import com.letu.solutions.model.request.login.RegisterRequest;
import com.letu.solutions.model.response.login.UserResponse;
import com.letu.solutions.model.response.user.LoginResponse;
import com.letu.solutions.share.model.enums.MessageEnum;
import com.letu.solutions.share.model.enums.user.UserStatusEnum;
import com.letu.solutions.share.model.request.user.*;
import com.letu.solutions.util.util.CertThreeUtil;
import com.letu.solutions.util.util.TimeUtil;
import com.letu.solutions.util.util.TokenUtil;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.utils.LanguageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 登录/注册
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LoginServiceImpl implements LoginService {
    private final WhiteConfiguration whiteConfiguration;
    @DubboReference(check = false)
    private MessageFacade messageFacade;
    @DubboReference(check = false)
    private UserFacade userFacade;
    @Resource
    @Lazy
    private UserService userService;
    private final UserMapper userMapper;
    private final TokenUtil tokenUtil;
    private final TransactionalManage transactionalManage;
    private final CertThreeUtil certThreeUtil;
    private final LockUtil lockUtil;

    @Resource
    private RedisTemplate<String, String> stringRedisTemplate;

    private final RegisterConfiguration registerConfiguration;
    private final RedisTemplate<String, String> redisTemplate;

    @Override
    public void sms(String phone, MessageEnum businessType, ExtendData extendData) {
        Assert.isFalse(businessType == MessageEnum.CUS_SMS_CODE_REG, "手机号不合法");
        Assert.isTrue(StrUtil.isNotEmpty(phone), "手机号不合法");
        boolean whitePhone = CollectionUtil.isNotEmpty(whiteConfiguration.getWhitePhone()) && whiteConfiguration.getWhitePhone().contains(phone);
        String smsCode = (!whiteConfiguration.isLoginCheck() || whitePhone) ? "1234" : RandomUtil.randomNumbers(4);
        // 校验次数是否超限
        String beginIpStr = Ipv4Util.getBeginIpStr(extendData.getIpAddress(), 16);
        if (!whiteConfiguration.getWhiteIps().contains(extendData.getIpAddress()) && !whiteConfiguration.getWhiteIps().contains(beginIpStr)) {
            lockUtil.checkLock(MessageEnum.CUS_SMS_CODE_CER.name(), extendData.getIpAddress());
            lockUtil.checkLock(MessageEnum.CUS_SMS_CODE_CER.name(), phone);
        }

        // redis存key做映射
        if (whitePhone) {
            stringRedisTemplate.opsForValue().set(String.format(CacheConstant.cusSms, businessType.name(), phone), smsCode);
        } else {
            stringRedisTemplate.opsForValue().set(String.format(CacheConstant.cusSms, businessType.name(), phone), smsCode, 5, TimeUnit.MINUTES);
        }
        // 发送短信 MessageFacadeImpl 对参数有判断
//        messageFacade.send(businessType, extendData, phone, smsCode);
        messageFacade.send(businessType, phone, smsCode);
    }

    @Override
    public void checkSms(String phone, String code, MessageEnum messageEnum, ExtendData extendData) {
        if (null == messageEnum) {
            messageEnum = MessageEnum.CUS_SMS_CODE_CER;
        }
        // 获取并删除库中验证码
        String redisKey = String.format(String.format(CacheConstant.cusSms, messageEnum.name(), phone));
        String cacheSmsCode = stringRedisTemplate.opsForValue().get(redisKey);
        boolean checkRes = code.equals(cacheSmsCode);
        if (!checkRes) {
            boolean lock = lockUtil.lock(LockUtil.LockKey.sms, messageEnum.name() + ":" + phone);
            // 连续错误5次 删除旧验证码
            if (!lock) {
                stringRedisTemplate.delete(redisKey);
                lockUtil.deleteLock(LockUtil.LockKey.sms, messageEnum.name() + ":" + phone);
                throw new ThrowException("#请重新获取验证码");
            }
            throw new ThrowException("#验证码有误，请重新验证");
        }
        boolean whitePhone = CollectionUtil.isNotEmpty(whiteConfiguration.getWhitePhone()) && whiteConfiguration.getWhitePhone().contains(phone);
        whitePhone = (!whiteConfiguration.isLoginCheck() || whitePhone);
        if (!whitePhone) {
            stringRedisTemplate.delete(redisKey);
        }
    }

    @Override
    public void checkPwd(String phone, String pwd, ExtendData extendData) {
        // 校验次数是否超限
        if (!phone.equals("18396893161")) {
            lockUtil.checkLock("checkPwd", phone);
        }
        User userOne = userMapper.selectOne(Wrappers.<User>lambdaQuery()
                .eq(User::getUserPhone, phone)
                .eq(User::getEnable, 1)
                .last("limit 1"));
        Assert.notNull(userOne, phone + "用户不存在");
        Assert.isTrue(userOne.getPwd().equals(SecureUtil.md5(pwd + userOne.getSalt())), "密码错误");
    }

    @Override
    public LoginResponse login(String phone, ExtendData extendData) {
        OsEnum os = extendData.getHeaderDto().getOs();
        User userOne = userMapper.selectOne(Wrappers.<User>lambdaQuery()
                .eq(User::getUserPhone, phone)
                .eq(User::getDel, 0)
                .last("limit 1"));
        if (ObjectUtil.isEmpty(userOne)) {
            //登陆时不存在则默认创建用户
            RegisterRequest registerRequest = new RegisterRequest();
            registerRequest.setPhone(phone);
            registerRequest.setPwd(RandomUtil.randomString(8));
            userService.createUser(registerRequest, extendData);
            userOne = userMapper.selectOne(Wrappers.<User>lambdaQuery()
                    .eq(User::getUserPhone, phone)
                    .last("limit 1"));
        }
        Assert.notNull(userOne, "登录或注册异常");
        Assert.isFalse(null != userOne && userOne.getEnable() == 0, "登录或注册异常");
        Long userId = userOne.getId();
        LoginResponse loginResponse = new LoginResponse();
        UserResponse userResponse = userService.baseUserInfo(userId);
        String token = tokenUtil.createToken(userId, BusinessTypeEnum.earnify_customer, extendData.getHeaderDto().getOsType().getCode(), os.getServerType(),
                userService.loadAuthentication(userId, null, userResponse, extendData), 30);
        loginResponse.setToken(token);
        loginResponse.setUserDetail(userResponse);
        loginResponse.setIsNewUser(userResponse.getLastLoginTime() == null ? 1 : 0);
        userFacade.login(LoginDeviceReq.builder()
                .clientUserId(userId)
                .loginDate(new Date())
                .build(), ExtendCovert.covert(extendData));
        userService.updateLastLoginTime(userResponse.getId());
        saveUserRedisCache(userId, userResponse);
        return loginResponse;
    }

    private void saveUserRedisCache(Long userId, UserResponse userResponse) {
        redisTemplate.opsForValue().set(String.format(CacheConstant.CUSTOMER_USER_INFO_CACHE_KEY, userId), JSONObject.toJSONString(userResponse), 30, TimeUnit.DAYS);
    }

    @Override
    public LoginResponse login(Long userId, ExtendData extendData) {
        OsEnum os = extendData.getHeaderDto().getOs();
        LoginResponse loginResponse = new LoginResponse();
        UserResponse userResponse = userService.baseUserInfo(userId);
        //游客登录，设置VIP状态
        userResponse.setLoginType(0);
        String token = tokenUtil.createToken(userId, BusinessTypeEnum.earnify_customer, extendData.getHeaderDto().getOsType().getCode(), os.getServerType(),
                userService.loadAuthentication(userId, null, userResponse, extendData), 30);
        loginResponse.setToken(token);
        loginResponse.setUserDetail(userResponse);
        loginResponse.setIsNewUser(userResponse.getLastLoginTime() == null ? 1 : 0);
        userFacade.login(LoginDeviceReq.builder()
                .clientUserId(userId)
                .loginDate(new Date())
                .build(), ExtendCovert.covert(extendData));
        //更新最后登录时间
        userService.updateLastLoginTime(userId);
        saveUserRedisCache(userId, userResponse);
        return loginResponse;
    }

    @Override
    public Long checkPhone(String phone) {
        Assert.isTrue(StrUtil.isNotEmpty(phone), "手机号不可为空");
        User user = userMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getUserPhone, phone).eq(User::getDel, 0).last("limit 1"));
        return null == user ? null : user.getId();
    }

    @Override
    public Boolean bindPhone(LoginRequest request, ExtendData extendData) {
        User user = userMapper.selectOne(Wrappers.<User>lambdaQuery().eq(User::getUserPhone, request.getPhone()).eq(User::getDel, 0)
                .last("limit 1"));
        if (ObjectUtil.isEmpty(user)) {
            user = userMapper.selectById(extendData.getUserId());
            Assert.isTrue(StrUtil.isEmpty(user.getUserPhone()), LanguageUtil.trans(I18nConstants.PHONE_ALREADY_BOUND_CANNOT_SWITCH));
        }
        boolean res = userMapper.update(Wrappers.
                <User>lambdaUpdate()
                .set(User::getUserPhone, request.getPhone())
                .set(User::getStatus, UserStatusEnum.common)
                .eq(User::getId, extendData.getUserId())
        ) > 0;
        if (res) {
            this.refreshToken(extendData);
        }
        userFacade.bindMobile(UserBindMobileReq.builder()
                .clientUserId(user.getId())
                .phone(request.getPhone())
                .build(), ExtendCovert.covert(extendData));
        return res;
    }

    @Override
    public void refreshToken(ExtendData extendData) {
        UserResponse userResponse = userService.baseUserInfo(extendData.getUserId());
        String openId = null;
        tokenUtil.refreshToken(extendData.getHeaderDto().getToken(), userService.loadAuthentication(extendData.getUserId(), openId, userResponse, extendData));
    }

    @Override
    public Long register(RegisterRequest request, ExtendData extendData) {
        User userOne = userMapper.selectOne(Wrappers.<User>lambdaQuery()
                .eq(User::getUserPhone, request.getPhone())
                .eq(User::getEnable, 1)
                .last("limit 1"));
        Assert.isNull(userOne, LanguageUtil.trans(I18nConstants.USER_ALREADY_REGISTERED_OR_CODE_ERROR));
        return userService.createUser(request, extendData);
    }

    @Override
    public Long register(EmptyRequest request, ExtendData extendData) {
        User oldUser = userMapper.selectOne(Wrappers.<User>lambdaQuery()
                .eq(User::getDeviceId, extendData.getHeaderDto().getDeviceId())
                .eq(User::getUserPhone, StrUtil.EMPTY)
                .eq(User::getDel, 0)
                .last("limit 1"));
        if (null != oldUser) {
            return oldUser.getId();
        }
        String salt = RandomUtil.randomString(4);
        Date now = new Date();
        User user = User.builder()
                .salt(salt)
                .nickName(NickNameUtils.getNickName())
                .userImage(registerConfiguration.getUserImgUrls().get(0))
                .deviceId(extendData.getHeaderDto().getDeviceId())
                .day(TimeUtil.today(now))
                .status(UserStatusEnum.tourist)
                .build();
        log.info("createUser saveUser:{} ", JSONObject.toJSONString(user));
        Collections.shuffle(registerConfiguration.getUserImgUrls());
        transactionalManage.execute(() -> {
            boolean res = userMapper.insert(user) > 0;
            Assert.isTrue(res, "新增用户失败，请稍后重试");
            noticeMiddle(user, extendData, now);
            return true;
        });
        return user.getId();
    }

    @Override
    public void certRegister(CertRegisterRequest request, ExtendData extendData) throws Exception {
//        User userOne = userMapper.selectOne(Wrappers.<User>lambdaQuery()
//                .eq(User::getUserPhone, request.getPhone())
//                .eq(User::getEnable, 1)
//                .last("limit 1"));
//        Assert.isNull(userOne, "登录或注册异常");
//        Assert.isTrue(IdcardUtil.isValidCard(request.getIdCard()), "身份证号不合法");
//        Assert.isTrue(lockUtil.lock(LockUtil.LockKey.thirdCert, request.getPhone()), "单日实名认证次数不可超过5次");
//        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(String.format(CacheConstant.certKey, request.getIdCard()), "1", 2L, TimeUnit.SECONDS);
//        Assert.isTrue(null != b && b, "身份实名频率异常，请稍后重试");
//        UserDetail oldCert = userDetailMapper.selectOne(Wrappers.<UserDetail>lambdaQuery().eq(UserDetail::getIdCard, request.getIdCard()).last("limit 1"));
//        Assert.isNull(oldCert, "一个身份证只能注册一个用户");
//        Pair<Boolean, SexEnum> pair = certThreeUtil.threeCert(request.getIdCard(), request.getPhone(), request.getName());
//        Assert.isTrue(pair.getKey(), "审核未通过，请保证姓名/身份证/手机号为统一状态");
//        try {
//            if (StrUtil.isNotEmpty(request.getInviteCode())) {
//                Long inviteUserId = CodeUtil.inviteCodeToUserId(request.getInviteCode());
//                User user = userMapper.selectById(inviteUserId);
//                Assert.notNull(user, "邀请人不存在");
//            }
//        } catch (Exception e) {
//            throw new ThrowException("#邀请码不合法");
//        }
//        RegisterRequest registerRequest = BeanUtil.copyProperties(request, RegisterRequest.class);
//        Collections.shuffle(registerConfiguration.getUserImgUrls());
//        boolean res = transactionalManage.executeWithException(() -> {
//            Long userId = userService.createUser(registerRequest, extendData);
//            if (StrUtil.isNotEmpty(request.getInviteCode())) {
//                userService.updateInvite(UserUpdateInviteReq.builder().inviteCode(request.getInviteCode()).build(), userId);
//            }
//            UserDetail build = UserDetail.builder()
//                    .id(userId)
//                    .imgUrl(registerConfiguration.getUserImgUrls().get(0))
//                    .realName(request.getName())
//                    .idCard(request.getIdCard())
//                    .sex(pair.getValue())
//                    .build();
//
//            return userDetailMapper.updateById(build) > 0;
//        });
//        Assert.isTrue(res, "重复注册，请重新确认身份信息");
    }

    @Override
    public void loginOut(ExtendData extendData) {
        // 删除token
        tokenUtil.removeToken(extendData.getHeaderDto().getToken());
    }

    private void noticeMiddle(User user, ExtendData extendData, Date now) {
        if (StrUtil.isNotEmpty(user.getPwd())) {
            userFacade.registerPwd(UserRegisterPwdReq.builder()
                            .clientUserId(user.getId())
                            .phone(user.getUserPhone())
                            .pwd(user.getPwd())
                            .salt(user.getSalt())
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .registerDate(now)
                            .build()
                    , ExtendCovert.covert(extendData));
        } else if (StrUtil.isNotEmpty(user.getUserPhone())) {
            userFacade.registerMobile(UserRegisterMobileReq.builder()
                            .clientUserId(user.getId())
                            .phone(user.getUserPhone())
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .registerDate(now)
                            .build()
                    , ExtendCovert.covert(extendData));
        } else {
            userFacade.registerDeviceId(UserRegisterDeviceIdReq.builder()
                            .deviceId(user.getDeviceId())
                            .clientUserId(user.getId())
                            .registerDate(now)
                            .codeChannel(ObjectUtil.isEmpty(extendData.getHeaderDto().getCodeChannel()) ? "N_DEFAULT_01" : extendData.getHeaderDto().getCodeChannel())
                            .build()
                    , ExtendCovert.covert(extendData));
        }
    }

}
