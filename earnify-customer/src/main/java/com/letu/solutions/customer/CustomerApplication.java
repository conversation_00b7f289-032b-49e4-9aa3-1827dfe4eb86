package com.letu.solutions.customer;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 *用户模块启动类
 * @date 2020/8/26
 * @version 1.0
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "com.letu")
@EnableDiscoveryClient
@EnableDubbo
public class CustomerApplication {

    public static void main(String[] args) {
        SpringApplication.run(CustomerApplication.class, args);
    }
}

