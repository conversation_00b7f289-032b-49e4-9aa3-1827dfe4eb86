package com.letu.solutions.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.letu.solutions.customer.service.FinanceConfigService;
import com.letu.solutions.customer.mapper.PlatformWalletAddressMapper;
import com.letu.solutions.customer.mapper.FinanceConfigMapper;
import com.letu.solutions.model.customer.response.FinanceConfigDetailRes;
import com.letu.solutions.model.entity.cms.FinanceConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 财务配置服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceConfigServiceImpl implements FinanceConfigService {

    private final PlatformWalletAddressMapper platformWalletAddressMapper;
    private final FinanceConfigMapper financeConfigMapper;

    @Override
    public FinanceConfigDetailRes selectByNetwork(String network) {
        log.info("根据协议网络查询财务配置详情，network：{}", network);

        // 查询财务配置
        LambdaQueryWrapper<FinanceConfig> query = Wrappers.<FinanceConfig>lambdaQuery()
                .eq(FinanceConfig::getNetwork, network);
        FinanceConfig record = financeConfigMapper.selectOne(query);

        if (record == null) {
            log.warn("未找到协议网络的财务配置，network：{}", network);
            return null;
        }

        // 转换为响应对象
        FinanceConfigDetailRes res = BeanUtil.copyProperties(record, FinanceConfigDetailRes.class);

        // 查询钱包地址列表
        List<String> addressList = getAddressesByNetwork(network);
        res.setAddressList(addressList);

        log.info("查询财务配置详情完成，network：{}，地址数量：{}", network, addressList.size());
        return res;
    }

    @Override
    public List<String> getAddressesByNetwork(String network) {
        log.info("查询协议网络钱包地址列表，network：{}", network);
        return platformWalletAddressMapper.getAddressesByNetwork(network);
    }
}
