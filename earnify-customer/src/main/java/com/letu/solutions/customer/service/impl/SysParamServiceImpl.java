package com.letu.solutions.customer.service.impl;

import com.letu.solutions.customer.mapper.SysParamMapper;
import com.letu.solutions.customer.service.SysParamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 系统参数服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysParamServiceImpl implements SysParamService {

    private final SysParamMapper sysParamMapper;

    @Override
    public String getValueByKey(String key) {
        return sysParamMapper.selectValueByKey(key);
    }

    @Override
    public String getValueByKey(String key, String defaultValue) {
        String value = getValueByKey(key);
        return value != null ? value : defaultValue;
    }

    @Override
    public Integer getIntValueByKey(String key, Integer defaultValue) {
        String value = getValueByKey(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            log.warn("系统参数{}的值{}无法转换为整数，使用默认值{}", key, value, defaultValue);
            return defaultValue;
        }
    }

    @Override
    public Long getLongValueByKey(String key, Long defaultValue) {
        String value = getValueByKey(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            log.warn("系统参数{}的值{}无法转换为长整数，使用默认值{}", key, value, defaultValue);
            return defaultValue;
        }
    }
} 