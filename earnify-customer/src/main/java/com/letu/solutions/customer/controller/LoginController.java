package com.letu.solutions.customer.controller;

import com.letu.solutions.core.model.ExtendData;
import com.letu.solutions.core.model.R;
import com.letu.solutions.core.transaction.TransactionalManage;
import com.letu.solutions.customer.service.EmailService;
import com.letu.solutions.customer.service.LoginService;
import com.letu.solutions.dubbo.middle.customer.ThirdLoginFacade;
import com.letu.solutions.model.request.login.EmailLoginRequest;
import com.letu.solutions.model.request.login.LoginRequest;
import com.letu.solutions.model.request.login.SendEmailReq;
import com.letu.solutions.model.response.login.LoginRes;
import com.letu.solutions.share.model.enums.MessageEnum;
import com.letu.solutions.core.constant.I18nConstants;
import com.letu.solutions.core.utils.LanguageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录/注册
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class LoginController {
    private final LoginService loginService;
    private final RedisTemplate<String, String> stringRedisTemplate;
    @DubboReference
    private ThirdLoginFacade thirdLoginFacade;
    private final TransactionalManage transactionalManage;
    private final EmailService emailService;
//
//    /**
//     * c端验证码发送
//     *
//     * @param phone        手机号
//     * @param businessType 业务类型
//     */
//    @GetMapping("/login/sms.e")
//    public R<Void> sms(@Pattern(regexp = "^1[0-9]{10}$", message = "$手机号不合法") @RequestParam("phone") String phone,
//                       @RequestParam(value = "businessType", defaultValue = "CUS_SMS_CODE_CER", required = false) MessageEnum businessType,
//                       @RequestHeader ExtendData extendData) {
//        loginService.sms(phone, businessType, extendData);
//        return R.success();
//    }
//
//    /**
//     * 校验用户验证码 并生成10分钟可修改令牌
//     *
//     * @return 令牌
//     */
//    @PostMapping("checkCert.e")
//    public R<String> checkCert(@Validated @RequestBody CheckCertReq body, @RequestHeader ExtendData extendData) {
//        String phone = body.getPhone();
//        Assert.isTrue(body.getBusinessType() != MessageEnum.MSG_UPDATE_PASSWORD || StrUtil.isNotEmpty(body.getPhone()), "手机号不可为空");
//        if (body.getBusinessType() != MessageEnum.MSG_UPDATE_PASSWORD) {
//            Assert.notNull(extendData.getUserId(), "登录后才可操作");
//            phone = extendData.getAuthentication().getUserInfo().getUserPhone();
//        }
//        loginService.checkSms(phone, body.getSmsCode(), body.getBusinessType(), extendData);
//        String uuid = UUID.randomUUID().toString(true);
//        String smsToken = String.format(CacheConstant.smsCheckKey, body.getBusinessType().name(), phone);
//        stringRedisTemplate.opsForValue().set(smsToken, uuid, 10, TimeUnit.MINUTES);
//        return R.success(uuid);
//    }
//
//    /**
//     * c端用户手机号登录
//     */
//    @PostMapping("/login/login")
//    public R<LoginResponse> login(@Validated @RequestBody LoginRequest request, @RequestHeader ExtendData extendData) {
//        loginService.checkSms(request.getPhone(), request.getSmsCode(), MessageEnum.CUS_SMS_CODE_CER, extendData);
//        Long userId = loginService.checkPhone(request.getPhone());
//        if (null == userId) {
//            loginService.bindPhone(request, extendData);
//        }
//        return R.success(loginService.login(request.getPhone(), extendData));
//    }
//
////    /**
////     * c端用户密码登录
////     */
////    @PostMapping("/login/pwd.e")
////    public R<LoginResponse> pwd(@Validated @RequestBody PwdLoginRequest request, @RequestHeader ExtendData extendData) {
////        loginService.checkPwd(request.getPhone(), request.getPassword(), extendData);
////        LoginResponse loginResponse = loginService.login(request.getPhone(), extendData);
////        return R.success(loginResponse);
////    }
//
////    /**
////     * c端用户手机号注册
////     */
////    @PostMapping("/login/register.e")
////    public R<LoginResponse> register(@Validated @RequestBody RegisterRequest request, @RequestHeader ExtendData extendData) throws Exception {
////        // 校验用户是否已经注册
////        loginService.checkSms(request.getPhone(), request.getSmsCode(), MessageEnum.CUS_SMS_CODE_REG, extendData);
////        Long userId = loginService.register(request, extendData);
////        if (StrUtil.isNotEmpty(request.getInviteCode())) {
////            try {
////                Long inviteUserId = CodeUtil.inviteCodeToUserId(request.getInviteCode());
////                //userService.updateInvite(UserUpdateInviteReq.builder().inviteCode(request.getInviteCode()).build(), userId);
////            } catch (Exception ignored) {
////            }
////        }
////        LoginResponse loginResponse = loginService.login(request.getPhone(), extendData);
////        return R.success(loginResponse);
////    }
//
//    /**
//     * 游客注册/登录
//     */
//    @PostMapping("/login/registerDevice.e")
//    public R<LoginResponse> registerDevice(@RequestBody EmptyRequest request, @RequestHeader ExtendData extendData) throws Exception {
//        // 校验用户是否已经注册
//        Long userId = loginService.register(request, extendData);
//        LoginResponse loginResponse = loginService.login(userId, extendData);
//        return R.success(loginResponse);
//    }
//
////    /**
////     * 游客绑定手机号
////     */
////    @PostMapping("/login/bindPhone")
////    public R<Boolean> bindPhone(@Validated @RequestBody LoginRequest request, @RequestHeader ExtendData extendData) throws Exception {
////        // 校验用户是否已经注册
////        loginService.checkSms(request.getPhone(), request.getSmsCode(), MessageEnum.MSG_BIND_PHONE, extendData);
////        return R.success(loginService.bindPhone(request, extendData));
////    }
//
////    /**
////     * c端用户实名手机号注册
////     */
////    @PostMapping("/login/certRegister.e")
////    public R<LoginResponse> certRegister(@Validated @RequestBody CertRegisterRequest request, @RequestHeader ExtendData extendData) throws Exception {
////        loginService.checkSms(request.getPhone(), request.getSmsCode(), MessageEnum.CUS_SMS_CODE_REG, extendData);
////        loginService.certRegister(request, extendData);
////        LoginResponse loginResponse = loginService.login(request.getPhone(), extendData);
////        return R.success(loginResponse);
////    }
//
//    /**
//     * c端用户退出登录
//     */
//    @PostMapping("/login/out")
//    public R<String> loginOut(@RequestHeader ExtendData extendData) {
//        loginService.loginOut(extendData);
//        return R.success();
//    }
//    /**
//     * 手机号一键登入
//     */
//    @PostMapping("/login/oneClickLogin.e")
//    public R<LoginResponse> oneClickLogin(@Validated @RequestBody OneClickLoginReq request, @RequestHeader ExtendData extendData) throws Exception {
//        if (StringUtils.isEmpty(extendData.getHeaderDto().getDeviceId())) {
//            return R.fail("设备id不能为空");
//        }
//        //获取手机号
//        String phone = "";
//        if (extendData.getHeaderDto().getOs() == OsEnum.h5) {
//            phone = thirdLoginFacade.aliH5OneKeyLogin(request.getToken());
//        } else {
//            phone = thirdLoginFacade.aliAppOneKeyLogin(request.getToken());
//        }
//        if (StringUtils.isEmpty(phone)) {
//            return R.fail("token错误");
//        }
//        Long userId = loginService.checkPhone(phone);
//        if (null == userId) {
//            LoginRequest loginRequest = new LoginRequest();
//            loginRequest.setPhone(phone);
//            loginService.bindPhone(loginRequest, extendData);
//        }
//        return R.success(loginService.login(phone, extendData));
//    }

    /**
     * 发送email验证码
     */
    @PostMapping("/login/email.e")
    public R<Void> email(@Validated @RequestBody SendEmailReq body, @RequestHeader ExtendData extendData) {
        emailService.emailCode(body.getEmail(),extendData.getIpAddress());
        return R.success();
    }

    /**
     * 邮箱登录
     */
    @PostMapping("/login/emailLogin.e")
    public R<LoginRes> emailLogin(@Validated @RequestBody EmailLoginRequest request, @RequestHeader ExtendData extendData) {
        emailService.checkEmailCode(request.getEmail(), request.getEmailCode());
        LoginRes loginRes = emailService.emailLogin(request, extendData);
        return R.success(loginRes);
    }

    /**
     * 发送手机验证码
     * 
     * @param phone 手机号码，用于接收验证码
     * @param extendData 扩展数据，包含用户信息和IP地址等
     * @return 发送结果，成功返回R.success()，失败抛出异常
     * 
     * 防刷限制：
     * - IP地址限制：1分钟最多3次，10分钟最多10次，1天最多30次
     * - 手机号限制：1分钟最多3次，10分钟最多10次，1天最多30次
     * - 白名单IP和手机号不受限制
     * - 验证码有效期5分钟
     */
    @PostMapping("/login/sendSmsCode")
    public R<Void> sendSmsCode(@RequestParam String phone, @RequestHeader ExtendData extendData) {
        try {
            // 调用LoginService发送绑定手机号专用的短信验证码
            // 使用MessageEnum.MSG_BIND_PHONE业务类型，会进行防刷限制
            loginService.sms(phone, MessageEnum.MSG_BIND_PHONE, extendData);
            return R.success();
        } catch (Exception e) {
            log.error("发送手机验证码失败，手机号：{}，错误：{}", phone, e.getMessage());
            return R.fail(LanguageUtil.trans(I18nConstants.SMS_CODE_SEND_FAILED));
        }
    }

    /**
     * 绑定手机号码
     * 
     * @param request 绑定请求，包含手机号和验证码
     * @param extendData 扩展数据，包含用户信息
     * @return 绑定结果，成功返回R.success(true)，失败返回R.success(false)
     * 
     * 绑定流程：
     * 1. 验证短信验证码是否正确
     * 2. 检查手机号是否已被其他用户绑定
     * 3. 检查当前用户是否已绑定手机号
     * 4. 执行绑定操作，更新用户手机号字段
     * 5. 更新用户状态为普通用户(common)
     * 6. 刷新用户登录token
     * 7. 同步到中间件系统
     * 
     * 安全特性：
     * - 防重复绑定：已绑定手机号的用户不能再次绑定
     * - 防冲突绑定：一个手机号只能绑定一个用户
     * - 验证码验证：必须通过短信验证码验证
     * - 事务安全：绑定操作在事务中执行
     */
    @PostMapping("/login/bindPhone")
    public R<Boolean> bindPhone(@Validated @RequestBody LoginRequest request, @RequestHeader ExtendData extendData) {
        try {
            // 第一步：验证短信验证码
            // 验证码错误会抛出异常，连续错误5次会要求重新获取验证码
            loginService.checkSms(request.getPhone(), request.getSmsCode(), MessageEnum.MSG_BIND_PHONE, extendData);
            
            // 第二步：执行手机号绑定
            // 包含手机号冲突检查、用户状态检查、数据库更新、token刷新等操作
            Boolean result = loginService.bindPhone(request, extendData);
            
            if (result) {
                return R.success(true);
            } else {
                return R.fail(LanguageUtil.trans(I18nConstants.PHONE_BIND_FAILED));
            }
        } catch (Exception e) {
            log.error("绑定手机号失败，手机号：{}，错误：{}", request.getPhone(), e.getMessage());
            return R.fail(e.getMessage());
        }
    }

}
