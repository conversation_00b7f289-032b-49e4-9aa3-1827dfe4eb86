package com.letu.solutions.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.letu.solutions.model.entity.sys.SysParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 系统参数 Mapper
 */
@Mapper
public interface SysParamMapper extends BaseMapper<SysParam> {

    /**
     * 根据key查询参数值
     * @param key 参数key
     * @return 参数值
     */
    @Select("SELECT value FROM sys_param WHERE `key` = #{key}")
    String selectValueByKey(@Param("key") String key);
} 