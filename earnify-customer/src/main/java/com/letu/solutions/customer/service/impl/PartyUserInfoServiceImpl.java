package com.letu.solutions.customer.service.impl;

import com.letu.solutions.customer.mapper.ProductMapper;
import com.letu.solutions.customer.mapper.TaskMapper;
import com.letu.solutions.customer.mapper.UserMapper;
import com.letu.solutions.customer.mapper.UserSocialMapper;
import com.letu.solutions.model.entity.cms.Product;
import com.letu.solutions.model.entity.cms.Task;
import com.letu.solutions.model.entity.cms.UserSocial;
import com.letu.solutions.customer.service.PartyUserInfoService;
import com.letu.solutions.model.entity.user.User;
import com.letu.solutions.model.response.customer.PartyUserInfoRes;
import com.letu.solutions.model.response.customer.UserSocialRes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import java.util.ArrayList;
import java.util.List;

/**
 * 甲方信息查询服务
 */
@Service
public class PartyUserInfoServiceImpl implements PartyUserInfoService {
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserSocialMapper userSocialMapper;
    @Autowired
    private  TaskMapper taskMapper;

    /**
     * 根据 taskId 查询甲方信息
     */
    @Override
    public PartyUserInfoRes getPartyUserInfoByTaskId(Long taskId) {
        // 1. 查 task
        Task task = null;
        try {
            task =taskMapper.selectById(taskId);
        } catch (Exception e) {}
        if (task == null) return null;
        // 2. 查 product
        Product product = productMapper.selectById(task.getProductId());
        if (product == null) return null;
        // 3. 查甲方用户
        User partyUser = userMapper.selectById(product.getPartyUserId());
        if (partyUser == null) return null;
        // 4. 查甲方社交
        List<UserSocial> socialList = userSocialMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<UserSocial>().eq(UserSocial::getUserId, product.getPartyUserId())
        );
        List<UserSocialRes> socialResList = new ArrayList<>();
        for (UserSocial social : socialList) {
            UserSocialRes res = new UserSocialRes();
            BeanUtil.copyProperties(social, res);
            socialResList.add(res);
        }
        // 5. 封装 PartyUserInfoRes
        PartyUserInfoRes partyUserInfo = new PartyUserInfoRes();
        partyUserInfo.setUserId(partyUser.getId());
        partyUserInfo.setName(partyUser.getNickName());
        partyUserInfo.setAvatar(partyUser.getUserImage());
//        partyUserInfo.setIntro(partyUser.getIntro());
        partyUserInfo.setSocialList(socialResList);
        return partyUserInfo;
    }
} 