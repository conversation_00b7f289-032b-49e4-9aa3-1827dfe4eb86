/**
 * 邀请记录表
 **/
CREATE TABLE invite_record
(
    id                  BIGINT(20) PRIMARY KEY AUTO_INCREMENT COMMENT '邀请记录唯一标识符，自增主键',
    inviter_id          BIGINT(20) NOT NULL COMMENT '邀请人用户ID',
    invitee_id          BIGINT(20) NOT NULL COMMENT '被邀请人用户ID',
    invite_code         VARCHAR(20) NOT NULL COMMENT '邀请码',
    status              VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '邀请状态：pending-待注册，registered-已注册，expired-已过期',
    reward_amount       DECIMAL(10,2) DEFAULT 0.00 COMMENT '邀请奖励金额',
    reward_status       VARCHAR(20) DEFAULT 'pending' COMMENT '奖励状态：pending-待发放，granted-已发放，failed-发放失败',
    register_time       DATETIME NULL COMMENT '被邀请人注册时间',
    reward_time         DATETIME NULL COMMENT '奖励发放时间',
    expire_time         DATETIME NOT NULL COMMENT '邀请过期时间',
    remark              VARCHAR(255) COMMENT '备注信息',
    del_flag            TINYINT(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
    create_time         DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_inviter_id (inviter_id) COMMENT '邀请人ID索引',
    INDEX idx_invitee_id (invitee_id) COMMENT '被邀请人ID索引',
    INDEX idx_invite_code (invite_code) COMMENT '邀请码索引',
    INDEX idx_status (status) COMMENT '状态索引',
    INDEX idx_create_time (create_time) COMMENT '创建时间索引',
    UNIQUE KEY uk_invitee_id (invitee_id) COMMENT '确保每个被邀请人只有一条邀请记录'
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '邀请记录表'
  ROW_FORMAT = DYNAMIC;

-- 系统参数初始化脚本
-- 审核倒计时天数
INSERT INTO `sys_param` (`name`, `key`, `value`, `describe`) VALUES
    ('审核倒计时天数', 'audit_countdown_days', '3', '任务审核倒计时天数，从审核时间开始计算');

-- 申诉倒计时天数
INSERT INTO `sys_param` (`name`, `key`, `value`, `describe`) VALUES
    ('申诉倒计时天数', 'appeal_countdown_days', '3', '任务申诉倒计时天数，从申诉时间开始计算');

-- 申诉有效期小时数
INSERT INTO `sys_param` (`name`, `key`, `value`, `describe`) VALUES
    ('申诉有效期小时数', 'appeal_valid_hours', '24', '申诉有效期，从申诉发起时间开始计算');
-- 邀请奖励金额
INSERT INTO `sys_param` (`name`, `key`, `value`, `describe`, create_time, update_time) VALUES
    ('邀请奖励金额', 'invite_reward_amount', '0.00', '用户邀请新用户注册成功后获得的奖励金额', NOW(), NOW());

-- 邀请有效期天数
INSERT INTO `sys_param` (`name`, `key`, `value`, `describe`, create_time, update_time) VALUES
    ('邀请有效期天数', 'invite_valid_days', '7', '邀请记录的有效期天数', NOW(), NOW());

/**
 * 用户社交媒体绑定表（灵活设计）
 **/
CREATE TABLE user_social
(
    id                  BIGINT(20) PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id             BIGINT(20) NOT NULL COMMENT '用户ID，关联user表',
    social_type         VARCHAR(32) NOT NULL COMMENT '社交媒体类型：twitter,telegram,discord,reddit,tiktok,medium等',
    social_url          VARCHAR(256) NOT NULL COMMENT '社交媒体链接',
    status              VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '绑定状态：pending-待验证，verified-已验证，failed-验证失败',
    verify_time         DATETIME NULL COMMENT '验证时间',
    create_time         DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_social_type (social_type) COMMENT '社交媒体类型索引',
    INDEX idx_status (status) COMMENT '绑定状态索引',
    UNIQUE KEY uk_user_social (user_id, social_type) COMMENT '用户和社交媒体类型唯一索引，确保每个用户每种类型只有一条记录'
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户-社交媒体绑定信息表'
  ROW_FORMAT = DYNAMIC;

/**
 * 用户钱包绑定表
 **/
CREATE TABLE user_wallet
(
    id                  BIGINT(20) PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id             BIGINT(20) NOT NULL COMMENT '用户ID，关联user表',
    wallet_type         VARCHAR(32) NOT NULL COMMENT '钱包类型：TRC20,ERC20,BEP20',
    wallet_address      VARCHAR(256) NOT NULL COMMENT '钱包地址',
    is_default          TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否默认钱包：0-否，1-是',
    create_time         DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_wallet_type (wallet_type) COMMENT '钱包类型索引',
    INDEX idx_is_default (is_default) COMMENT '默认钱包索引',
    UNIQUE KEY uk_user_wallet (user_id, wallet_type, wallet_address) COMMENT '用户钱包地址唯一索引，确保每个用户每种类型每个地址只有一条记录'
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户-钱包绑定信息表'
  ROW_FORMAT = DYNAMIC;

-- 为task_step表添加步骤类型字段
ALTER TABLE task_step ADD COLUMN step_type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '步骤类型：text-文本，image-图片' AFTER srot;

-- 为user_task_step表添加步骤类型字段
ALTER TABLE user_task_step ADD COLUMN step_type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '步骤类型：text-文本，image-图片' AFTER is_saved;

-- 为invite_record表添加顶级邀请人ID字段
ALTER TABLE invite_record ADD COLUMN root_inviter_id BIGINT(20) DEFAULT NULL COMMENT '顶级邀请人ID（一级邀请人）' AFTER inviter_id;
ALTER TABLE invite_record ADD INDEX idx_root_inviter_id (root_inviter_id) COMMENT '顶级邀请人ID索引';
