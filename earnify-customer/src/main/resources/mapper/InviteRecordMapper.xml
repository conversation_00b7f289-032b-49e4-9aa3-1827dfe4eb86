<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.customer.mapper.InviteRecordMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.letu.solutions.model.entity.cms.InviteRecord">
        <id column="id" property="id"/>
        <result column="inviter_id" property="inviterId"/>
        <result column="root_inviter_id" property="rootInviterId"/>
        <result column="invitee_id" property="inviteeId"/>
        <result column="invite_code" property="inviteCode"/>
        <result column="status" property="status"/>
        <result column="reward_amount" property="rewardAmount"/>
        <result column="reward_status" property="rewardStatus"/>
        <result column="register_time" property="registerTime"/>
        <result column="reward_time" property="rewardTime"/>
        <result column="expire_time" property="expireTime"/>
        <result column="remark" property="remark"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 邀请记录响应结果映射 -->
    <resultMap id="InviteRecordResMap" type="com.letu.solutions.model.response.customer.InviteRecordRes">
        <result column="invitee_nick_name" property="userName"/>
        <result column="invite_method" property="inviteMethod"/>
        <result column="register_time" property="registerTime"/>
        <result column="reward_amount" property="rewardPoints"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, inviter_id, root_inviter_id, invitee_id, invite_code, status, reward_amount, reward_status,
        register_time, reward_time, expire_time, remark, del_flag, create_time, update_time
    </sql>

    <!-- 分页查询邀请记录 -->
    <select id="selectInviteRecordPage" resultMap="InviteRecordResMap">
        SELECT 
            invitee.nick_name as invitee_nick_name,
            'invite_code' as invite_method,
            ir.register_time,
            ir.reward_amount
        FROM invite_record ir
        LEFT JOIN user invitee ON ir.invitee_id = invitee.id
        ORDER BY ir.create_time DESC
    </select>

    <!-- 根据邀请码查询邀请记录 -->
    <select id="selectByInviteCode" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM invite_record
        WHERE invite_code = #{inviteCode} AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 根据被邀请人ID查询邀请记录 -->
    <select id="selectByInviteeId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM invite_record
        WHERE invitee_id = #{inviteeId} AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 根据顶级邀请人ID查询所有下级用户数量 -->
    <select id="countByRootInviterId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM invite_record
        WHERE root_inviter_id = #{rootInviterId} AND del_flag = 0
    </select>



</mapper> 