<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.customer.mapper.UserTaskMapper">
    <resultMap id="UserTaskPageResMap" type="com.letu.solutions.model.response.customer.UserTaskPageRes">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="task_name" property="taskName"/>
        <result column="task_type" property="taskType"/>
        <result column="state" property="state"/>
        <result column="price" property="price"/>
        <result column="expire_time" property="expireTime"/>
        <result column="audit_countdown" property="auditCountdown"/>
        <result column="status_display" property="statusDisplay"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="appeal_time" property="appealTime"/>
        <!-- 甲方信息、步骤列表等可后续扩展 -->
    </resultMap>

    <select id="selectUserTaskPageResPage" resultMap="UserTaskPageResMap">
        SELECT
            ut.id, ut.task_id, t.name as task_name, t.task_type, ut.state, ut.price, ut.time as expire_time,
            TIMESTAMPDIFF(SECOND, ut.update_time, DATE_ADD(ut.update_time, INTERVAL 30 MINUTE)) as audit_countdown,
            null as status_display, ut.create_time, ut.update_time, ut.appeal_time
        FROM user_task ut
        LEFT JOIN task t ON ut.task_id = t.id
        <where>
            <if test="userId != null">
                AND ut.user_id = #{userId}
            </if>
            <if test="request.typeList != null and request.typeList.size() > 0">
                AND t.task_type IN
                <foreach collection="request.typeList" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                AND ut.state IN
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="request.orderBy == 'expireTime'">
                ORDER BY ut.time ${request.order}
            </when>
            <when test="request.orderBy == 'reward'">
                ORDER BY ut.price ${request.order}
            </when>
            <otherwise>
                ORDER BY ut.time ASC
            </otherwise>
        </choose>
    </select>

    <!-- 已完成用户任务响应映射 -->
    <resultMap id="CompletedUserTaskResponseMap" type="com.letu.solutions.model.response.user.CompletedUserTaskResponse">
        <result property="publisher" column="publisher"/>
        <result property="taskTitle" column="task_title"/>
        <result property="completedTime" column="completed_time"/>
        <result property="rewardPoints" column="reward_points"/>
    </resultMap>

    <!-- 分页查询已完成且发布者为甲方账户的用户任务 -->
    <select id="selectCompletedUserTaskPage" resultMap="CompletedUserTaskResponseMap">
        SELECT
            partyA.nick_name AS publisher,
            t.name AS task_title,
            ut.examine_time AS completed_time,
            t.price AS reward_points
        FROM user_task ut
        INNER JOIN task t ON ut.task_id = t.id
        LEFT JOIN `user` partyA ON t.user_id = partyA.id
        <where>
            <if test="req.stateList != null and req.stateList.size() > 0">
                AND ut.state IN
                <foreach collection="req.stateList" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
        </where>
        ORDER BY ut.examine_time DESC
    </select>
</mapper> 