<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.customer.mapper.TaskMapper">
    <resultMap id="baseResultMap" type="com.letu.solutions.model.entity.cms.Task" autoMapping="true">
        <id column="id" property="id"/>
        <result column="attribute" property="attribute"/>
        <result column="name" property="name"/>
        <result column="user_id" property="userId"/>
        <result column="product_id" property="productId"/>
        <result column="weight_sorting" property="weightSorting"/>
        <result column="task_type" property="taskType"/>
        <result column="number" property="number"/>
        <result column="price" property="price"/>
        <result column="time" property="time"/>
        <result column="state" property="state"/>
        <result column="step_number" property="stepNumber"/>
        <result column="url" property="url"/>
        <result column="bond" property="bond"/>
        <result column="task_finish_num" property="taskFinishNum"/>
        <result column="task_receive_num" property="taskReceiveNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, attribute, name, user_id, product_id, weight_sorting, task_type, number, price, time, state, step_number, url, bond, task_finish_num, task_receive_num, create_time, update_time
    </sql>

    <resultMap id="TaskPageResMap" type="com.letu.solutions.model.response.customer.TaskPageRes">
        <id column="id" property="id"/>
        <result column="attribute" property="attribute"/>
        <result column="name" property="name"/>
        <result column="user_id" property="userId"/>
        <result column="product_id" property="productId"/>
        <result column="weight_sorting" property="weightSorting"/>
        <result column="task_type" property="taskType"/>
        <result column="number" property="number"/>
        <result column="price" property="price"/>
        <result column="time" property="time"/>
        <result column="state" property="state"/>
        <result column="step_number" property="stepNumber"/>
        <result column="url" property="url"/>
        <result column="bond" property="bond"/>
        <result column="task_finish_num" property="taskFinishNum"/>
        <result column="task_receive_num" property="taskReceiveNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="selectTaskPageResPage" resultMap="TaskPageResMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM task
        <where>
            <if test="request.name != null and request.name != ''">
                AND name LIKE CONCAT('%', #{request.name}, '%')
            </if>
            <if test="request.time != null and request.time != ''">
                AND time = #{request.time}
            </if>
        </where>
        <choose>
            <when test="request.orderBy == 'create_time'">
                ORDER BY create_time ${request.order}
            </when>
            <when test="request.orderBy == 'reward'">
                ORDER BY price ${request.order}
            </when>
        </choose>
    </select>

    <update id="updateTaskReceiveNum">
        UPDATE task t
        SET t.task_receive_num = t.task_receive_num + 1
        WHERE t.id = #{taskId} AND t.task_receive_num &lt; t.number
    </update>
</mapper> 