<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.letu.solutions.customer.mapper.UserTaskStepMapper">
    <insert id="batchInsert">
        INSERT INTO user_task_step (user_task_id, task_id, user_id, task_describe, task_text_operate, task_image_operate, srot, is_saved, step_type, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userTaskId}, #{item.taskId}, #{item.userId}, #{item.taskDescribe}, #{item.taskTextOperate}, #{item.taskImageOperate}, #{item.srot}, #{item.isSaved}, #{item.stepType}, NOW(), NOW())
        </foreach>
    </insert>
</mapper> 