server:
  port: 11802
  servlet:
    context-path: /earnify-customer
dubbo:
  protocol:
    port: 21802
  cloud:
    subscribed-services: 'earnify-order,middle-collect,middle-customer,middle-cms'
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: earnify-customer
    type: customer
  cloud:
    nacos:
      server-addr: http://192.168.77.101:28848
      discovery:
        namespace: ${discoverNameSpace:server}
        username: earnify
        password: 6sqGL8nj4oIt5P8n
      config:
        file-extension: yml
        namespace: earnify
        username: earnify
        password: 6sqGL8nj4oIt5P8n
  config:
    import:
      - nacos:redis.yaml
      - nacos:mysql.yaml
      - nacos:mybatis.yaml
      - nacos:base.yaml
      - nacos:nos.yaml
      - nacos:actuator.yaml
      - nacos:dubbo${discoverDubboEnv:}.yaml?refresh=true
      - nacos:account.yaml?refresh=true
      - nacos:oss.yaml?refresh=true
      - nacos:refresh.yaml?refresh=true
      - nacos:check.yaml?refresh=true
      - nacos:third.yaml?refresh=true
      - nacos:message.yaml?refresh=true