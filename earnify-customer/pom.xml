<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>earnify-customer</artifactId>
    <version>0.0.2-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>earnify-customer</name>
    <description>C端用户服务</description>

    <parent>
        <groupId>com.letu.solutions</groupId>
        <artifactId>novel</artifactId>
        <version>0.0.2-SNAPSHOT</version>
    </parent>


    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--服务基础模块-->
        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-core</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-feign</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <!--公共模块-->
        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-model</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.letu.solutions</groupId>
            <artifactId>earnify-common-util</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client</artifactId>
            <version>1.32.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client-jackson2</artifactId>
            <version>1.32.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client-java6</artifactId>
            <version>1.32.1</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.3.0</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.letu.solutions.customer.CustomerApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>

        <!-- 定义包含这些资源文件，能在jar包中获取这些文件 -->
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                </includes>
                <!--是否替换资源中的属性-->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
</project>
